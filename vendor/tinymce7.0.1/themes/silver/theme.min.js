/**
 * TinyMCE version 7.0.1 (2024-04-10)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=e=>()=>e(),y=(e,t)=>(...o)=>e(t.apply(null,o)),x=e=>()=>e,w=e=>e,S=(e,t)=>e===t;function k(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const C=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},_=e=>e(),T=x(!1),E=x(!0);class A{constructor(e,t){this.tag=e,this.value=t}static some(e){return new A(!0,e)}static none(){return A.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?A.some(e(this.value)):A.none()}bind(e){return this.tag?e(this.value):A.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:A.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?A.some(e):A.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}A.singletonNone=new A(!1);const M=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,I=(e,t)=>D.call(e,t),F=(e,t)=>{const o=I(e,t);return-1===o?A.none():A.some(o)},R=(e,t)=>I(e,t)>-1,N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},L=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},z=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},V=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},H=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},P=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},U=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},W=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t,o)=>(H(e,((e,n)=>{o=t(o,e,n)})),o),G=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return A.some(s);if(o(s,n))break}return A.none()})(e,t,T),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return A.some(o);return A.none()},q=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},Y=(e,t)=>q(V(e,t)),X=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},K=e=>{const t=M.call(e,0);return t.reverse(),t},J=(e,t)=>U(e,(e=>!R(t,e))),Z=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],ee=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},te=(e,t)=>t>=0&&t<e.length?A.some(e[t]):A.none(),oe=e=>te(e,0),ne=e=>te(e,e.length-1),se=p(Array.from)?Array.from:e=>M.call(e),re=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return A.none()},ae=Object.keys,ie=Object.hasOwnProperty,le=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},ce=(e,t)=>de(e,((e,o)=>({k:o,v:t(e,o)}))),de=(e,t)=>{const o={};return le(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},ue=e=>(t,o)=>{e[o]=t},me=(e,t,o,n)=>{le(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},ge=(e,t)=>{const o={};return me(e,t,ue(o),b),o},pe=(e,t)=>{const o=[];return le(e,((e,n)=>{o.push(t(e,n))})),o},he=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return A.some(r)}return A.none()},fe=e=>pe(e,w),be=(e,t)=>ve(e,t)?A.from(e[t]):A.none(),ve=(e,t)=>ie.call(e,t),ye=(e,t)=>ve(e,t)&&void 0!==e[t]&&null!==e[t],xe=(e,t,o=S)=>e.exists((e=>o(e,t))),we=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},Se=(e,t,o)=>e.isSome()&&t.isSome()?A.some(o(e.getOrDie(),t.getOrDie())):A.none(),ke=(e,t)=>null!=e?A.some(t(e)):A.none(),Ce=(e,t)=>e?A.some(t):A.none(),Oe=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,_e=(e,t)=>Ee(e,t)?((e,t)=>e.substring(t))(e,t.length):e,Te=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},Ee=(e,t)=>Oe(e,t,0),Ae=(e,t)=>Oe(e,t,e.length-t.length),Me=(Do=/^\s+|\s+$/g,e=>e.replace(Do,"")),De=e=>e.length>0,Be=e=>!De(e),Ie=e=>void 0!==e.style&&p(e.style.getPropertyValue),Fe=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Re=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Fe(o.childNodes[0])},Ne=(e,t)=>{const o=(t||document).createElement(e);return Fe(o)},Le=(e,t)=>{const o=(t||document).createTextNode(e);return Fe(o)},ze=Fe,Ve="undefined"!=typeof window?window:Function("return this;")(),He=(e,t)=>((e,t)=>{let o=null!=t?t:Ve;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),Pe=Object.getPrototypeOf,Ue=e=>{const t=He("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>He(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Pe(e).constructor.name))},We=e=>e.dom.nodeName.toLowerCase(),je=e=>t=>(e=>e.dom.nodeType)(t)===e,Ge=e=>$e(e)&&Ue(e.dom),$e=je(1),qe=je(3),Ye=je(9),Xe=je(11),Ke=e=>t=>$e(t)&&We(t)===e,Je=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Ze=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Qe=(e,t)=>{const o=void 0===t?document:t.dom;return Ze(o)?A.none():A.from(o.querySelector(e)).map(ze)},et=(e,t)=>e.dom===t.dom,tt=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},ot=e=>ze(e.dom.ownerDocument),nt=e=>Ye(e)?e:ot(e),st=e=>ze(nt(e).dom.documentElement),rt=e=>ze(nt(e).dom.defaultView),at=e=>A.from(e.dom.parentNode).map(ze),it=e=>A.from(e.dom.parentElement).map(ze),lt=e=>A.from(e.dom.offsetParent).map(ze),ct=e=>V(e.dom.childNodes,ze),dt=(e,t)=>{const o=e.dom.childNodes;return A.from(o[t]).map(ze)},ut=e=>dt(e,0),mt=(e,t)=>({element:e,offset:t}),gt=(e,t)=>{const o=ct(e);return o.length>0&&t<o.length?mt(o[t],0):mt(e,t)},pt=e=>Xe(e)&&g(e.dom.host),ht=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),ft=x(ht),bt=ht?e=>ze(e.dom.getRootNode()):nt,vt=e=>pt(e)?e:ze(nt(e).dom.body),yt=e=>{const t=bt(e);return pt(t)?A.some(t):A.none()},xt=e=>ze(e.dom.host),wt=e=>{const t=qe(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return yt(ze(t)).fold((()=>o.body.contains(t)),(n=wt,s=xt,e=>n(s(e))));var n,s},St=()=>kt(ze(document)),kt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return ze(t)},Ct=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},Ot=(e,t,o)=>{Ct(e.dom,t,o)},_t=(e,t)=>{const o=e.dom;le(t,((e,t)=>{Ct(o,t,e)}))},Tt=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},Et=(e,t)=>A.from(Tt(e,t)),At=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},Mt=(e,t)=>{e.dom.removeAttribute(t)},Dt=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Ie(e)&&e.style.setProperty(t,o)},Bt=(e,t)=>{Ie(e)&&e.style.removeProperty(t)},It=(e,t,o)=>{const n=e.dom;Dt(n,t,o)},Ft=(e,t)=>{const o=e.dom;le(t,((e,t)=>{Dt(o,t,e)}))},Rt=(e,t)=>{const o=e.dom;le(t,((e,t)=>{e.fold((()=>{Bt(o,t)}),(e=>{Dt(o,t,e)}))}))},Nt=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||wt(e)?n:Lt(o,t)},Lt=(e,t)=>Ie(e)?e.style.getPropertyValue(t):"",zt=(e,t)=>{const o=e.dom,n=Lt(o,t);return A.from(n).filter((e=>e.length>0))},Vt=e=>{const t={},o=e.dom;if(Ie(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},Ht=(e,t,o)=>{const n=Ne(e);return It(n,t,o),zt(n,t).isSome()},Pt=(e,t)=>{const o=e.dom;Bt(o,t),xe(Et(e,"style").map(Me),"")&&Mt(e,"style")},Ut=e=>e.dom.offsetWidth,Wt=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Nt(o,e);return parseFloat(t)||0}return n},n=(e,t)=>j(t,((t,o)=>{const n=Nt(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Ie(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},jt=Wt("height",(e=>{const t=e.dom;return wt(e)?t.getBoundingClientRect().height:t.offsetHeight})),Gt=e=>jt.get(e),$t=e=>jt.getOuter(e),qt=(e,t)=>({left:e,top:t,translate:(o,n)=>qt(e+o,t+n)}),Yt=qt,Xt=(e,t)=>void 0!==e?e:void 0!==t?t:0,Kt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return Yt(o.offsetLeft,o.offsetTop);const r=Xt(null==n?void 0:n.pageYOffset,s.scrollTop),a=Xt(null==n?void 0:n.pageXOffset,s.scrollLeft),i=Xt(s.clientTop,o.clientTop),l=Xt(s.clientLeft,o.clientLeft);return Jt(e).translate(a-l,r-i)},Jt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?Yt(o.offsetLeft,o.offsetTop):wt(e)?(e=>{const t=e.getBoundingClientRect();return Yt(t.left,t.top)})(t):Yt(0,0)},Zt=Wt("width",(e=>e.dom.offsetWidth)),Qt=e=>Zt.get(e),eo=e=>Zt.getOuter(e),to=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},oo=()=>no(0,0),no=(e,t)=>({major:e,minor:t}),so={nu:no,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?oo():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return no(n(1),n(2))})(e,o)},unknown:oo},ro=(e,t)=>{const o=String(t).toLowerCase();return G(e,(e=>e.search(o)))},ao=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,io=e=>t=>Te(t,e),lo=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Te(e,"edge/")&&Te(e,"chrome")&&Te(e,"safari")&&Te(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,ao],search:e=>Te(e,"chrome")&&!Te(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Te(e,"msie")||Te(e,"trident")},{name:"Opera",versionRegexes:[ao,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:io("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:io("firefox")},{name:"Safari",versionRegexes:[ao,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Te(e,"safari")||Te(e,"mobile/"))&&Te(e,"applewebkit")}],co=[{name:"Windows",search:io("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Te(e,"iphone")||Te(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:io("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:io("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:io("linux"),versionRegexes:[]},{name:"Solaris",search:io("sunos"),versionRegexes:[]},{name:"FreeBSD",search:io("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:io("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],uo={browsers:x(lo),oses:x(co)},mo="Edge",go="Chromium",po="Opera",ho="Firefox",fo="Safari",bo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(mo),isChromium:n(go),isIE:n("IE"),isOpera:n(po),isFirefox:n(ho),isSafari:n(fo)}},vo=()=>bo({current:void 0,version:so.unknown()}),yo=bo,xo=(x(mo),x(go),x("IE"),x(po),x(ho),x(fo),"Windows"),wo="Android",So="Linux",ko="macOS",Co="Solaris",Oo="FreeBSD",_o="ChromeOS",To=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(xo),isiOS:n("iOS"),isAndroid:n(wo),isMacOS:n(ko),isLinux:n(So),isSolaris:n(Co),isFreeBSD:n(Oo),isChromeOS:n(_o)}},Eo=()=>To({current:void 0,version:so.unknown()}),Ao=To,Mo=(x(xo),x("iOS"),x(wo),x(So),x(ko),x(Co),x(Oo),x(_o),e=>window.matchMedia(e).matches);var Do;let Bo=to((()=>((e,t,o)=>{const n=uo.browsers(),s=uo.oses(),r=t.bind((e=>((e,t)=>re(t.brands,(t=>{const o=t.brand.toLowerCase();return G(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:so.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>ro(e,t).map((e=>{const o=so.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(vo,yo),a=((e,t)=>ro(e,t).map((e=>{const o=so.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(Eo,Ao),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:x(s),isiPhone:x(r),isTablet:x(l),isPhone:x(c),isTouch:x(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:x(d),isDesktop:x(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(navigator.userAgent,A.from(navigator.userAgentData),Mo)));const Io=()=>Bo(),Fo=e=>{const t=ze((e=>{if(ft()&&g(e.target)){const t=ze(e.target);if($e(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return oe(t)}}return A.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=y(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Ro=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(Fo(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:k(No,e,t,r,s)}},No=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Lo=(e,t)=>{at(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},zo=(e,t)=>{const o=(e=>A.from(e.dom.nextSibling).map(ze))(e);o.fold((()=>{at(e).each((e=>{Ho(e,t)}))}),(e=>{Lo(e,t)}))},Vo=(e,t)=>{ut(e).fold((()=>{Ho(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Ho=(e,t)=>{e.dom.appendChild(t.dom)},Po=(e,t)=>{H(t,(t=>{Ho(e,t)}))},Uo=e=>{e.dom.textContent="",H(ct(e),(e=>{Wo(e)}))},Wo=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},jo=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return Yt(o,n)},Go=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},$o=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),qo=e=>{const t=void 0===e?window:e,o=t.document,n=jo(ze(o));return(e=>{const t=void 0===e?window:e;return Io().browser.isFirefox()?A.none():A.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return $o(n.left,n.top,o,s)}),(e=>$o(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},Yo=()=>ze(document),Xo=(e,t)=>e.view(t).fold(x([]),(t=>{const o=e.owner(t),n=Xo(e,o);return[t].concat(n)}));var Ko=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?A.none():A.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(ze)},owner:e=>ot(e)});const Jo=e=>{const t=Yo(),o=jo(t),n=((e,t)=>{const o=t.owner(e),n=Xo(t,o);return A.some(n)})(e,Ko);return n.fold(k(Kt,e),(t=>{const n=Jt(e),s=W(t,((e,t)=>{const o=Jt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return Yt(s.left+n.left+o.left,s.top+n.top+o.top)}))},Zo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Qo=e=>{const t=Kt(e),o=eo(e),n=$t(e);return Zo(t.left,t.top,o,n)},en=e=>{const t=Jo(e),o=eo(e),n=$t(e);return Zo(t.left,t.top,o,n)},tn=(e,t)=>{const o=Math.max(e.x,t.x),n=Math.max(e.y,t.y),s=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return Zo(o,n,s-o,r-n)},on=()=>qo(window);var nn=tinymce.util.Tools.resolve("tinymce.ThemeManager");const sn=e=>{const t=t=>t(e),o=x(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:E,isError:T,map:t=>an.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>A.some(e)};return s},rn=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:T,isError:E,map:t,mapError:t=>an.error(t(e)),bind:t,exists:T,forall:E,getOr:w,or:w,getOrThunk:_,orThunk:_,getOrDie:O(String(e)),each:b,toOptional:A.none};return o},an={value:sn,error:rn,fromOption:(e,t)=>e.fold((()=>rn(t)),sn)};var ln;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(ln||(ln={}));const cn=(e,t,o)=>e.stype===ln.Error?t(e.serror):o(e.svalue),dn=e=>({stype:ln.Value,svalue:e}),un=e=>({stype:ln.Error,serror:e}),mn=dn,gn=un,pn=cn,hn=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),fn=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},bn=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)ve(s,t)&&(o[t]=e(o[t],s[t]))}return o},vn=bn(((e,t)=>i(e)&&i(t)?vn(e,t):t)),yn=bn(((e,t)=>t)),xn=e=>({tag:"defaultedThunk",process:e}),wn=e=>xn(x(e)),Sn=e=>({tag:"mergeWithThunk",process:e}),kn=e=>{const t=(e=>{const t=[],o=[];return H(e,(e=>{cn(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,y(gn,q)(o)):mn(t.values);var o},Cn=e=>a(e)&&ae(e).length>100?" removed due to size":JSON.stringify(e,null,2),On=(e,t)=>gn([{path:e,getErrorInfo:t}]),_n=e=>({extract:(t,o)=>((e,t)=>e.stype===ln.Error?t(e.serror):e)(e(o),(e=>((e,t)=>On(e,x(t)))(t,e))),toString:x("val")}),Tn=_n(mn),En=(e,t,o,n)=>n(be(e,t).getOrThunk((()=>o(e)))),An=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>mn(A.none())),(e=>((e,t)=>e.stype===ln.Value?{stype:ln.Value,svalue:t(e.svalue)}:e)(s.extract(t.concat([n]),e),A.some)));switch(e.tag){case"required":return((e,t,o,n)=>be(t,o).fold((()=>((e,t,o)=>On(e,(()=>'Could not find valid *required* value for "'+t+'" in '+Cn(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return En(o,n,e.process,r);case"option":return((e,t,o)=>o(be(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(be(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return En(o,n,x({}),(t=>{const n=vn(e.process(o),t);return r(n)}))}},Mn=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),Dn=e=>ae(ge(e,g)),Bn=e=>{const t=In(e),o=W(e,((e,t)=>fn(t,(t=>vn(e,{[t]:!0})),x(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:Dn(n),r=U(s,(e=>!ye(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>On(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},In=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)fn(r,((o,r,a,i)=>{const l=An(a,e,t,o,i);pn(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?gn(s):mn(n)})(t,o,e),toString:()=>{const t=V(e,(e=>fn(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),Fn=e=>({extract:(t,o)=>{const n=V(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return kn(n)},toString:()=>"array("+e.toString()+")"}),Rn=(e,t)=>{const o=void 0!==t?t:w;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===ln.Value)return{stype:ln.Value,svalue:o(e.svalue)};s.push(e)}return kn(s)},toString:()=>"oneOf("+V(e,(e=>e.toString())).join(", ")+")"}},Nn=(e,t)=>({extract:(o,n)=>{const s=ae(n),r=((t,o)=>Fn(_n(e)).extract(t,o))(o,s);return((e,t)=>e.stype===ln.Value?t(e.svalue):e)(r,(e=>{const s=V(e,(e=>hn(e,e,{tag:"required",process:{}},t)));return In(s).extract(o,n)}))},toString:()=>"setOf("+t.toString()+")"}),Ln=y(Fn,In),zn=x(Tn),Vn=(e,t)=>_n((o=>{const n=typeof o;return e(o)?mn(o):gn(`Expected type: ${t} but got: ${n}`)})),Hn=Vn(h,"number"),Pn=Vn(r,"string"),Un=Vn(d,"boolean"),Wn=Vn(p,"function"),jn=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>jn(e[t])));default:return!1}},Gn=_n((e=>jn(e)?mn(e):gn("Expected value to be acceptable for sending via postMessage"))),$n=(e,t)=>({extract:(o,n)=>be(n,e).fold((()=>((e,t)=>On(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>be(o,n).fold((()=>((e,t,o)=>On(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+Cn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ae(t)}),qn=e=>_n((t=>e(t).fold(gn,mn))),Yn=(e,t)=>Nn((t=>e(t).fold(un,dn)),t),Xn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===ln.Error?{stype:ln.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),cn(n,an.error,an.value);var n},Kn=e=>e.fold((e=>{throw new Error(Zn(e))}),w),Jn=(e,t,o)=>Kn(Xn(e,t,o)),Zn=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:x("... (only showing first ten failures)")}]):e;return V(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+Cn(e.input),Qn=(e,t)=>$n(e,ce(t,In)),es=(e,t)=>((e,t)=>{const o=to(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,t),ts=hn,os=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),ns=e=>qn((t=>R(e,t)?an.value(t):an.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),ss=e=>ts(e,e,{tag:"required",process:{}},zn()),rs=(e,t)=>ts(e,e,{tag:"required",process:{}},t),as=e=>rs(e,Hn),is=e=>rs(e,Pn),ls=(e,t)=>ts(e,e,{tag:"required",process:{}},ns(t)),cs=e=>rs(e,Wn),ds=(e,t)=>ts(e,e,{tag:"required",process:{}},In(t)),us=(e,t)=>ts(e,e,{tag:"required",process:{}},Ln(t)),ms=(e,t)=>ts(e,e,{tag:"required",process:{}},Fn(t)),gs=e=>ts(e,e,{tag:"option",process:{}},zn()),ps=(e,t)=>ts(e,e,{tag:"option",process:{}},t),hs=e=>ps(e,Hn),fs=e=>ps(e,Pn),bs=(e,t)=>ps(e,ns(t)),vs=e=>ps(e,Wn),ys=(e,t)=>ps(e,Fn(t)),xs=(e,t)=>ps(e,In(t)),ws=(e,t)=>ts(e,e,wn(t),zn()),Ss=(e,t,o)=>ts(e,e,wn(t),o),ks=(e,t)=>Ss(e,t,Hn),Cs=(e,t)=>Ss(e,t,Pn),Os=(e,t,o)=>Ss(e,t,ns(o)),_s=(e,t)=>Ss(e,t,Un),Ts=(e,t)=>Ss(e,t,Wn),Es=(e,t,o)=>Ss(e,t,Fn(o)),As=(e,t,o)=>Ss(e,t,In(o)),Ms=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},Ds=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return H(e,((n,s)=>{const r=ae(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=ae(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!X(t,(e=>R(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};Ds([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Bs=(e,t)=>((e,t)=>({[e]:t}))(e,t),Is=e=>(e=>{const t={};return H(e,(e=>{t[e.key]=e.value})),t})(e),Fs=e=>p(e)?e:T,Rs=(e,t,o)=>{let n=e.dom;const s=Fs(o);for(;n.parentNode;){n=n.parentNode;const e=ze(n),o=t(e);if(o.isSome())return o;if(s(e))break}return A.none()},Ns=(e,t,o)=>{const n=t(e),s=Fs(o);return n.orThunk((()=>s(e)?A.none():Rs(e,t,s)))},Ls=(e,t)=>et(e.element,t.event.target),zs={can:E,abort:T,run:b},Vs=e=>{if(!ye(e,"can")&&!ye(e,"abort")&&!ye(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...zs,...e}},Hs=x,Ps=Hs("touchstart"),Us=Hs("touchmove"),Ws=Hs("touchend"),js=Hs("touchcancel"),Gs=Hs("mousedown"),$s=Hs("mousemove"),qs=Hs("mouseout"),Ys=Hs("mouseup"),Xs=Hs("mouseover"),Ks=Hs("focusin"),Js=Hs("focusout"),Zs=Hs("keydown"),Qs=Hs("keyup"),er=Hs("input"),tr=Hs("change"),or=Hs("click"),nr=Hs("transitioncancel"),sr=Hs("transitionend"),rr=Hs("transitionstart"),ar=Hs("selectstart"),ir=e=>x("alloy."+e),lr={tap:ir("tap")},cr=ir("focus"),dr=ir("blur.post"),ur=ir("paste.post"),mr=ir("receive"),gr=ir("execute"),pr=ir("focus.item"),hr=lr.tap,fr=ir("longpress"),br=ir("sandbox.close"),vr=ir("typeahead.cancel"),yr=ir("system.init"),xr=ir("system.touchmove"),wr=ir("system.touchend"),Sr=ir("system.scroll"),kr=ir("system.resize"),Cr=ir("system.attached"),Or=ir("system.detached"),_r=ir("system.dismissRequested"),Tr=ir("system.repositionRequested"),Er=ir("focusmanager.shifted"),Ar=ir("slotcontainer.visibility"),Mr=ir("system.external.element.scroll"),Dr=ir("change.tab"),Br=ir("dismiss.tab"),Ir=ir("highlight"),Fr=ir("dehighlight"),Rr=(e,t)=>{Vr(e,e.element,t,{})},Nr=(e,t,o)=>{Vr(e,e.element,t,o)},Lr=e=>{Rr(e,gr())},zr=(e,t,o)=>{Vr(e,t,o,{})},Vr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},Hr=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Pr=e=>Is(e),Ur=(e,t)=>({key:e,value:Vs({abort:t})}),Wr=e=>({key:e,value:Vs({run:(e,t)=>{t.event.prevent()}})}),jr=(e,t)=>({key:e,value:Vs({run:t})}),Gr=(e,t,o)=>({key:e,value:Vs({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),$r=e=>t=>({key:e,value:Vs({run:(e,o)=>{Ls(e,o)&&t(e,o)}})}),qr=(e,t,o)=>((e,t)=>jr(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Hr(t,t.element,e,n)}))})))(e,t.partUids[o]),Yr=(e,t)=>jr(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>Ns(n.target,(t=>e.getSystem().getByDom(t).toOptional()),T).getOr(e)));t(e,s,o)})),Xr=e=>jr(e,((e,t)=>{t.cut()})),Kr=e=>jr(e,((e,t)=>{t.stop()})),Jr=(e,t)=>$r(e)(t),Zr=$r(Cr()),Qr=$r(Or()),ea=$r(yr()),ta=(ia=gr(),e=>jr(ia,e)),oa=e=>e.dom.innerHTML,na=(e,t)=>{const o=ot(e).dom,n=ze(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,ct(ze(o))})(t,o);Po(n,s),Uo(e),Ho(e,n)},sa=(e,t)=>ze(e.dom.cloneNode(t)),ra=e=>(e=>{if(pt(e))return"#shadow-root";{const t=(e=>sa(e,!1))(e);return(e=>{const t=Ne("div"),o=ze(e.dom.cloneNode(!0));return Ho(t,o),oa(t)})(t)}})(e),aa=Pr([((e,t)=>({key:e,value:Vs({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>et(t,e.element)&&!et(t,o))(e,n,s)||(console.warn(cr()+" did not get interpreted by the desired target. \nOriginator: "+ra(n)+"\nTarget: "+ra(s)+"\nCheck the "+cr()+" event handlers"),!1)}})}))(cr())]);var ia,la=Object.freeze({__proto__:null,events:aa});let ca=0;const da=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return ca++,e+"_"+o+ca+String(t)},ua=x("alloy-id-"),ma=x("data-alloy-id"),ga=ua(),pa=ma(),ha=(e,t)=>{Object.defineProperty(e.dom,pa,{value:t,writable:!0})},fa=e=>{const t=$e(e)?e.dom[pa]:null;return A.from(t)},ba=e=>da(e),va=w,ya=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+ra(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:x("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:T}},xa=ya(),wa=e=>V(e,(e=>Ae(e,"/*")?e.substring(0,e.length-2):e)),Sa=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:wa(r)}),e},ka=da("alloy-premade"),Ca=e=>(Object.defineProperty(e.element.dom,ka,{value:e.uid,writable:!0}),Bs(ka,e)),Oa=e=>be(e,ka),_a=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:wa(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),Ta={init:()=>Ea({readState:x("No State required")})},Ea=e=>e,Aa=(e,t)=>{const o={};return le(e,((e,n)=>{le(e,((e,s)=>{const r=be(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},Ma=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),Da=e=>e.cHandler,Ba=(e,t)=>({name:e,handler:t}),Ia=(e,t)=>{const o={};return H(e,(e=>{o[e.name()]=e.handlers(t)})),o},Fa=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const s=ee(o,((o,s)=>{const r=o[t],a=s[t],i=n.indexOf(r),l=n.indexOf(a);if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===l)throw new Error("The ordering for "+e+" does not have an entry for "+a+".\nOrder specified: "+JSON.stringify(n,null,2));return i<l?-1:l<i?1:0}));return an.value(s)}catch(e){return an.error([e])}})("Event: "+o,"name",e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>j(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>j(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{H(e,(e=>{e.run.apply(void 0,t)}))}}})(V(e,(e=>e.handler))))):((e,t)=>an.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(V(t,(e=>e.name)),null,2)]))(o,e)},Ra=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return H(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,an.error(q(n))):((e,t)=>0===e.length?an.value(t):an.value(vn(t,yn.apply(void 0,e))))(o.values,t);var n})(pe(e,((e,o)=>(1===e.length?an.value(e[0].handler):Fa(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:E,abort:T,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?U(t[o],(t=>N(e,(e=>e.name===t)))).join(" > "):e[0].name;return Bs(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),Na="alloy.base.behaviour",La=In([ts("dom","dom",{tag:"required",process:{}},In([ss("tag"),ws("styles",{}),ws("classes",[]),ws("attributes",{}),gs("value"),gs("innerHtml")])),ss("components"),ss("uid"),ws("events",{}),ws("apis",{}),ts("eventOrder","eventOrder",(mi={[gr()]:["disabling",Na,"toggling","typeaheadevents"],[cr()]:[Na,"focusing","keying"],[yr()]:[Na,"disabling","toggling","representing"],[er()]:[Na,"representing","streaming","invalidating"],[Or()]:[Na,"representing","item-events","toolbar-button-events","tooltipping"],[Gs()]:["focusing",Na,"item-type-events"],[Ps()]:["focusing",Na,"item-type-events"],[Xs()]:["item-type-events","tooltipping"],[mr()]:["receiving","reflecting","tooltipping"]},Sn(x(mi))),zn()),gs("domModification")]),za=e=>e.events,Va=(e,t)=>{const o=Tt(e,t);return void 0===o||""===o?[]:o.split(" ")},Ha=e=>void 0!==e.dom.classList,Pa=e=>Va(e,"class"),Ua=(e,t)=>((e,t,o)=>{const n=Va(e,t).concat([o]);return Ot(e,t,n.join(" ")),!0})(e,"class",t),Wa=(e,t)=>((e,t,o)=>{const n=U(Va(e,t),(e=>e!==o));return n.length>0?Ot(e,t,n.join(" ")):Mt(e,t),!1})(e,"class",t),ja=(e,t)=>{Ha(e)?e.dom.classList.add(t):Ua(e,t)},Ga=e=>{0===(Ha(e)?e.dom.classList:Pa(e)).length&&Mt(e,"class")},$a=(e,t)=>{Ha(e)?e.dom.classList.remove(t):Wa(e,t),Ga(e)},qa=(e,t)=>Ha(e)&&e.dom.classList.contains(t),Ya=(e,t)=>{H(t,(t=>{ja(e,t)}))},Xa=(e,t)=>{H(t,(t=>{$a(e,t)}))},Ka=e=>Ha(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Pa(e),Ja=e=>e.dom.value,Za=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},Qa=(e,t,o)=>{o.fold((()=>Ho(e,t)),(e=>{et(e,t)||(Lo(e,t),Wo(e))}))},ei=(e,t,o)=>{const n=V(t,o),s=ct(e);return H(s.slice(n.length),Wo),n},ti=(e,t,o,n)=>{const s=dt(e,t),r=n(o,s),a=((e,t,o)=>dt(e,t).map((e=>{if(o.exists((t=>!et(t,e)))){const t=o.map(We).getOr("span"),n=Ne(t);return Lo(e,n),n}return e})))(e,t,s);return Qa(e,r.element,a),r},oi=(e,t)=>{const o=ae(e),n=ae(t),s=J(n,o),r=((e,o)=>{const n={},s={};return me(e,((e,o)=>!ve(t,o)||e!==t[o]),ue(n),ue(s)),{t:n,f:s}})(e).t;return{toRemove:s,toSet:r}},ni=(e,t)=>{const o=t.filter((t=>We(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>ve(e.dom,ka))(t))).bind((t=>((e,t)=>{try{const o=((e,t)=>{const{class:o,style:n,...s}=(e=>j(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=oi(e.attributes,s),i=Vt(t),{toSet:l,toRemove:c}=oi(e.styles,i),d=Ka(t),u=J(d,e.classes),m=J(e.classes,d);return H(a,(e=>Mt(t,e))),_t(t,r),Ya(t,m),Xa(t,u),H(c,(e=>Pt(t,e))),Ft(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{ei(e,t,((t,o)=>{const n=dt(e,o);return Qa(e,t,n),t}))})(t,o)}),(e=>{na(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==Ja(o)&&Za(o,null!=n?n:"")})(),t})(e,t);return A.some(o)}catch(e){return A.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Ne(e.tag);_t(t,e.attributes),Ya(t,e.classes),Ft(t,e.styles),e.innerHtml.each((e=>na(t,e)));const o=e.domChildren;return Po(t,o),e.value.each((e=>{Za(t,e)})),t})(e)));return ha(o,e.uid),o},si=e=>{const t=(e=>{const t=be(e,"behaviours").getOr({});return Y(ae(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=V(t,(e=>xs(e.name(),[ss("config"),ws("state",Ta)]))),n=Xn("component.behaviours",In(o),e.behaviours).fold((t=>{throw new Error(Zn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),w);return{list:t,data:ce(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return x(t)}))}})(e,t))(e,t)},ri=(e,t)=>{const o=()=>m,n=Ms(xa),s=Kn((e=>Xn("custom.definition",La,e))(e)),r=si(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:V(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>Ma({})),Ma))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};H(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=Aa(s,((e,t)=>({name:e,modification:t}))),a=e=>W(e,((e,t)=>({...t.modification,...e})),{}),i=W(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return Ma({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=ni(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":za(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...Ia(t,e)};return Aa(n,Ba)})(e,o,n);return Ra(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=Ms(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(ya(o))},element:c,syncComponents:()=>{const e=ct(c),t=Y(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},ai=e=>{const t=Le(e);return ii({element:t})},ii=e=>{const t=Jn("external.component",Bn([ss("element"),gs("uid")]),e),o=Ms(ya()),n=t.uid.getOrThunk((()=>ba("external")));ha(t.element,n);const s={uid:n,getSystem:o.get,config:A.none,hasConfigured:T,connect:e=>{o.set(e)},disconnect:()=>{o.set(ya((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:x("No state"),syncComponents:b,components:x([]),events:{}};return Ca(s)},li=ba,ci=(e,t)=>Oa(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=va(e),s=((e,t)=>{const o=be(e,"components").getOr([]);return t.fold((()=>V(o,di)),(e=>V(o,((t,o)=>ci(t,dt(e,o))))))})(n,t),r={...n,events:{...la,...o},components:s};return an.value(ri(r,t))})((e=>ve(e,"uid"))(e)?e:{uid:li(""),...e},t).getOrDie())),di=e=>ci(e,A.none()),ui=Ca;var mi,gi=(e,t,o,n,s)=>e(o,n)?A.some(o):p(s)&&s(o)?A.none():t(o,n,s);const pi=(e,t,o)=>{let n=e.dom;const s=p(o)?o:T;for(;n.parentNode;){n=n.parentNode;const e=ze(n);if(t(e))return A.some(e);if(s(e))break}return A.none()},hi=(e,t,o)=>gi(((e,t)=>t(e)),pi,e,t,o),fi=(e,t)=>G(e.dom.childNodes,(e=>t(ze(e)))).map(ze),bi=(e,t,o)=>hi(e,t,o).isSome(),vi=(e,t,o)=>pi(e,(e=>Je(e,t)),o),yi=(e,t)=>((e,o)=>{const n=e.dom;return n.parentNode?fi(ze(n.parentNode),(o=>!et(e,o)&&Je(o,t))):A.none()})(e),xi=(e,t)=>fi(e,(e=>Je(e,t))),wi=(e,t)=>Qe(t,e),Si=(e,t,o)=>gi(((e,t)=>Je(e,t)),vi,e,t,o),ki="aria-controls",Ci=()=>{const e=da(ki);return{id:e,link:t=>{Ot(t,ki,e)},unlink:e=>{Mt(e,ki)}}},Oi=(e,t)=>bi(t,(t=>et(t,e.element)),T)||((e,t)=>(e=>hi(e,(e=>{if(!$e(e))return!1;const t=Tt(e,"id");return void 0!==t&&t.indexOf(ki)>-1})).bind((e=>{const t=Tt(e,"id"),o=bt(e);return wi(o,`[${ki}="${t}"]`)})))(t).exists((t=>Oi(e,t))))(e,t);var _i;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(_i||(_i={}));const Ti=Ms({}),Ei=["alloy/data/Fields","alloy/debugging/Debugging"],Ai=(e,t,o)=>((e,t,o)=>{switch(be(Ti.get(),e).orThunk((()=>{const t=ae(Ti.get());return re(t,(t=>e.indexOf(t)>-1?A.some(Ti.get()[t]):A.none()))})).getOr(_i.NORMAL)){case _i.NORMAL:return o(Mi());case _i.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();R(["mousemove","mouseover","mouseout",yr()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:V(o,(e=>R(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+ra(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case _i.STOP:return!0}})(e,t,o),Mi=x({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),Di=x([ss("menu"),ss("selectedMenu")]),Bi=x([ss("item"),ss("selectedItem")]);x(In(Bi().concat(Di())));const Ii=x(In(Bi())),Fi=ds("initSize",[ss("numColumns"),ss("numRows")]),Ri=()=>ds("markers",[ss("backgroundMenu")].concat(Di()).concat(Bi())),Ni=e=>ds("markers",V(e,ss)),Li=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");G(t,(e=>e.indexOf("alloy")>0&&!N(Ei,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),ts(t,t,o,qn((e=>an.value(((...t)=>e.apply(void 0,t))))))),zi=e=>Li(0,e,wn(b)),Vi=e=>Li(0,e,wn(A.none)),Hi=e=>Li(0,e,{tag:"required",process:{}}),Pi=e=>Li(0,e,{tag:"required",process:{}}),Ui=(e,t)=>os(e,x(t)),Wi=e=>os(e,w),ji=x(Fi),Gi=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),$i=Ds([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),qi=$i.southeast,Yi=$i.southwest,Xi=$i.northeast,Ki=$i.northwest,Ji=$i.south,Zi=$i.north,Qi=$i.east,el=$i.west,tl=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},ol=(e,t,o)=>Math.min(Math.max(e,t),o),nl=(e,t)=>Z(["left","right","top","bottom"],(o=>be(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),sl="layout",rl=e=>e.x,al=(e,t)=>e.x+e.width/2-t.width/2,il=(e,t)=>e.x+e.width-t.width,ll=(e,t)=>e.y-t.height,cl=e=>e.y+e.height,dl=(e,t)=>e.y+e.height/2-t.height/2,ul=(e,t,o)=>Gi(rl(e),cl(e),o.southeast(),qi(),"southeast",nl(e,{left:1,top:3}),sl),ml=(e,t,o)=>Gi(il(e,t),cl(e),o.southwest(),Yi(),"southwest",nl(e,{right:0,top:3}),sl),gl=(e,t,o)=>Gi(rl(e),ll(e,t),o.northeast(),Xi(),"northeast",nl(e,{left:1,bottom:2}),sl),pl=(e,t,o)=>Gi(il(e,t),ll(e,t),o.northwest(),Ki(),"northwest",nl(e,{right:0,bottom:2}),sl),hl=(e,t,o)=>Gi(al(e,t),ll(e,t),o.north(),Zi(),"north",nl(e,{bottom:2}),sl),fl=(e,t,o)=>Gi(al(e,t),cl(e),o.south(),Ji(),"south",nl(e,{top:3}),sl),bl=(e,t,o)=>Gi((e=>e.x+e.width)(e),dl(e,t),o.east(),Qi(),"east",nl(e,{left:0}),sl),vl=(e,t,o)=>Gi(((e,t)=>e.x-t.width)(e,t),dl(e,t),o.west(),el(),"west",nl(e,{right:1}),sl),yl=()=>[ul,ml,gl,pl,fl,hl,bl,vl],xl=()=>[ml,ul,pl,gl,fl,hl,bl,vl],wl=()=>[gl,pl,ul,ml,hl,fl],Sl=()=>[pl,gl,ml,ul,hl,fl],kl=()=>[ul,ml,gl,pl,fl,hl],Cl=()=>[ml,ul,pl,gl,fl,hl];var Ol=Object.freeze({__proto__:null,events:e=>Pr([jr(mr(),((t,o)=>{const n=e.channels,s=ae(n),r=o,a=((e,t)=>t.universal?e:U(e,(e=>R(t.channels,e))))(s,r);H(a,(e=>{const o=n[e],s=o.schema,a=Jn("channel["+e+"] data\nReceiver: "+ra(t.element),s,r.data);o.onReceive(t,a)}))}))])}),_l=[rs("channels",Yn(an.value,Bn([Hi("onReceive"),ws("schema",zn())])))];const Tl=(e,t,o)=>ea(((n,s)=>{o(n,e,t)})),El=e=>({key:e,value:void 0}),Al=(e,t,o,n,s,r,a)=>{const i=e=>ye(e,o)?e[o]():A.none(),l=ce(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:wa(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:x(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...ce(r,((e,t)=>Sa(e,t))),...l,revoke:k(El,o),config:t=>{const n=Jn(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:to((()=>Jn(o+"-config",e,t))),initialConfig:t,state:a}}},schema:x(t),exhibit:(e,t)=>Se(i(e),be(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>Ma({}))),name:x(o),handlers:e=>i(e).map((e=>be(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},Ml=e=>Is(e),Dl=Bn([ss("fields"),ss("name"),ws("active",{}),ws("apis",{}),ws("state",Ta),ws("extra",{})]),Bl=e=>{const t=Jn("Creating behaviour: "+e.name,Dl,e);return((e,t,o,n,s,r)=>{const a=Bn(e),i=xs(t,[("config",l=e,ps("config",Bn(l)))]);var l;return Al(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},Il=Bn([ss("branchKey"),ss("branches"),ss("name"),ws("active",{}),ws("apis",{}),ws("state",Ta),ws("extra",{})]),Fl=e=>{const t=Jn("Creating behaviour: "+e.name,Il,e);return((e,t,o,n,s,r)=>{const a=e,i=xs(t,[ps("config",e)]);return Al(a,i,t,o,n,s,r)})(Qn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},Rl=x(void 0),Nl=Bl({fields:_l,name:"receiving",active:Ol});var Ll=Object.freeze({__proto__:null,exhibit:(e,t)=>Ma({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const zl=(e,t=!1)=>e.dom.focus({preventScroll:t}),Vl=e=>e.dom.blur(),Hl=e=>{const t=bt(e).dom;return e.dom===t.activeElement},Pl=(e=Yo())=>A.from(e.dom.activeElement).map(ze),Ul=e=>Pl(bt(e)).filter((t=>e.dom.contains(t.dom))),Wl=(e,t)=>{const o=bt(t),n=Pl(o).bind((e=>{const o=t=>et(e,t);return o(t)?A.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=ze(e.childNodes[n]);if(t(s))return A.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return A.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{Pl(o).filter((t=>et(t,e))).fold((()=>{zl(e)}),b)})),s},jl=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},Gl=(e,t)=>{Rt(e,(e=>({...e,position:A.some(e.position)}))(t))},$l=Ds([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),ql=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=A.some(i),m=A.some(l),g=A.some(c),p=A.some(d),h=A.none();return t.direction.fold((()=>jl(e,u,m,h,h)),(()=>jl(e,h,m,g,h)),(()=>jl(e,u,h,h,p)),(()=>jl(e,h,h,g,p)),(()=>jl(e,u,m,h,h)),(()=>jl(e,u,h,h,p)),(()=>jl(e,u,m,h,h)),(()=>jl(e,h,m,g,h)))},Yl=(e,t)=>e.fold((()=>{const e=t.rect;return jl("absolute",A.some(e.x),A.some(e.y),A.none(),A.none())}),((e,o,n,s)=>ql("absolute",t,e,o,n,s)),((e,o,n,s)=>ql("fixed",t,e,o,n,s))),Xl=(e,t)=>{const o=k(Jo,t),n=e.fold(o,o,(()=>{const e=jo();return Jo(t).translate(-e.left,-e.top)})),s=eo(t),r=$t(t);return Zo(n.left,n.top,s,r)},Kl=(e,t)=>t.fold((()=>e.fold(on,on,Zo)),(t=>e.fold(x(t),x(t),(()=>{const o=Jl(e,t.x,t.y);return Zo(o.left,o.top,t.width,t.height)})))),Jl=(e,t,o)=>{const n=Yt(t,o);return e.fold(x(n),x(n),(()=>{const e=jo();return n.translate(-e.left,-e.top)}))};$l.none;const Zl=$l.relative,Ql=$l.fixed,ec="data-alloy-placement",tc=e=>Et(e,ec),oc=Ds([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),nc=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?ol(i,e.y,e.bottom):ol(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Zo(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Zo(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=ol(a,o,d),g=ol(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Zo(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=x(t.bottom-o.y),s=x(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=x(t.right-o.x),i=x(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),y={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?oc.fit(y):oc.nofit(y,m,g,f)},sc=e=>{const t=Ms(A.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(A.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(A.some(e))}}},rc=()=>sc((e=>e.unbind())),ac=()=>{const e=sc(b);return{...e,on:t=>e.get().each(t)}},ic=E,lc=(e,t,o)=>((e,t,o,n)=>Ro(e,t,o,n,!1))(e,t,ic,o),cc=(e,t,o)=>((e,t,o,n)=>Ro(e,t,o,n,!0))(e,t,ic,o),dc=Fo,uc=["top","bottom","right","left"],mc="data-alloy-transition-timer",gc=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>X(t,(t=>qa(e,t))))(e,t.classes))(e,n)){It(e,"position",o.position);const a=Xl(t,e),l=Yl(t,{...s,rect:a}),c=Z(uc,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return he(t,((t,n)=>!((e,t,o=S)=>Se(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Rt(e,c),i&&((e,t)=>{Ya(e,t.classes),Et(e,mc).each((t=>{clearTimeout(parseInt(t,10)),Mt(e,mc)})),((e,t)=>{const o=rc(),n=rc();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return et(t.target,e)&&Be(n)&&R(uc,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===sr())&&(clearTimeout(s),Mt(e,mc),Xa(e,t.classes))}},l=lc(e,rr(),(t=>{a(t)&&(l.unbind(),o.set(lc(e,sr(),i)),n.set(lc(e,nr(),i)))})),c=(e=>{const t=t=>{const o=Nt(e,t).split(/\s*,\s*/);return U(o,De)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Ae(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return j(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),Ot(e,mc,s)}))})(e,t)})(e,n),Ut(e))}else Xa(e,n.classes)},pc=(e,t)=>{((e,t)=>{const o=jt.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);It(e,"max-height",o+"px")})(e,Math.floor(t))},hc=x(((e,t)=>{pc(e,t),Ft(e,{"overflow-x":"hidden","overflow-y":"auto"})})),fc=x(((e,t)=>{pc(e,t)})),bc=(e,t,o)=>void 0===e[t]?o:e[t],vc=(e,t,o,n)=>{const s=((e,t,o,n)=>{Pt(t,"max-height"),Pt(t,"max-width");const s={width:eo(r=t),height:$t(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=nc(m,a,i,r);return g.fold(x(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:oc.nofit(l,c,d,u)))};return j(t,((e,t)=>{const o=k(l,t);return e.fold(x(e),o)}),oc.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:qi(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(w,w)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=Yl(o.origin,t);o.transition.each((s=>{gc(e,o.origin,n,s,t,o.lastPlacement)})),Gl(e,n)})(t,s,n),((e,t)=>{((e,t)=>{Ot(e,ec,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;Xa(e,o.off),Ya(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},yc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],xc=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>be(o,e).getOr([]),i=(e,t,o)=>{const n=J(yc,o);return{offset:Yt(e,t),classesOn:Y(o,a),classesOff:Y(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},wc=()=>xc(0,0,{}),Sc=w,kc=(e,t)=>o=>"rtl"===Cc(o)?t:e,Cc=e=>"rtl"===Nt(e,"direction")?"rtl":"ltr";var Oc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(Oc||(Oc={}));const _c="data-alloy-vertical-dir",Tc=e=>bi(e,(e=>$e(e)&&Tt(e,"data-alloy-vertical-dir")===Oc.BottomToTop)),Ec=()=>xs("layouts",[ss("onLtr"),ss("onRtl"),gs("onBottomLtr"),gs("onBottomRtl")]),Ac=(e,t,o,n,s,r,a)=>{const i=a.map(Tc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return kc(d,u)(e)};var Mc=[ss("hotspot"),gs("bubble"),ws("overrides",{}),Ec(),Ui("placement",((e,t,o)=>{const n=t.hotspot,s=Xl(o,n.element),r=Ac(e.element,t,kl(),Cl(),wl(),Sl(),A.some(t.hotspot.element));return A.some(Sc({anchorBox:s,bubble:t.bubble.getOr(wc()),overrides:t.overrides,layouts:r}))}))],Dc=[ss("x"),ss("y"),ws("height",0),ws("width",0),ws("bubble",wc()),ws("overrides",{}),Ec(),Ui("placement",((e,t,o)=>{const n=Jl(o,t.x,t.y),s=Zo(n.left,n.top,t.width,t.height),r=Ac(e.element,t,yl(),xl(),yl(),xl(),A.none());return A.some(Sc({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r}))}))];const Bc=Ds([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),Ic=e=>e.fold(w,((e,t,o)=>e.translate(-t,-o))),Fc=e=>e.fold(w,w),Rc=e=>j(e,((e,t)=>e.translate(t.left,t.top)),Yt(0,0)),Nc=e=>{const t=V(e,Fc);return Rc(t)},Lc=Bc.screen,zc=Bc.absolute,Vc=(e,t,o)=>{const n=ot(e.element),s=jo(n),r=((e,t,o)=>{const n=rt(o.root).dom;return A.from(n.frameElement).map(ze).filter((t=>{const o=ot(t),n=ot(e.element);return et(o,n)})).map(Kt)})(e,0,o).getOr(s);return zc(r,s.left,s.top)},Hc=(e,t,o,n)=>{const s=Lc(Yt(e,t));return A.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},Pc=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>Nc(r),l=()=>Nc(r),c=()=>(e=>{const t=V(e,Ic);return Rc(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?wl():kl(),m=o.showAbove?Sl():Cl(),g=Ac(s,o,u,m,u,m,A.none());var p,h,f,b;return Sc({anchorBox:d,bubble:o.bubble.getOr(wc()),overrides:o.overrides,layouts:g})}));var Uc=[ss("node"),ss("root"),gs("bubble"),Ec(),ws("overrides",{}),ws("showAbove",!1),Ui("placement",((e,t,o)=>{const n=Vc(e,0,t);return t.node.filter(wt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=Hc(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return Pc(a,n,t,o,i)}))}))];const Wc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),jc=Ds([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Gc=(jc.before,jc.on,jc.after,e=>e.fold(w,w,w)),$c=Ds([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),qc={domRange:$c.domRange,relative:$c.relative,exact:$c.exact,exactFromRange:e=>$c.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>ze(e.startContainer),relative:(e,t)=>Gc(e),exact:(e,t,o,n)=>e}))(e);return rt(t)},range:Wc},Yc=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},Xc=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},Kc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),Jc=Ds([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Zc=(e,t,o)=>t(ze(o.startContainer),o.startOffset,ze(o.endContainer),o.endOffset),Qc=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:x(e),rtl:A.none}),relative:(t,o)=>({ltr:to((()=>Yc(e,t,o))),rtl:to((()=>A.some(Yc(e,o,t))))}),exact:(t,o,n,s)=>({ltr:to((()=>Xc(e,t,o,n,s))),rtl:to((()=>A.some(Xc(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Jc.rtl(ze(e.endContainer),e.endOffset,ze(e.startContainer),e.startOffset))).getOrThunk((()=>Zc(0,Jc.ltr,o))):Zc(0,Jc.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});Jc.ltr,Jc.rtl;const ed=(e,t,o)=>U(((e,t)=>{const o=p(t)?t:T;let n=e.dom;const s=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=ze(e);if(s.push(t),!0===o(t))break;n=e}return s})(e,o),t),td=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Ze(o)?[]:V(o.querySelectorAll(e),ze)})(t,e),od=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return A.some(Wc(ze(t.startContainer),t.startOffset,ze(o.endContainer),o.endOffset))}return A.none()},nd=e=>{if(null===e.anchorNode||null===e.focusNode)return od(e);{const t=ze(e.anchorNode),o=ze(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=ot(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=et(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?A.some(Wc(t,e.anchorOffset,o,e.focusOffset)):od(e)}},sd=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?A.some(o).map(Kc):A.none()})(Qc(e,t)),rd=((e,t)=>{const o=t=>e(t)?A.from(t.dom.nodeValue):A.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(qe),ad=(e,t)=>({element:e,offset:t}),id=(e,t)=>qe(e)?ad(e,t):((e,t)=>{const o=ct(e);if(0===o.length)return ad(e,t);if(t<o.length)return ad(o[t],0);{const e=o[o.length-1],t=qe(e)?(e=>rd.get(e))(e).length:ct(e).length;return ad(e,t)}})(e,t),ld=e=>void 0!==e.foffset,cd=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>A.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(nd))(e)))().map((e=>{if(ld(e)){const t=id(e.start,e.soffset),o=id(e.finish,e.foffset);return qc.range(t.element,t.offset,o.element,o.offset)}return e}));var dd=[gs("getSelection"),ss("root"),gs("bubble"),Ec(),ws("overrides",{}),ws("showAbove",!1),Ui("placement",((e,t,o)=>{const n=rt(t.root).dom,s=Vc(e,0,t),r=cd(n,t).bind((e=>{if(ld(e)){const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?A.some(t).map(Kc):A.none()})(Qc(e,t)))(n,qc.exactFromRange(e)).orThunk((()=>{const t=Le("\ufeff");Lo(e.start,t);const o=sd(n,qc.exact(t,0,t,1));return Wo(t),o}));return t.bind((e=>Hc(e.left,e.top,e.width,e.height)))}{const t=ce(e,(e=>e.dom.getBoundingClientRect())),o={left:Math.min(t.firstCell.left,t.lastCell.left),right:Math.max(t.firstCell.right,t.lastCell.right),top:Math.min(t.firstCell.top,t.lastCell.top),bottom:Math.max(t.firstCell.bottom,t.lastCell.bottom)};return Hc(o.left,o.top,o.right-o.left,o.bottom-o.top)}})),a=cd(n,t).bind((e=>ld(e)?$e(e.start)?A.some(e.start):it(e.start):A.some(e.firstCell))).getOr(e.element);return Pc(r,s,t,o,a)}))];const ud="link-layout",md=e=>e.x+e.width,gd=(e,t)=>e.x-t.width,pd=(e,t)=>e.y-t.height+e.height,hd=e=>e.y,fd=(e,t,o)=>Gi(md(e),hd(e),o.southeast(),qi(),"southeast",nl(e,{left:0,top:2}),ud),bd=(e,t,o)=>Gi(gd(e,t),hd(e),o.southwest(),Yi(),"southwest",nl(e,{right:1,top:2}),ud),vd=(e,t,o)=>Gi(md(e),pd(e,t),o.northeast(),Xi(),"northeast",nl(e,{left:0,bottom:3}),ud),yd=(e,t,o)=>Gi(gd(e,t),pd(e,t),o.northwest(),Ki(),"northwest",nl(e,{right:1,bottom:3}),ud),xd=()=>[fd,bd,vd,yd],wd=()=>[bd,fd,yd,vd];var Sd=[ss("item"),Ec(),ws("overrides",{}),Ui("placement",((e,t,o)=>{const n=Xl(o,t.item.element),s=Ac(e.element,t,xd(),wd(),xd(),wd(),A.none());return A.some(Sc({anchorBox:n,bubble:wc(),overrides:t.overrides,layouts:s}))}))],kd=Qn("type",{selection:dd,node:Uc,hotspot:Mc,submenu:Sd,makeshift:Dc});const Cd=[ms("classes",Pn),Os("mode","all",["all","layout","placement"])],Od=[ws("useFixed",T),gs("getBounds")],_d=[rs("anchor",kd),xs("transition",Cd)],Td=(e,t,o,n,s,r)=>{const a=Jn("placement.info",In(_d),s),i=a.anchor,l=n.element,c=o.get(n.uid);Wl((()=>{It(l,"position","fixed");const s=zt(l,"visibility");It(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return Ql(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Kt(e.element),o=e.element.dom.getBoundingClientRect();return Zl(t.left,t.top,o.width,o.height)})(e);i.placement(e,i,d).each((e=>{const s=r.orThunk((()=>t.getBounds.map(_))),i=((e,t,o,n,s,r)=>((e,t,o,n,s,r,a,i)=>{const l=bc(a,"maxHeightFunction",hc()),c=bc(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:Kl(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return vc(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(t.anchorBox,e),n.element,t.bubble,t.layouts,s,o,t.overrides,r))(d,e,s,n,c,a.transition);o.set(n.uid,i)})),s.fold((()=>{Pt(l,"visibility")}),(e=>{It(l,"visibility",e)})),zt(l,"left").isNone()&&zt(l,"top").isNone()&&zt(l,"right").isNone()&&zt(l,"bottom").isNone()&&xe(zt(l,"position"),"fixed")&&Pt(l,"position")}),l)};var Ed=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{const r=A.none();Td(e,t,o,n,s,r)},positionWithinBounds:Td,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;H(["position","left","right","top","bottom"],(e=>Pt(s,e))),(e=>{Mt(e,ec)})(s),o.clear(n.uid)}});const Ad=Bl({fields:Od,name:"positioning",active:Ll,apis:Ed,state:Object.freeze({__proto__:null,init:()=>{let e={};return Ea({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>be(e,t)})}})}),Md=e=>e.getSystem().isConnected(),Dd=e=>{Rr(e,Or());const t=e.components();H(t,Dd)},Bd=e=>{const t=e.components();H(t,Bd),Rr(e,Cr())},Id=(e,t)=>{e.getSystem().addToWorld(t),wt(e.element)&&Bd(t)},Fd=e=>{Dd(e),e.getSystem().removeFromWorld(e)},Rd=(e,t)=>{Ho(e.element,t.element)},Nd=(e,t)=>{Ld(e,t,Ho)},Ld=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),wt(e.element)&&Bd(t),e.syncComponents()},zd=e=>{Dd(e),Wo(e.element),e.getSystem().removeFromWorld(e)},Vd=e=>{const t=at(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));zd(e),t.each((e=>{e.syncComponents()}))},Hd=e=>{const t=e.components();H(t,zd),Uo(e.element),e.syncComponents()},Pd=(e,t)=>{Wd(e,t,Ho)},Ud=(e,t)=>{Wd(e,t,zo)},Wd=(e,t,o)=>{o(e,t.element);const n=ct(t.element);H(n,(e=>{t.getByDom(e).each(Bd)}))},jd=e=>{const t=ct(e.element);H(t,(t=>{e.getByDom(t).each(Dd)})),Wo(e.element)},Gd=(e,t,o,n)=>{o.get().each((t=>{Hd(e)}));const s=t.getAttachPoint(e);Nd(s,e);const r=e.getSystem().build(n);return Nd(e,r),o.set(r),r},$d=(e,t,o,n)=>{const s=Gd(e,t,o,n);return t.onOpen(e,s),s},qd=(e,t,o)=>{o.get().each((n=>{Hd(e),Vd(e),t.onClose(e,n),o.clear()}))},Yd=(e,t,o)=>o.isOpen(),Xd=(e,t,o)=>{const n=t.getAttachPoint(e);It(e.element,"position",Ad.getMode(n)),((e,t,o,n)=>{zt(e.element,t).fold((()=>{Mt(e.element,o)}),(t=>{Ot(e.element,o,t)})),It(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},Kd=(e,t,o)=>{(e=>N(["top","left","right","bottom"],(t=>zt(e,t).isSome())))(e.element)||Pt(e.element,"position"),((e,t,o)=>{Et(e.element,o).fold((()=>Pt(e.element,t)),(o=>It(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var Jd=Object.freeze({__proto__:null,cloak:Xd,decloak:Kd,open:$d,openWhileCloaked:(e,t,o,n,s)=>{Xd(e,t),$d(e,t,o,n),s(),Kd(e,t)},close:qd,isOpen:Yd,isPartOf:(e,t,o,n)=>Yd(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>Gd(e,t,o,n)))}),Zd=Object.freeze({__proto__:null,events:(e,t)=>Pr([jr(br(),((o,n)=>{qd(o,e,t)}))])}),Qd=[zi("onOpen"),zi("onClose"),ss("isPartOf"),ss("getAttachPoint"),ws("cloakVisibilityAttr","data-precloak-visibility")],eu=Object.freeze({__proto__:null,init:()=>{const e=ac(),t=x("not-implemented");return Ea({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const tu=Bl({fields:Qd,name:"sandboxing",active:Zd,apis:Jd,state:eu}),ou=x("dismiss.popups"),nu=x("reposition.popups"),su=x("mouse.released"),ru=Bn([ws("isExtraPart",T),xs("fireEventInstead",[ws("event",_r())])]),au=e=>{const t=Jn("Dismissal",ru,e);return{[ou()]:{schema:Bn([ss("target")]),onReceive:(e,o)=>{tu.isOpen(e)&&(tu.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>tu.close(e)),(t=>Rr(e,t.event))))}}}},iu=Bn([xs("fireEventInstead",[ws("event",Tr())]),cs("doReposition")]),lu=e=>{const t=Jn("Reposition",iu,e);return{[nu()]:{onReceive:e=>{tu.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>Rr(e,t.event)))}}}},cu=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},du=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var uu=Object.freeze({__proto__:null,onLoad:cu,onUnload:du,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),mu=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Zr(((o,n)=>{cu(o,e,t)})),Qr(((o,n)=>{du(o,e,t)}))]:[Tl(e,t,cu)];return Pr(o)}});const gu=()=>{const e=Ms(null);return Ea({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},pu=()=>{const e=Ms({}),t=Ms({});return Ea({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>be(e.get(),o).orThunk((()=>be(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};H(o,(e=>{r[e.value]=e,be(e,"meta").each((t=>{be(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var hu=Object.freeze({__proto__:null,memory:gu,dataset:pu,manual:()=>Ea({readState:b}),init:e=>e.store.manager.state(e)});const fu=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var bu=[gs("initialValue"),ss("getFallbackEntry"),ss("getDataKey"),ss("setValue"),Ui("manager",{setValue:fu,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{fu(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:pu})],vu=[ss("getValue"),ws("setValue",b),gs("initialValue"),Ui("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:Ta.init})],yu=[gs("initialValue"),Ui("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:gu})],xu=[Ss("store",{mode:"memory"},Qn("mode",{memory:yu,manual:vu,dataset:bu})),zi("onSetValue"),ws("resetOnDom",!1)];const wu=Bl({fields:xu,name:"representing",active:mu,apis:uu,extra:{setValueFrom:(e,t)=>{const o=wu.getValue(t);wu.setValue(e,o)}},state:hu}),Su=(e,t)=>As(e,{},V(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,ts(o,o,{tag:"option",process:{}},_n((e=>gn("The field: "+o+" is forbidden. "+n))));var o,n})).concat([os("dump",w)])),ku=e=>e.dump,Cu=(e,t)=>({...Ml(t),...e.dump}),Ou=Su,_u=Cu,Tu="placeholder",Eu=Ds([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Au=e=>ve(e,"uiType"),Mu=(e,t,o,n)=>((e,t,o,n)=>Au(o)&&o.uiType===Tu?((e,t,o,n)=>e.exists((e=>e!==o.owner))?Eu.single(!0,x(o)):be(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+ae(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):Eu.single(!1,x(o)))(e,0,o,n).fold(((s,r)=>{const a=Au(o)?r(t,o.config,o.validated):r(t),i=be(a,"components").getOr([]),l=Y(i,(o=>Mu(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(Au(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(w)(e)}return n(t)})),Du=Eu.single,Bu=Eu.multiple,Iu=x(Tu),Fu=Ds([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Ru=ws("factory",{sketch:w}),Nu=ws("schema",[]),Lu=ss("name"),zu=ts("pname","pname",xn((e=>"<alloy."+da(e.name)+">")),zn()),Vu=os("schema",(()=>[gs("preprocess")])),Hu=ws("defaults",x({})),Pu=ws("overrides",x({})),Uu=In([Ru,Nu,Lu,zu,Hu,Pu]),Wu=In([Ru,Nu,Lu,Hu,Pu]),ju=In([Ru,Nu,Lu,zu,Hu,Pu]),Gu=In([Ru,Vu,Lu,ss("unit"),zu,Hu,Pu]),$u=e=>e.fold(A.some,A.none,A.some,A.some),qu=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},Yu=(e,t)=>o=>{const n=Jn("Converting part type",t,o);return e(n)},Xu=Yu(Fu.required,Uu),Ku=Yu(Fu.external,Wu),Ju=Yu(Fu.optional,ju),Zu=Yu(Fu.group,Gu),Qu=x("entirety");var em=Object.freeze({__proto__:null,required:Xu,external:Ku,optional:Ju,group:Zu,asNamedPart:$u,name:qu,asCommon:e=>e.fold(w,w,w,w),original:Qu});const tm=(e,t,o,n)=>vn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),om=(e,t)=>{const o={};return H(t,(t=>{$u(t).each((t=>{const n=nm(e,t.pname);o[t.name]=o=>{const s=Jn("Part: "+t.name+" in "+e,In(t.schema),o);return{...n,config:o,validated:s}}}))})),o},nm=(e,t)=>({uiType:Iu(),owner:e,name:t}),sm=(e,t,o)=>({uiType:Iu(),owner:e,name:t,config:o,validated:{}}),rm=e=>Y(e,(e=>e.fold(A.none,A.some,A.none,A.none).map((e=>ds(e.name,e.schema.concat([Wi(Qu())])))).toArray())),am=e=>V(e,qu),im=(e,t,o)=>((e,t,o)=>{const n={},s={};return H(o,(e=>{e.fold((e=>{n[e.pname]=Du(!0,((t,o,n)=>e.factory.sketch(tm(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=x(e.factory.sketch(tm(t,e,o[Qu()]),o))}),(e=>{n[e.pname]=Du(!1,((t,o,n)=>e.factory.sketch(tm(t,e,o,n))))}),(e=>{n[e.pname]=Bu(!0,((t,o,n)=>{const s=t[e.name];return V(s,(o=>e.factory.sketch(vn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:x(n),externals:x(s)}})(0,t,o),lm=(e,t,o)=>((e,t,o,n)=>{const s=ce(n,((e,t)=>((e,t)=>{let o=!1;return{name:x(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>Y(o,(o=>Mu(e,t,o,n))))(e,t,o,s);return le(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(A.some(e),t,t.components,o),cm=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},dm=(e,t,o)=>cm(e,t,o).getOrDie("Could not find part: "+o),um=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return H(o,(e=>{n[e]=x(r.getByUid(s[e]))})),n},mm=(e,t)=>{const o=e.getSystem();return ce(t.partUids,((e,t)=>x(o.getByUid(e))))},gm=e=>ae(e.partUids),pm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return H(o,(e=>{n[e]=x(r.getByUid(s[e]).getOrDie())})),n},hm=(e,t)=>{const o=am(t);return Is(V(o,(t=>({key:t,value:e+"-"+t}))))},fm=e=>ts("partUids","partUids",Sn((t=>hm(t.uid,e))),zn());var bm=Object.freeze({__proto__:null,generate:om,generateOne:sm,schemas:rm,names:am,substitutes:im,components:lm,defaultUids:hm,defaultUidsSchema:fm,getAllParts:mm,getAllPartNames:gm,getPart:cm,getPartOrDie:dm,getParts:um,getPartsOrDie:pm});const vm=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[ds("parts",e)]:[]).concat([ss("uid"),ws("dom",{}),ws("components",[]),Wi("originalSpec"),ws("debug.sketcher",{})]).concat(t))(n,s);return Jn(e+" [SpecSchema]",Bn(r.concat(t)),o)},ym=(e,t,o,n,s)=>{const r=xm(s),a=rm(o),i=fm(o),l=vm(e,t,r,a,[i]),c=im(0,l,o);return n(l,lm(e,l,c.internals()),r,c.externals())},xm=e=>(e=>ve(e,"uid"))(e)?e:{...e,uid:ba("uid")},wm=Bn([ss("name"),ss("factory"),ss("configFields"),ws("apis",{}),ws("extraApis",{})]),Sm=Bn([ss("name"),ss("factory"),ss("configFields"),ss("partFields"),ws("apis",{}),ws("extraApis",{})]),km=e=>{const t=Jn("Sketcher for "+e.name,wm,e),o=ce(t.apis,_a),n=ce(t.extraApis,((e,t)=>Sa(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=xm(n);return o(vm(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},Cm=e=>{const t=Jn("Sketcher for "+e.name,Sm,e),o=om(t.name,t.partFields),n=ce(t.apis,_a),s=ce(t.extraApis,((e,t)=>Sa(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>ym(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},Om=e=>Ke("input")(e)&&"radio"!==Tt(e,"type")||Ke("textarea")(e);var _m=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const Tm=[ss("find")],Em=Bl({fields:Tm,name:"composing",apis:_m}),Am=["input","button","textarea","select"],Mm=(e,t,o)=>{(t.disabled()?Nm:Lm)(e,t)},Dm=(e,t)=>!0===t.useNative&&R(Am,We(e.element)),Bm=e=>{Ot(e.element,"disabled","disabled")},Im=e=>{Mt(e.element,"disabled")},Fm=e=>{Ot(e.element,"aria-disabled","true")},Rm=e=>{Ot(e.element,"aria-disabled","false")},Nm=(e,t,o)=>{t.disableClass.each((t=>{ja(e.element,t)})),(Dm(e,t)?Bm:Fm)(e),t.onDisabled(e)},Lm=(e,t,o)=>{t.disableClass.each((t=>{$a(e.element,t)})),(Dm(e,t)?Im:Rm)(e),t.onEnabled(e)},zm=(e,t)=>Dm(e,t)?(e=>At(e.element,"disabled"))(e):(e=>"true"===Tt(e.element,"aria-disabled"))(e);var Vm=Object.freeze({__proto__:null,enable:Lm,disable:Nm,isDisabled:zm,onLoad:Mm,set:(e,t,o,n)=>{(n?Nm:Lm)(e,t)}}),Hm=Object.freeze({__proto__:null,exhibit:(e,t)=>Ma({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Pr([Ur(gr(),((t,o)=>zm(t,e))),Tl(e,t,Mm)])}),Pm=[Ts("disabled",T),ws("useNative",!0),gs("disableClass"),zi("onDisabled"),zi("onEnabled")];const Um=Bl({fields:Pm,name:"disabling",active:Hm,apis:Vm}),Wm=(e,t,o,n)=>{const s=td(e.element,"."+t.highlightClass);H(s,(o=>{N(n,(e=>et(e.element,o)))||($a(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),Rr(o,Fr())})))}))},jm=(e,t,o,n)=>{Wm(e,t,0,[n]),Gm(e,t,o,n)||(ja(n.element,t.highlightClass),t.onHighlight(e,n),Rr(n,Ir()))},Gm=(e,t,o,n)=>qa(n.element,t.highlightClass),$m=(e,t,o)=>wi(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),qm=(e,t,o)=>{const n=td(e.element,"."+t.itemClass);return(n.length>0?A.some(n[n.length-1]):A.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},Ym=(e,t,o,n)=>{const s=td(e.element,"."+t.itemClass);return $(s,(e=>qa(e,t.highlightClass))).bind((t=>{const o=tl(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},Xm=(e,t,o)=>{const n=td(e.element,"."+t.itemClass);return we(V(n,(t=>e.getSystem().getByDom(t).toOptional())))};var Km=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>Wm(e,t,0,[]),dehighlight:(e,t,o,n)=>{Gm(e,t,o,n)&&($a(n.element,t.highlightClass),t.onDehighlight(e,n),Rr(n,Fr()))},highlight:jm,highlightFirst:(e,t,o)=>{$m(e,t).each((n=>{jm(e,t,o,n)}))},highlightLast:(e,t,o)=>{qm(e,t).each((n=>{jm(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=td(e.element,"."+t.itemClass);return A.from(s[n]).fold((()=>an.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{jm(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=Xm(e,t);G(s,n).each((n=>{jm(e,t,o,n)}))},isHighlighted:Gm,getHighlighted:(e,t,o)=>wi(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:$m,getLast:qm,getPrevious:(e,t,o)=>Ym(e,t,0,-1),getNext:(e,t,o)=>Ym(e,t,0,1),getCandidates:Xm}),Jm=[ss("highlightClass"),ss("itemClass"),zi("onHighlight"),zi("onDehighlight")];const Zm=Bl({fields:Jm,name:"highlighting",apis:Km}),Qm=[8],eg=[9],tg=[13],og=[27],ng=[32],sg=[37],rg=[38],ag=[39],ig=[40],lg=(e,t,o)=>{const n=K(e.slice(0,t)),s=K(e.slice(t+1));return G(n.concat(s),o)},cg=(e,t,o)=>{const n=K(e.slice(0,t));return G(n,o)},dg=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return G(s.concat(n),o)},ug=(e,t,o)=>{const n=e.slice(t+1);return G(n,o)},mg=e=>t=>{const o=t.raw;return R(e,o.which)},gg=e=>t=>X(e,(e=>e(t))),pg=e=>!0===e.raw.shiftKey,hg=e=>!0===e.raw.ctrlKey,fg=C(pg),bg=(e,t)=>({matches:e,classification:t}),vg=(e,t,o)=>{t.exists((e=>o.exists((t=>et(t,e)))))||Nr(e,Er(),{prevFocus:t,newFocus:o})},yg=()=>{const e=e=>Ul(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);vg(t,n,s)}}},xg=()=>{const e=e=>Zm.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Zm.highlight(t,e)}));const s=e(t);vg(t,n,s)}}};var wg;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(wg||(wg={}));const Sg=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,G(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([ws("focusManager",yg()),Ss("focusInside","onFocus",qn((e=>R(["onFocus","onEnterOrSpace","onApi"],e)?an.value(e):an.error("Invalid value for focusInside")))),Ui("handler",a),Ui("state",t),Ui("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==wg.OnFocusMode?A.none():s(e).map((o=>jr(cr(),((n,s)=>{o(n,e,t),s.stop()})))),i=[jr(Zs(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=mg(ng.concat(tg))(n.event);e.focusInside===wg.OnEnterOrSpaceMode&&r&&Ls(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),jr(Qs(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Pr(a.toArray().concat(i))}};return a},kg=e=>{const t=[gs("onEscape"),gs("onEnter"),ws("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),ws("firstTabstop",0),ws("useTabstopAt",E),gs("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>Si(t,e))).getOr(t);return Gt(o)>0},n=(e,t)=>t.focusManager.get(e).bind((e=>Si(e,t.selector))),s=(e,t,n)=>{((e,t)=>{const n=td(e.element,t.selector),s=U(n,(e=>o(t,e)));return A.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},r=(e,t,s,r)=>{const a=td(e.element,s.selector);return n(e,s).bind((t=>$(a,k(et,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?A.some(!0):A.none()),(t=>(s.focusManager.set(e,t),A.some(!0)))))(e,a,t,s,r)))))},a=(e,t,o)=>{const n=o.cyclic?lg:cg;return r(e,0,o,n)},i=(e,t,o)=>{const n=o.cyclic?dg:ug;return r(e,0,o,n)},l=x([bg(gg([pg,mg(eg)]),a),bg(mg(eg),i),bg(gg([fg,mg(tg)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),c=x([bg(mg(og),((e,t,o)=>o.onEscape.bind((o=>o(e,t))))),bg(mg(eg),((e,t,o)=>n(e,o).filter((e=>!o.useTabstopAt(e))).bind((n=>((e=>(e=>at(e))(e).bind(ut).exists((t=>et(t,e))))(n)?a:i)(e,t,o)))))]);return Sg(t,Ta.init,l,c,(()=>A.some(s)))};var Cg=kg(os("cyclic",T)),Og=kg(os("cyclic",E));const _g=(e,t,o)=>Om(o)&&mg(ng)(t.event)?A.none():((e,t,o)=>(zr(e,o,gr()),A.some(!0)))(e,0,o),Tg=(e,t)=>A.some(!0),Eg=[ws("execute",_g),ws("useSpace",!1),ws("useEnter",!0),ws("useControlEnter",!1),ws("useDown",!1)],Ag=(e,t,o)=>o.execute(e,t,e.element);var Mg=Sg(Eg,Ta.init,((e,t,o,n)=>{const s=o.useSpace&&!Om(e.element)?ng:[],r=o.useEnter?tg:[],a=o.useDown?ig:[],i=s.concat(r).concat(a);return[bg(mg(i),Ag)].concat(o.useControlEnter?[bg(gg([hg,mg(tg)]),Ag)]:[])}),((e,t,o,n)=>o.useSpace&&!Om(e.element)?[bg(mg(ng),Tg)]:[]),(()=>A.none()));const Dg=()=>{const e=ac();return Ea({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var Bg=Object.freeze({__proto__:null,flatgrid:Dg,init:e=>e.state(e)});const Ig=e=>(t,o,n,s)=>{const r=e(t.element);return Lg(r,t,o,n,s)},Fg=(e,t)=>{const o=kc(e,t);return Ig(o)},Rg=(e,t)=>{const o=kc(t,e);return Ig(o)},Ng=e=>(t,o,n,s)=>Lg(e,t,o,n,s),Lg=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),zg=Ng,Vg=Ng,Hg=Ng,Pg=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),Ug=(e,t,o)=>{const n=td(e,o);return((e,o)=>$(e,(e=>et(e,t))).map((t=>({index:t,candidates:e}))))(U(n,Pg))},Wg=(e,t)=>$(e,(e=>et(t,e))),jg=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?A.some(e[n]):A.none()})),Gg=(e,t,o,n,s)=>jg(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=tl(r,s,0,a-1);return A.some({row:t,column:i})})),$g=(e,t,o,n,s)=>jg(e,t,n,((t,r)=>{const a=tl(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=ol(r,0,i-1);return A.some({row:a,column:l})})),qg=[ss("selector"),ws("execute",_g),Vi("onEscape"),ws("captureTab",!1),ji()],Yg=(e,t,o)=>{wi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Xg=e=>(t,o,n,s)=>Ug(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),Kg=(e,t,o)=>o.captureTab?A.some(!0):A.none(),Jg=Xg(((e,t,o,n)=>Gg(e,t,o,n,-1))),Zg=Xg(((e,t,o,n)=>Gg(e,t,o,n,1))),Qg=Xg(((e,t,o,n)=>$g(e,t,o,n,-1))),ep=Xg(((e,t,o,n)=>$g(e,t,o,n,1))),tp=x([bg(mg(sg),Fg(Jg,Zg)),bg(mg(ag),Rg(Jg,Zg)),bg(mg(rg),zg(Qg)),bg(mg(ig),Vg(ep)),bg(gg([pg,mg(eg)]),Kg),bg(gg([fg,mg(eg)]),Kg),bg(mg(ng.concat(tg)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>Si(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),op=x([bg(mg(og),((e,t,o)=>o.onEscape(e,t))),bg(mg(ng),Tg)]);var np=Sg(qg,Dg,tp,op,(()=>A.some(Yg)));const sp=(e,t,o,n,s)=>{const r=(e,t,o)=>s(e,t,n,0,o.length-1,o[t],(t=>{return n=o[t],"button"===We(n)&&"disabled"===Tt(n,"disabled")?r(e,t,o):A.from(o[t]);var n}));return Ug(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},rp=(e,t,o,n)=>sp(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=ol(t+o,n,s);return i===e?A.from(r):a(i)})),ap=(e,t,o,n)=>sp(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=tl(t,o,n,s);return i===e?A.none():a(i)})),ip=[ss("selector"),ws("getInitial",A.none),ws("execute",_g),Vi("onEscape"),ws("executeOnMove",!1),ws("allowVertical",!0),ws("allowHorizontal",!0),ws("cycles",!0)],lp=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>Si(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),cp=(e,t,o)=>{t.getInitial(e).orThunk((()=>wi(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},dp=(e,t,o)=>(o.cycles?ap:rp)(e,o.selector,t,-1),up=(e,t,o)=>(o.cycles?ap:rp)(e,o.selector,t,1),mp=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?lp(t,o,n):A.some(!0))),gp=x([bg(mg(ng),Tg),bg(mg(og),((e,t,o)=>o.onEscape(e,t)))]);var pp=Sg(ip,Ta.init,((e,t,o,n)=>{const s=[...o.allowHorizontal?sg:[]].concat(o.allowVertical?rg:[]),r=[...o.allowHorizontal?ag:[]].concat(o.allowVertical?ig:[]);return[bg(mg(s),mp(Fg(dp,up))),bg(mg(r),mp(Rg(dp,up))),bg(mg(tg),lp),bg(mg(ng),lp)]}),gp,(()=>A.some(cp)));const hp=(e,t,o)=>A.from(e[t]).bind((e=>A.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),fp=(e,t,o,n)=>{const s=e[t].length,r=tl(o,n,0,s-1);return hp(e,t,r)},bp=(e,t,o,n)=>{const s=tl(o,n,0,e.length-1),r=e[s].length,a=ol(t,0,r-1);return hp(e,s,a)},vp=(e,t,o,n)=>{const s=e[t].length,r=ol(o+n,0,s-1);return hp(e,t,r)},yp=(e,t,o,n)=>{const s=ol(o+n,0,e.length-1),r=e[s].length,a=ol(t,0,r-1);return hp(e,s,a)},xp=[ds("selectors",[ss("row"),ss("cell")]),ws("cycles",!0),ws("previousSelector",A.none),ws("execute",_g)],wp=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return wi(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},Sp=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return Si(n,s.selectors.row).bind((e=>{const t=td(e,s.selectors.cell);return Wg(t,n).bind((t=>{const n=td(o,s.selectors.row);return Wg(n,e).bind((e=>{const o=((e,t)=>V(e,(e=>td(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},kp=Sp(((e,t,o)=>fp(e,t,o,-1)),((e,t,o)=>vp(e,t,o,-1))),Cp=Sp(((e,t,o)=>fp(e,t,o,1)),((e,t,o)=>vp(e,t,o,1))),Op=Sp(((e,t,o)=>bp(e,o,t,-1)),((e,t,o)=>yp(e,o,t,-1))),_p=Sp(((e,t,o)=>bp(e,o,t,1)),((e,t,o)=>yp(e,o,t,1))),Tp=x([bg(mg(sg),Fg(kp,Cp)),bg(mg(ag),Rg(kp,Cp)),bg(mg(rg),zg(Op)),bg(mg(ig),Vg(_p)),bg(mg(ng.concat(tg)),((e,t,o)=>Ul(e.element).bind((n=>o.execute(e,t,n)))))]),Ep=x([bg(mg(ng),Tg)]);var Ap=Sg(xp,Ta.init,Tp,Ep,(()=>A.some(wp)));const Mp=[ss("selector"),ws("execute",_g),ws("moveOnTab",!1)],Dp=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),Bp=(e,t,o)=>{wi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Ip=(e,t,o)=>ap(e,o.selector,t,-1),Fp=(e,t,o)=>ap(e,o.selector,t,1),Rp=x([bg(mg(rg),Hg(Ip)),bg(mg(ig),Hg(Fp)),bg(gg([pg,mg(eg)]),((e,t,o,n)=>o.moveOnTab?Hg(Ip)(e,t,o,n):A.none())),bg(gg([fg,mg(eg)]),((e,t,o,n)=>o.moveOnTab?Hg(Fp)(e,t,o,n):A.none())),bg(mg(tg),Dp),bg(mg(ng),Dp)]),Np=x([bg(mg(ng),Tg)]);var Lp=Sg(Mp,Ta.init,Rp,Np,(()=>A.some(Bp)));const zp=[Vi("onSpace"),Vi("onEnter"),Vi("onShiftEnter"),Vi("onLeft"),Vi("onRight"),Vi("onTab"),Vi("onShiftTab"),Vi("onUp"),Vi("onDown"),Vi("onEscape"),ws("stopSpaceKeyup",!1),gs("focusIn")];var Vp=Sg(zp,Ta.init,((e,t,o)=>[bg(mg(ng),o.onSpace),bg(gg([fg,mg(tg)]),o.onEnter),bg(gg([pg,mg(tg)]),o.onShiftEnter),bg(gg([pg,mg(eg)]),o.onShiftTab),bg(gg([fg,mg(eg)]),o.onTab),bg(mg(rg),o.onUp),bg(mg(ig),o.onDown),bg(mg(sg),o.onLeft),bg(mg(ag),o.onRight),bg(mg(ng),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[bg(mg(ng),Tg)]:[],bg(mg(og),o.onEscape)]),(e=>e.focusIn));const Hp=Cg.schema(),Pp=Og.schema(),Up=pp.schema(),Wp=np.schema(),jp=Ap.schema(),Gp=Mg.schema(),$p=Lp.schema(),qp=Vp.schema(),Yp=Fl({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:Hp,cyclic:Pp,flow:Up,flatgrid:Wp,matrix:jp,execution:Gp,menu:$p,special:qp}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ye(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:Bg}),Xp=(e,t)=>{Wl((()=>{((e,t,o)=>{const n=e.components();(e=>{H(e.components(),(e=>Wo(e.element))),Uo(e.element),e.syncComponents()})(e);const s=o(t),r=J(n,s);H(r,(t=>{Dd(t),e.getSystem().removeFromWorld(t)})),H(s,(t=>{Md(t)?Rd(e,t):(e.getSystem().addToWorld(t),Rd(e,t),wt(e.element)&&Bd(t))})),e.syncComponents()})(e,t,(()=>V(t,e.getSystem().build)))}),e.element)},Kp=(e,t)=>{Wl((()=>{((o,n,s)=>{const r=o.components(),a=Y(n,(e=>Oa(e).toArray()));H(r,(e=>{R(a,e)||Fd(e)}));const i=((e,t,o)=>ei(e,t,((t,n)=>ti(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=J(r,i);H(l,(e=>{Md(e)&&Fd(e)})),H(i,(e=>{Md(e)||Id(o,e)})),o.syncComponents()})(e,t)}),e.element)},Jp=(e,t,o,n)=>{Fd(t);const s=ti(e.element,o,n,e.getSystem().buildOrPatch);Id(e,s),e.syncComponents()},Zp=(e,t,o)=>{const n=e.getSystem().build(o);Ld(e,n,t)},Qp=(e,t,o,n)=>{Vd(t),Zp(e,((e,t)=>((e,t,o)=>{dt(e,o).fold((()=>{Ho(e,t)}),(e=>{Lo(e,t)}))})(e,t,o)),n)},eh=(e,t)=>e.components(),th=(e,t,o,n,s)=>{const r=eh(e);return A.from(r[n]).map((o=>(s.fold((()=>Vd(o)),(s=>{(t.reuseDom?Jp:Qp)(e,o,n,s)})),o)))};var oh=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Zp(e,Ho,n)},prepend:(e,t,o,n)=>{Zp(e,Vo,n)},remove:(e,t,o,n)=>{const s=eh(e),r=G(s,(e=>et(n.element,e.element)));r.each(Vd)},replaceAt:th,replaceBy:(e,t,o,n,s)=>{const r=eh(e);return $(r,n).bind((o=>th(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?Kp:Xp)(e,n),contents:eh});const nh=Bl({fields:[_s("reuseDom",!0)],name:"replacing",apis:oh}),sh=(e,t)=>{const o=((e,t)=>{const o=Pr(t);return Bl({fields:[ss("enabled")],name:e,active:{events:x(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:x({}),initialConfig:{},state:Ta}}},rh=(e,t)=>{t.ignore||(zl(e.element),t.onFocus(e))};var ah=Object.freeze({__proto__:null,focus:rh,blur:(e,t)=>{t.ignore||Vl(e.element)},isFocused:e=>Hl(e.element)}),ih=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return Ma(o)},events:e=>Pr([jr(cr(),((t,o)=>{rh(t,e),o.stop()}))].concat(e.stopMousedown?[jr(Gs(),((e,t)=>{t.event.prevent()}))]:[]))}),lh=[zi("onFocus"),ws("stopMousedown",!1),ws("ignore",!1)];const ch=Bl({fields:lh,name:"focusing",active:ih,apis:ah}),dh=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?ja(e.element,t):$a(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},uh=(e,t,o)=>{dh(e,t,o,!o.get())},mh=(e,t,o)=>{dh(e,t,o,t.selected)};var gh=Object.freeze({__proto__:null,onLoad:mh,toggle:uh,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{dh(e,t,o,!0)},off:(e,t,o)=>{dh(e,t,o,!1)},set:dh}),ph=Object.freeze({__proto__:null,exhibit:()=>Ma({}),events:(e,t)=>{const o=(n=e,s=t,r=uh,ta((e=>{r(e,n,s)})));var n,s,r;const a=Tl(e,t,mh);return Pr(q([e.toggleOnExecute?[o]:[],[a]]))}});const hh=(e,t,o)=>{Ot(e.element,"aria-expanded",o)};var fh=[ws("selected",!1),gs("toggleClass"),ws("toggleOnExecute",!0),zi("onToggled"),Ss("aria",{mode:"none"},Qn("mode",{pressed:[ws("syncWithExpanded",!1),Ui("update",((e,t,o)=>{Ot(e.element,"aria-pressed",o),t.syncWithExpanded&&hh(e,0,o)}))],checked:[Ui("update",((e,t,o)=>{Ot(e.element,"aria-checked",o)}))],expanded:[Ui("update",hh)],selected:[Ui("update",((e,t,o)=>{Ot(e.element,"aria-selected",o)}))],none:[Ui("update",b)]}))];const bh=Bl({fields:fh,name:"toggling",active:ph,apis:gh,state:(!1,{init:()=>{const e=Ms(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const vh=()=>{const e=(e,t)=>{t.stop(),Lr(e)};return[jr(or(),e),jr(hr(),e),Xr(Ps()),Xr(Gs())]},yh=e=>Pr(q([e.map((e=>ta(((t,o)=>{e(t),o.stop()})))).toArray(),vh()])),xh="alloy.item-hover",wh="alloy.item-focus",Sh="alloy.item-toggled",kh=e=>{(Ul(e.element).isNone()||ch.isFocused(e))&&(ch.isFocused(e)||ch.focus(e),Nr(e,xh,{item:e}))},Ch=e=>{Nr(e,wh,{item:e})},Oh=x(xh),_h=x(wh),Th=x(Sh),Eh=e=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem"),Ah=[ss("data"),ss("components"),ss("dom"),ws("hasSubmenu",!1),gs("toggling"),Ou("itemBehaviours",[bh,ch,Yp,wu]),ws("ignoreFocus",!1),ws("domModification",{}),Ui("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:Eh(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:_u(e.itemBehaviours,[e.toggling.fold(bh.revoke,(e=>bh.config((e=>({aria:{mode:"checked"},...ge(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Nr(e,Sh,{item:e,state:t})})(t,o)}}))(e)))),ch.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{Ch(e)}}),Yp.config({mode:"execution"}),wu.config({store:{mode:"memory",initialValue:e.data}}),sh("item-type-events",[...vh(),jr(Xs(),kh),jr(pr(),ch.focus)])]),components:e.components,eventOrder:e.eventOrder}))),ws("eventOrder",{})],Mh=[ss("dom"),ss("components"),Ui("builder",(e=>({dom:e.dom,components:e.components,events:Pr([Kr(pr())])})))],Dh=x("item-widget"),Bh=x([Xu({name:"widget",overrides:e=>({behaviours:Ml([wu.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),Ih=[ss("uid"),ss("data"),ss("components"),ss("dom"),ws("autofocus",!1),ws("ignoreFocus",!1),Ou("widgetBehaviours",[wu,ch,Yp]),ws("domModification",{}),fm(Bh()),Ui("builder",(e=>{const t=im(Dh(),e,Bh()),o=lm(Dh(),e,t.internals()),n=t=>cm(t,e,"widget").map((e=>(Yp.focusIn(e),e))),s=(t,o)=>Om(o.event.target)?A.none():e.autofocus?(o.setSource(t.element),A.none()):A.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Pr([ta(((e,t)=>{n(e).each((e=>{t.stop()}))})),jr(Xs(),kh),jr(pr(),((t,o)=>{e.autofocus?n(t):ch.focus(t)}))]),behaviours:_u(e.widgetBehaviours,[wu.config({store:{mode:"memory",initialValue:e.data}}),ch.config({ignore:e.ignoreFocus,onFocus:e=>{Ch(e)}}),Yp.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:Rl(),onLeft:s,onRight:s,onEscape:(t,o)=>ch.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),A.none()):A.none():(ch.focus(t),A.some(!0))})])}}))],Fh=Qn("type",{widget:Ih,item:Ah,separator:Mh}),Rh=x([Zu({factory:{sketch:e=>{const t=Jn("menu.spec item",Fh,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>ve(t,"uid")?t:{...t,uid:ba("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),Nh=x([ss("value"),ss("items"),ss("dom"),ss("components"),ws("eventOrder",{}),Su("menuBehaviours",[Zm,wu,Em,Yp]),Ss("movement",{mode:"menu",moveOnTab:!0},Qn("mode",{grid:[ji(),Ui("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[Ui("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),ss("rowSelector"),ws("previousSelector",A.none)],menu:[ws("moveOnTab",!0),Ui("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),rs("markers",Ii()),ws("fakeFocus",!1),ws("focusManager",yg()),zi("onHighlight"),zi("onDehighlight")]),Lh=x("alloy.menu-focus"),zh=Cm({name:"Menu",configFields:Nh(),partFields:Rh(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Cu(e.menuBehaviours,[Zm.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),wu.config({store:{mode:"memory",initialValue:e.value}}),Em.config({find:A.some}),Yp.config(e.movement.config(e,e.movement))]),events:Pr([jr(_h(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Zm.highlight(e,o),t.stop(),Nr(e,Lh(),{menu:e,item:o})}))})),jr(Oh(),((e,t)=>{const o=t.event.item;Zm.highlight(e,o)})),jr(Th(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===Tt(o.element,"role")&&((e,t)=>{const o=td(e.element,'[role="menuitemradio"][aria-checked="true"]');H(o,(o=>{et(o,t.element)||e.getSystem().getByDom(o).each((e=>{bh.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,domModification:{attributes:{role:"menu"}}})}),Vh=(e,t,o,n)=>be(o,n).bind((n=>be(e,n).bind((n=>{const s=Vh(e,t,o,n);return A.some([n].concat(s))})))).getOr([]),Hh=e=>"prepared"===e.type?A.some(e.menu):A.none(),Ph=()=>{const e=Ms({}),t=Ms({}),o=Ms({}),n=ac(),s=Ms({}),r=e=>a(e).bind(Hh),a=e=>be(t.get(),e),i=t=>be(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};le(e,((e,t)=>{H(e,(e=>{o[e]=t}))}));const n=t,s=de(t,((e,t)=>({k:e,v:t}))),r=ce(s,((e,t)=>[t].concat(Vh(o,n,s,t))));return ce(o,(e=>be(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>be(e.get(),t).map((e=>{const n=be(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>be(o.get(),e),collapse:e=>be(o.get(),e).bind((e=>e.length>1?A.some(e.slice(1)):A.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return J(ae(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=U(i(t).toArray(),(e=>r(e).isSome()));return be(o.get(),t).bind((t=>{const o=K(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return A.none();t.push(n.getOrDie())}return A.some(t)})(Y(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>he(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>xe(n.get(),t)?[]:[A.none()]),(e=>[A.some(e)])))))}))}}},Uh=Hh,Wh=da("tiered-menu-item-highlight"),jh=da("tiered-menu-item-dehighlight");var Gh;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(Gh||(Gh={}));const $h=x("collapse-item"),qh=km({name:"TieredMenu",configFields:[Pi("onExecute"),Pi("onEscape"),Hi("onOpenMenu"),Hi("onOpenSubmenu"),zi("onRepositionMenu"),zi("onCollapseMenu"),ws("highlightOnOpen",Gh.HighlightMenuAndItem),ds("data",[ss("primary"),ss("menus"),ss("expansions")]),ws("fakeFocus",!1),zi("onHighlightItem"),zi("onDehighlightItem"),zi("onHover"),Ri(),ss("dom"),ws("navigateOnHover",!0),ws("stayInDom",!1),Su("tmenuBehaviours",[Yp,Zm,Em,nh]),ws("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=ac(),n=Ph(),s=e=>wu.getValue(e).value,r=t=>ce(e.data.menus,((e,t)=>Y(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Zm.highlight,i=(t,o)=>{a(t,o),Zm.getHighlighted(o).orThunk((()=>Zm.getFirst(o))).each((n=>{e.fakeFocus?Zm.highlight(o,n):zr(t,n.element,pr())}))},l=(e,t)=>we(V(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?A.some(e.menu):A.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));H(s,(o=>{Xa(o.element,[e.markers.backgroundMenu]),e.stayInDom||nh.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=td(t.element,`.${e.markers.item}`),a=U(r,(e=>"true"===Tt(e,"aria-haspopup")));return H(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);le(r,((e,t)=>{const o=R(n,t);Ot(e.element,"aria-expanded",o)}))},u=(t,o,n)=>A.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return A.none();{const r=s.menu,a=l(o,n.slice(1));return H(a,(t=>{ja(t.element,e.markers.backgroundMenu)})),wt(r.element)||nh.append(t,ui(r)),Xa(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),A.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(Um)&&Um.isDisabled(o))return A.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),A.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return wt(l.element)||nh.append(t,ui(l)),e.onOpenSubmenu(t,o,l,K(s)),r===m.HighlightSubmenu?(Zm.highlightFirst(l),u(t,n,s)):(Zm.dehighlightAll(l),A.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>Si(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(E))))),f=Pr([jr(Lh(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Zm.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),ta(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Zr(((t,o)=>{(t=>{const o=((t,o,n)=>ce(n,((n,s)=>{const r=()=>zh.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Nr(e,Wh,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Nr(e,jh,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?xg():yg()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{nh.append(t,ui(o)),e.onOpenMenu(t,o),e.highlightOnOpen===Gh.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===Gh.HighlightJustMenu&&a(t,o)}))})),jr(Wh,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),jr(jh,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[jr(Oh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Zm.getHighlighted(e).bind(Zm.getHighlighted),y={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=fe(n.getMenus()),r=we(V(o,Uh));return n.getTriggeringPath(t,(e=>((e,t,o)=>re(t,(e=>{if(!e.getSystem().isConnected())return A.none();const t=Zm.getCandidates(e);return G(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>A.from(e.components()[0]).filter((e=>"menu"===Tt(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Cu(e.tmenuBehaviours,[Yp.config({mode:"special",onRight:h(((e,t)=>Om(t.element)?A.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>Om(t.element)?A.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{zr(e,t.element,pr())}))}}),Zm.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),Em.config({find:e=>Zm.getHighlighted(e)}),nh.config({})]),eventOrder:e.eventOrder,apis:y,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:Bs(e,t),expansions:{}}),collapseItem:e=>({value:da($h()),meta:{text:e}})}}),Yh=km({name:"InlineView",configFields:[ss("lazySink"),zi("onShow"),zi("onHide"),vs("onEscape"),Su("inlineBehaviours",[tu,wu,Nl]),xs("fireDismissalEventInstead",[ws("event",_r())]),xs("fireRepositionEventInstead",[ws("event",Tr())]),ws("getRelated",A.none),ws("isExtraPart",T),ws("eventOrder",A.none)],factory:(e,t)=>{const o=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();tu.openWhileCloaked(t,o,(()=>Ad.positionWithinBounds(r,t,n,s()))),wu.setValue(t,A.some({mode:"position",config:n,getBounds:s}))},n=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>kl(),onRtl:()=>Cl()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return qh.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(tu.close(t),e.onEscape.map((e=>e(t))),A.some(!0)),onExecute:()=>A.some(!0),onOpenMenu:(e,t)=>{Ad.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();Ad.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();Ad.positionWithinBounds(a,t,o,s()),H(n,(e=>{const t=i(e.triggeringPath);Ad.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);tu.open(t,r),wu.setValue(t,A.some({mode:"menu",menu:r}))},s=t=>{tu.isOpen(t)&&wu.getValue(t).each((o=>{switch(o.mode){case"menu":tu.getState(t).each(qh.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();Ad.positionWithinBounds(n,t,o.config,o.getBounds())}}))},r={setContent:(e,t)=>{tu.setContent(e,t)},showAt:(e,t,n)=>{const s=A.none;o(e,t,n,s)},showWithinBounds:o,showMenuAt:(e,t,o)=>{n(e,t,o,A.none)},showMenuWithinBounds:n,hide:e=>{tu.isOpen(e)&&(wu.setValue(e,A.none()),tu.close(e))},getContent:e=>tu.getState(e),reposition:s,isOpen:tu.isOpen};return{uid:e.uid,dom:e.dom,behaviours:Cu(e.inlineBehaviours,[tu.config({isPartOf:(t,o,n)=>Oi(o,n)||((t,o)=>e.getRelated(t).exists((e=>Oi(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),wu.config({store:{mode:"memory",initialValue:A.none()}}),Nl.config({channels:{...au({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...lu({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:r}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var Xh=tinymce.util.Tools.resolve("tinymce.util.Delay");const Kh=km({name:"Button",factory:e=>{const t=yh(e.action),o=e.dom.tag,n=t=>be(e.dom,"attributes").bind((e=>be(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:_u(e.buttonBehaviours,[ch.config({}),Yp.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:e.role.getOr(n("role").getOr("button"))}},eventOrder:e.eventOrder}},configFields:[ws("uid",void 0),ss("dom"),ws("components",[]),Ou("buttonBehaviours",[ch,Yp]),gs("action"),gs("role"),ws("eventOrder",{})]}),Jh=e=>{const t=Re(e),o=ct(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return j(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:oa(t)};return{tag:We(t),classes:s,attributes:n,...r}},Zh=e=>{const t=(e=>void 0!==e.uid)(e)&&ye(e,"uid")?e.uid:ba("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}},{entries:Qh,setPrototypeOf:ef,isFrozen:tf,getPrototypeOf:of,getOwnPropertyDescriptor:nf}=Object;let{freeze:sf,seal:rf,create:af}=Object,{apply:lf,construct:cf}="undefined"!=typeof Reflect&&Reflect;lf||(lf=function(e,t,o){return e.apply(t,o)}),sf||(sf=function(e){return e}),rf||(rf=function(e){return e}),cf||(cf=function(e,t){return new e(...t)});const df=Sf(Array.prototype.forEach),uf=Sf(Array.prototype.pop),mf=Sf(Array.prototype.push),gf=Sf(String.prototype.toLowerCase),pf=Sf(String.prototype.toString),hf=Sf(String.prototype.match),ff=Sf(String.prototype.replace),bf=Sf(String.prototype.indexOf),vf=Sf(String.prototype.trim),yf=Sf(RegExp.prototype.test),xf=(wf=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return cf(wf,t)});var wf;function Sf(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),s=1;s<o;s++)n[s-1]=arguments[s];return lf(e,t,n)}}function kf(e,t,o){var n;o=null!==(n=o)&&void 0!==n?n:gf,ef&&ef(e,null);let s=t.length;for(;s--;){let n=t[s];if("string"==typeof n){const e=o(n);e!==n&&(tf(t)||(t[s]=e),n=e)}e[n]=!0}return e}function Cf(e){const t=af(null);for(const[o,n]of Qh(e))t[o]=n;return t}function Of(e,t){for(;null!==e;){const o=nf(e,t);if(o){if(o.get)return Sf(o.get);if("function"==typeof o.value)return Sf(o.value)}e=of(e)}return function(e){return console.warn("fallback value for",e),null}}const _f=sf(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Tf=sf(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ef=sf(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Af=sf(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Mf=sf(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Df=sf(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Bf=sf(["#text"]),If=sf(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Ff=sf(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Rf=sf(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Nf=sf(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Lf=rf(/\{\{[\w\W]*|[\w\W]*\}\}/gm),zf=rf(/<%[\w\W]*|[\w\W]*%>/gm),Vf=rf(/\${[\w\W]*}/gm),Hf=rf(/^data-[\-\w.\u00B7-\uFFFF]/),Pf=rf(/^aria-[\-\w]+$/),Uf=rf(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Wf=rf(/^(?:\w+script|data):/i),jf=rf(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Gf=rf(/^html$/i);var $f=Object.freeze({__proto__:null,MUSTACHE_EXPR:Lf,ERB_EXPR:zf,TMPLIT_EXPR:Vf,DATA_ATTR:Hf,ARIA_ATTR:Pf,IS_ALLOWED_URI:Uf,IS_SCRIPT_OR_DATA:Wf,ATTR_WHITESPACE:jf,DOCTYPE_NAME:Gf});const qf=()=>"undefined"==typeof window?null:window;var Yf=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:qf();const o=t=>e(t);if(o.version="3.0.5",o.removed=[],!t||!t.document||9!==t.document.nodeType)return o.isSupported=!1,o;const n=t.document,s=n.currentScript;let{document:r}=t;const{DocumentFragment:a,HTMLTemplateElement:i,Node:l,Element:c,NodeFilter:d,NamedNodeMap:u=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:m,DOMParser:g,trustedTypes:p}=t,h=c.prototype,f=Of(h,"cloneNode"),b=Of(h,"nextSibling"),v=Of(h,"childNodes"),y=Of(h,"parentNode");if("function"==typeof i){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let x,w="";const{implementation:S,createNodeIterator:k,createDocumentFragment:C,getElementsByTagName:O}=r,{importNode:_}=n;let T={};o.isSupported="function"==typeof Qh&&"function"==typeof y&&S&&void 0!==S.createHTMLDocument;const{MUSTACHE_EXPR:E,ERB_EXPR:A,TMPLIT_EXPR:M,DATA_ATTR:D,ARIA_ATTR:B,IS_SCRIPT_OR_DATA:I,ATTR_WHITESPACE:F}=$f;let{IS_ALLOWED_URI:R}=$f,N=null;const L=kf({},[..._f,...Tf,...Ef,...Mf,...Bf]);let z=null;const V=kf({},[...If,...Ff,...Rf,...Nf]);let H=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),P=null,U=null,W=!0,j=!0,G=!1,$=!0,q=!1,Y=!1,X=!1,K=!1,J=!1,Z=!1,Q=!1,ee=!0,te=!1,oe=!0,ne=!1,se={},re=null;const ae=kf({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ie=null;const le=kf({},["audio","video","img","source","image","track"]);let ce=null;const de=kf({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ue="http://www.w3.org/1998/Math/MathML",me="http://www.w3.org/2000/svg",ge="http://www.w3.org/1999/xhtml";let pe=ge,he=!1,fe=null;const be=kf({},[ue,me,ge],pf);let ve;const ye=["application/xhtml+xml","text/html"];let xe,we=null;const Se=r.createElement("form"),ke=function(e){return e instanceof RegExp||e instanceof Function},Ce=function(e){if(!we||we!==e){if(e&&"object"==typeof e||(e={}),e=Cf(e),ve=ve=-1===ye.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,xe="application/xhtml+xml"===ve?pf:gf,N="ALLOWED_TAGS"in e?kf({},e.ALLOWED_TAGS,xe):L,z="ALLOWED_ATTR"in e?kf({},e.ALLOWED_ATTR,xe):V,fe="ALLOWED_NAMESPACES"in e?kf({},e.ALLOWED_NAMESPACES,pf):be,ce="ADD_URI_SAFE_ATTR"in e?kf(Cf(de),e.ADD_URI_SAFE_ATTR,xe):de,ie="ADD_DATA_URI_TAGS"in e?kf(Cf(le),e.ADD_DATA_URI_TAGS,xe):le,re="FORBID_CONTENTS"in e?kf({},e.FORBID_CONTENTS,xe):ae,P="FORBID_TAGS"in e?kf({},e.FORBID_TAGS,xe):{},U="FORBID_ATTR"in e?kf({},e.FORBID_ATTR,xe):{},se="USE_PROFILES"in e&&e.USE_PROFILES,W=!1!==e.ALLOW_ARIA_ATTR,j=!1!==e.ALLOW_DATA_ATTR,G=e.ALLOW_UNKNOWN_PROTOCOLS||!1,$=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,q=e.SAFE_FOR_TEMPLATES||!1,Y=e.WHOLE_DOCUMENT||!1,J=e.RETURN_DOM||!1,Z=e.RETURN_DOM_FRAGMENT||!1,Q=e.RETURN_TRUSTED_TYPE||!1,K=e.FORCE_BODY||!1,ee=!1!==e.SANITIZE_DOM,te=e.SANITIZE_NAMED_PROPS||!1,oe=!1!==e.KEEP_CONTENT,ne=e.IN_PLACE||!1,R=e.ALLOWED_URI_REGEXP||Uf,pe=e.NAMESPACE||ge,H=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ke(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(H.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ke(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(H.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(H.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),q&&(j=!1),Z&&(J=!0),se&&(N=kf({},[...Bf]),z=[],!0===se.html&&(kf(N,_f),kf(z,If)),!0===se.svg&&(kf(N,Tf),kf(z,Ff),kf(z,Nf)),!0===se.svgFilters&&(kf(N,Ef),kf(z,Ff),kf(z,Nf)),!0===se.mathMl&&(kf(N,Mf),kf(z,Rf),kf(z,Nf))),e.ADD_TAGS&&(N===L&&(N=Cf(N)),kf(N,e.ADD_TAGS,xe)),e.ADD_ATTR&&(z===V&&(z=Cf(z)),kf(z,e.ADD_ATTR,xe)),e.ADD_URI_SAFE_ATTR&&kf(ce,e.ADD_URI_SAFE_ATTR,xe),e.FORBID_CONTENTS&&(re===ae&&(re=Cf(re)),kf(re,e.FORBID_CONTENTS,xe)),oe&&(N["#text"]=!0),Y&&kf(N,["html","head","body"]),N.table&&(kf(N,["tbody"]),delete P.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw xf('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw xf('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');x=e.TRUSTED_TYPES_POLICY,w=x.createHTML("")}else void 0===x&&(x=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let o=null;const n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(o=t.getAttribute(n));const s="dompurify"+(o?"#"+o:"");try{return e.createPolicy(s,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(p,s)),null!==x&&"string"==typeof w&&(w=x.createHTML(""));sf&&sf(e),we=e}},Oe=kf({},["mi","mo","mn","ms","mtext"]),_e=kf({},["foreignobject","desc","title","annotation-xml"]),Te=kf({},["title","style","font","a","script"]),Ee=kf({},Tf);kf(Ee,Ef),kf(Ee,Af);const Ae=kf({},Mf);kf(Ae,Df);const Me=function(e){mf(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){e.remove()}},De=function(e,t){try{mf(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){mf(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!z[e])if(J||Z)try{Me(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Be=function(e){let t,o;if(K)e="<remove></remove>"+e;else{const t=hf(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===ve&&pe===ge&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const n=x?x.createHTML(e):e;if(pe===ge)try{t=(new g).parseFromString(n,ve)}catch(e){}if(!t||!t.documentElement){t=S.createDocument(pe,"template",null);try{t.documentElement.innerHTML=he?w:n}catch(e){}}const s=t.body||t.documentElement;return e&&o&&s.insertBefore(r.createTextNode(o),s.childNodes[0]||null),pe===ge?O.call(t,Y?"html":"body")[0]:Y?t.documentElement:s},Ie=function(e){return k.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT,null,!1)},Fe=function(e){return"object"==typeof l?e instanceof l:e&&"object"==typeof e&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Re=function(e,t,n){T[e]&&df(T[e],(e=>{e.call(o,t,n,we)}))},Ne=function(e){let t;if(Re("beforeSanitizeElements",e,null),(n=e)instanceof m&&("string"!=typeof n.nodeName||"string"!=typeof n.textContent||"function"!=typeof n.removeChild||!(n.attributes instanceof u)||"function"!=typeof n.removeAttribute||"function"!=typeof n.setAttribute||"string"!=typeof n.namespaceURI||"function"!=typeof n.insertBefore||"function"!=typeof n.hasChildNodes))return Me(e),!0;var n;const s=xe(e.nodeName);if(Re("uponSanitizeElement",e,{tagName:s,allowedTags:N}),e.hasChildNodes()&&!Fe(e.firstElementChild)&&(!Fe(e.content)||!Fe(e.content.firstElementChild))&&yf(/<[/\w]/g,e.innerHTML)&&yf(/<[/\w]/g,e.textContent))return Me(e),!0;if(!N[s]||P[s]){if(!P[s]&&ze(s)){if(H.tagNameCheck instanceof RegExp&&yf(H.tagNameCheck,s))return!1;if(H.tagNameCheck instanceof Function&&H.tagNameCheck(s))return!1}if(oe&&!re[s]){const t=y(e)||e.parentNode,o=v(e)||e.childNodes;if(o&&t)for(let n=o.length-1;n>=0;--n)t.insertBefore(f(o[n],!0),b(e))}return Me(e),!0}return e instanceof c&&!function(e){let t=y(e);t&&t.tagName||(t={namespaceURI:pe,tagName:"template"});const o=gf(e.tagName),n=gf(t.tagName);return!!fe[e.namespaceURI]&&(e.namespaceURI===me?t.namespaceURI===ge?"svg"===o:t.namespaceURI===ue?"svg"===o&&("annotation-xml"===n||Oe[n]):Boolean(Ee[o]):e.namespaceURI===ue?t.namespaceURI===ge?"math"===o:t.namespaceURI===me?"math"===o&&_e[n]:Boolean(Ae[o]):e.namespaceURI===ge?!(t.namespaceURI===me&&!_e[n])&&!(t.namespaceURI===ue&&!Oe[n])&&!Ae[o]&&(Te[o]||!Ee[o]):!("application/xhtml+xml"!==ve||!fe[e.namespaceURI]))}(e)?(Me(e),!0):"noscript"!==s&&"noembed"!==s&&"noframes"!==s||!yf(/<\/no(script|embed|frames)/i,e.innerHTML)?(q&&3===e.nodeType&&(t=e.textContent,t=ff(t,E," "),t=ff(t,A," "),t=ff(t,M," "),e.textContent!==t&&(mf(o.removed,{element:e.cloneNode()}),e.textContent=t)),Re("afterSanitizeElements",e,null),!1):(Me(e),!0)},Le=function(e,t,o){if(ee&&("id"===t||"name"===t)&&(o in r||o in Se))return!1;if(j&&!U[t]&&yf(D,t));else if(W&&yf(B,t));else if(!z[t]||U[t]){if(!(ze(e)&&(H.tagNameCheck instanceof RegExp&&yf(H.tagNameCheck,e)||H.tagNameCheck instanceof Function&&H.tagNameCheck(e))&&(H.attributeNameCheck instanceof RegExp&&yf(H.attributeNameCheck,t)||H.attributeNameCheck instanceof Function&&H.attributeNameCheck(t))||"is"===t&&H.allowCustomizedBuiltInElements&&(H.tagNameCheck instanceof RegExp&&yf(H.tagNameCheck,o)||H.tagNameCheck instanceof Function&&H.tagNameCheck(o))))return!1}else if(ce[t]);else if(yf(R,ff(o,F,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==bf(o,"data:")||!ie[e])if(G&&!yf(I,ff(o,F,"")));else if(o)return!1;return!0},ze=function(e){return e.indexOf("-")>0},Ve=function(e){let t,o,n,s;Re("beforeSanitizeAttributes",e,null);const{attributes:r}=e;if(!r)return;const a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:z};for(s=r.length;s--;){t=r[s];const{name:i,namespaceURI:l}=t;o="value"===i?t.value:vf(t.value);const c=o;if(n=xe(i),a.attrName=n,a.attrValue=o,a.keepAttr=!0,a.forceKeepAttr=void 0,Re("uponSanitizeAttribute",e,a),o=a.attrValue,a.forceKeepAttr)continue;if(!a.keepAttr){De(i,e);continue}if(!$&&yf(/\/>/i,o)){De(i,e);continue}q&&(o=ff(o,E," "),o=ff(o,A," "),o=ff(o,M," "));const d=xe(e.nodeName);if(Le(d,n,o)){if(!te||"id"!==n&&"name"!==n||(De(i,e),o="user-content-"+o),x&&"object"==typeof p&&"function"==typeof p.getAttributeType)if(l);else switch(p.getAttributeType(d,n)){case"TrustedHTML":o=x.createHTML(o);break;case"TrustedScriptURL":o=x.createScriptURL(o)}if(o!==c)try{l?e.setAttributeNS(l,i,o):e.setAttribute(i,o)}catch(t){De(i,e)}}else De(i,e)}Re("afterSanitizeAttributes",e,null)},He=function e(t){let o;const n=Ie(t);for(Re("beforeSanitizeShadowDOM",t,null);o=n.nextNode();)Re("uponSanitizeShadowNode",o,null),Ne(o)||(o.content instanceof a&&e(o.content),Ve(o));Re("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(e){let t,s,r,i,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(he=!e,he&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Fe(e)){if("function"!=typeof e.toString)throw xf("toString is not a function");if("string"!=typeof(e=e.toString()))throw xf("dirty is not a string, aborting")}if(!o.isSupported)return e;if(X||Ce(c),o.removed=[],"string"==typeof e&&(ne=!1),ne){if(e.nodeName){const t=xe(e.nodeName);if(!N[t]||P[t])throw xf("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)t=Be("\x3c!----\x3e"),s=t.ownerDocument.importNode(e,!0),1===s.nodeType&&"BODY"===s.nodeName||"HTML"===s.nodeName?t=s:t.appendChild(s);else{if(!J&&!q&&!Y&&-1===e.indexOf("<"))return x&&Q?x.createHTML(e):e;if(t=Be(e),!t)return J?null:Q?w:""}t&&K&&Me(t.firstChild);const d=Ie(ne?e:t);for(;r=d.nextNode();)Ne(r)||(r.content instanceof a&&He(r.content),Ve(r));if(ne)return e;if(J){if(Z)for(i=C.call(t.ownerDocument);t.firstChild;)i.appendChild(t.firstChild);else i=t;return(z.shadowroot||z.shadowrootmode)&&(i=_.call(n,i,!0)),i}let u=Y?t.outerHTML:t.innerHTML;return Y&&N["!doctype"]&&t.ownerDocument&&t.ownerDocument.doctype&&t.ownerDocument.doctype.name&&yf(Gf,t.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+t.ownerDocument.doctype.name+">\n"+u),q&&(u=ff(u,E," "),u=ff(u,A," "),u=ff(u,M," ")),x&&Q?x.createHTML(u):u},o.setConfig=function(e){Ce(e),X=!0},o.clearConfig=function(){we=null,X=!1},o.isValidAttribute=function(e,t,o){we||Ce({});const n=xe(e),s=xe(t);return Le(n,s,o)},o.addHook=function(e,t){"function"==typeof t&&(T[e]=T[e]||[],mf(T[e],t))},o.removeHook=function(e){if(T[e])return uf(T[e])},o.removeHooks=function(e){T[e]&&(T[e]=[])},o.removeAllHooks=function(){T={}},o}();const Xf=e=>Yf().sanitize(e);var Kf=tinymce.util.Tools.resolve("tinymce.util.I18n");const Jf={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},Zf="temporary-placeholder",Qf=e=>()=>be(e,Zf).getOr("!not found!"),eb=(e,t)=>{const o=e.toLowerCase();if(Kf.isRtl()){const e=((e,t)=>Ae(e,t)?e:((e,t)=>e+t)(e,t))(o,"-rtl");return ve(t,e)?e:o}return o},tb=(e,t)=>be(t,eb(e,t)),ob=(e,t)=>{const o=t();return tb(e,o).getOrThunk(Qf(o))},nb=()=>sh("add-focusable",[Zr((e=>{xi(e.element,"svg").each((e=>Ot(e,"focusable","false")))}))]),sb=(e,t,o,n)=>{var s,r;const a=(e=>!!Kf.isRtl()&&ve(Jf,e))(t)?["tox-icon--flip"]:[],i=be(o,eb(t,o)).or(n).getOrThunk(Qf(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:Ml([...null!==(r=e.behaviours)&&void 0!==r?r:[],nb()])}},rb=(e,t,o,n=A.none())=>sb(t,e,o(),n),ab={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},ib=km({name:"Notification",factory:e=>{const t=Zh({dom:Jh(`<p>${Xf(e.translationProvider(e.text))}</p>`),behaviours:Ml([nh.config({})])}),o=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),n=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),s=Zh({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(0)]},n(0)],behaviours:Ml([nh.config({})])}),r={updateProgress:(e,t)=>{e.getSystem().isConnected()&&s.getOpt(e).each((e=>{nh.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(t)]},n(t)])}))},updateText:(e,o)=>{if(e.getSystem().isConnected()){const n=t.get(e);nh.set(n,[ai(o)])}}},a=q([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>A.from(ab[e]))).toArray()]),i=Zh(Kh.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[rb("close",{tag:"span",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:t=>{e.onAction(t)}})),l=((e,t,o)=>{const n=o(),s=G(e,(e=>ve(n,eb(e,n))));return sb({tag:"div",classes:["tox-notification__icon"]},s.getOr(Zf),n,A.none())})(a,0,e.iconProvider),c=[l,{dom:{tag:"div",classes:["tox-notification__body"]},components:[t.asSpec()],behaviours:Ml([nh.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:Ml([ch.config({}),sh("notification-events",[jr(Ks(),(e=>{i.getOpt(e).each(ch.focus)}))])]),components:c.concat(e.progress?[s.asSpec()]:[]).concat([i.asSpec()]),apis:r}},configFields:[gs("level"),ss("progress"),gs("icon"),ss("onAction"),ss("text"),ss("iconProvider"),ss("translationProvider")],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var lb,cb,db=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),ub=tinymce.util.Tools.resolve("tinymce.EditorManager"),mb=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(lb||(lb={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(cb||(cb={}));const gb=e=>t=>t.options.get(e),pb=e=>t=>A.from(e(t)),hb=e=>{const t=mb.deviceType.isPhone(),o=mb.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:db.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("font_size_input_default_unit",{processor:"string",default:"pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),L(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:cb.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("ui_mode",{processor:"string",default:"combined"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!mb.deviceType.isTouch()}),n("sidebar_show",{processor:"string"}),n("help_accessibility",{processor:"boolean",default:e.hasPlugin("help")}),n("default_font_stack",{processor:"string[]",default:[]})},fb=gb("readonly"),bb=gb("height"),vb=gb("width"),yb=pb(gb("min_width")),xb=pb(gb("min_height")),wb=pb(gb("max_width")),Sb=pb(gb("max_height")),kb=pb(gb("style_formats")),Cb=gb("style_formats_merge"),Ob=gb("style_formats_autohide"),_b=gb("content_langs"),Tb=gb("removed_menuitems"),Eb=gb("toolbar_mode"),Ab=gb("toolbar_groups"),Mb=gb("toolbar_location"),Db=gb("fixed_toolbar_container"),Bb=gb("fixed_toolbar_container_target"),Ib=gb("toolbar_persist"),Fb=gb("toolbar_sticky_offset"),Rb=gb("menubar"),Nb=gb("toolbar"),Lb=gb("file_picker_callback"),zb=gb("file_picker_validator_handler"),Vb=gb("font_size_input_default_unit"),Hb=gb("file_picker_types"),Pb=gb("typeahead_urls"),Ub=gb("anchor_top"),Wb=gb("anchor_bottom"),jb=gb("draggable_modal"),Gb=gb("statusbar"),$b=gb("elementpath"),qb=gb("branding"),Yb=gb("resize"),Xb=gb("paste_as_text"),Kb=gb("sidebar_show"),Jb=gb("promotion"),Zb=gb("help_accessibility"),Qb=gb("default_font_stack"),ev=e=>!1===e.options.get("skin"),tv=e=>!1!==e.options.get("menubar"),ov=e=>{const t=e.options.get("skin_url");if(ev(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return ub.baseURL+"/skins/ui/"+t}},nv=e=>A.from(e.options.get("skin_url")),sv=e=>e.options.get("line_height_formats").split(" "),rv=e=>{const t=Nb(e),o=r(t),n=l(t)&&t.length>0;return!iv(e)&&(n||o||!0===t)},av=e=>{const t=L(9,(t=>e.options.get("toolbar"+(t+1)))),o=U(t,r);return Ce(o.length>0,o)},iv=e=>av(e).fold((()=>{const t=Nb(e);return f(t,r)&&t.length>0}),E),lv=e=>Mb(e)===cb.bottom,cv=e=>{var t;if(!e.inline)return A.none();const o=null!==(t=Db(e))&&void 0!==t?t:"";if(o.length>0)return wi(St(),o);const n=Bb(e);return g(n)?A.some(ze(n)):A.none()},dv=e=>e.inline&&cv(e).isSome(),uv=e=>cv(e).getOrThunk((()=>vt(bt(ze(e.getElement()))))),mv=e=>e.inline&&!tv(e)&&!rv(e)&&!iv(e),gv=e=>(e.options.get("toolbar_sticky")||e.inline)&&!dv(e)&&!mv(e),pv=e=>!dv(e)&&"split"===e.options.get("ui_mode"),hv=e=>{const t=e.options.get("menu");return ce(t,(e=>({...e,items:e.items})))};var fv=Object.freeze({__proto__:null,get ToolbarMode(){return lb},get ToolbarLocation(){return cb},register:hb,getSkinUrl:ov,getSkinUrlOption:nv,isReadOnly:fb,isSkinDisabled:ev,getHeightOption:bb,getWidthOption:vb,getMinWidthOption:yb,getMinHeightOption:xb,getMaxWidthOption:wb,getMaxHeightOption:Sb,getUserStyleFormats:kb,shouldMergeStyleFormats:Cb,shouldAutoHideStyleFormats:Ob,getLineHeightFormats:sv,getContentLanguages:_b,getRemovedMenuItems:Tb,isMenubarEnabled:tv,isMultipleToolbars:iv,isToolbarEnabled:rv,isToolbarPersist:Ib,getMultipleToolbarsOption:av,getUiContainer:uv,useFixedContainer:dv,isSplitUiMode:pv,getToolbarMode:Eb,isDraggableModal:jb,isDistractionFree:mv,isStickyToolbar:gv,getStickyToolbarOffset:Fb,getToolbarLocation:Mb,isToolbarLocationBottom:lv,getToolbarGroups:Ab,getMenus:hv,getMenubar:Rb,getToolbar:Nb,getFilePickerCallback:Lb,getFilePickerTypes:Hb,useTypeaheadUrls:Pb,getAnchorTop:Ub,getAnchorBottom:Wb,getFilePickerValidatorHandler:zb,getFontSizeInputDefaultUnit:Vb,useStatusBar:Gb,useElementPath:$b,promotionEnabled:Jb,useBranding:qb,getResize:Yb,getPasteAsText:Xb,getSidebarShow:Kb,useHelpAccessibility:Zb,getDefaultFontStack:Qb});var bv;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(bv||(bv={}));var vv=bv;const yv="tox-menu-nav__js",xv="tox-collection__item",wv="tox-swatch",Sv={normal:yv,color:wv},kv="tox-collection__item--enabled",Cv="tox-collection__item-icon",Ov="tox-collection__item-label",_v="tox-collection__item-caret",Tv="tox-collection__item--active",Ev="tox-collection__item-container",Av="tox-collection__item-container--row",Mv=e=>be(Sv,e).getOr(yv),Dv=e=>"color"===e?"tox-swatches":"tox-menu",Bv=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:Dv(e),tieredMenu:"tox-tiered-menu"}),Iv=e=>{const t=Bv(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:Mv(e)}},Fv=(e,t,o)=>{const n=Bv(o);return{tag:"div",classes:q([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},Rv=[zh.parts.items({})],Nv=(e,t,o)=>{const n=Bv(o);return{dom:{tag:"div",classes:q([[n.tieredMenu]])},markers:Iv(o)}},Lv=x([gs("data"),ws("inputAttributes",{}),ws("inputStyles",{}),ws("tag","input"),ws("inputClasses",[]),zi("onSetValue"),ws("styles",{}),ws("eventOrder",{}),Su("inputBehaviours",[wu,ch]),ws("selectOnFocus",!0)]),zv=e=>Ml([ch.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=Ja(t);t.dom.setSelectionRange(0,o.length)}:b})]),Vv=e=>({...zv(e),...Cu(e.inputBehaviours,[wu.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>Ja(e.element),setValue:(e,t)=>{Ja(e.element)!==t&&Za(e.element,t)}},onSetValue:e.onSetValue})])}),Hv=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Pv=km({name:"Input",configFields:Lv(),factory:(e,t)=>({uid:e.uid,dom:Hv(e),components:[],behaviours:Vv(e),eventOrder:e.eventOrder})}),Uv=da("refetch-trigger-event"),Wv=da("redirect-menu-item-interaction"),jv="tox-menu__searcher",Gv=e=>wi(e.element,`.${jv}`).bind((t=>e.getSystem().getByDom(t).toOptional())),$v=Gv,qv=e=>({fetchPattern:wu.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),Yv=e=>{const t=(e,t)=>(t.cut(),A.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Nr(e,Wv,o),A.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[xv]},components:[Pv.sketch({inputClasses:[jv,"tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:Ml([sh(n,[jr(er(),(e=>{Rr(e,Uv)})),jr(Zs(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),Yp.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,Yp.name()]}})]}},Xv="tox-collection--results__js",Kv=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:da("aria-item-search-result-id"),"aria-selected":"false"}}}:e},Jv=(e,t)=>o=>{const n=z(o,t);return V(n,(t=>({dom:e,components:t})))},Zv=(e,t)=>{const o=[];let n=[];return H(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(ve(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),V(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},Qv=(e,t,o)=>zh.parts.items({preprocess:n=>{const s=V(n,o);return"auto"!==e&&e>1?Jv({tag:"div",classes:["tox-collection__group"]},e)(s):Zv(s,((e,o)=>"separator"===t[o].type))}}),ey=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Qv(e,t,w)]}),ty=e=>N(e,(e=>"icon"in e&&void 0!==e.icon)),oy=e=>(console.error(Zn(e)),console.log(e),A.none()),ny=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[zh.parts.items({preprocess:e=>Zv(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},sy=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[zh.parts.items({preprocess:"auto"!==e?Jv({tag:"div",classes:["tox-swatches__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=ey(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?ey(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=da("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Yv({i18n:Kf.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],Xv],attributes:{id:n}},components:[Qv(e,t,Kv)]}]}})(n,o,s.searchMode):((e,t,o=!0)=>{const n=da("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",Xv].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:n}},components:[Qv(e,t,Kv)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[zh.parts.items({preprocess:Jv({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:Fv(t,n,s.menuType),components:Rv,items:o}},ry=is("type"),ay=is("name"),iy=is("label"),ly=is("text"),cy=is("title"),dy=is("icon"),uy=is("value"),my=cs("fetch"),gy=cs("getSubmenuItems"),py=cs("onAction"),hy=cs("onItemAction"),fy=Ts("onSetup",(()=>b)),by=fs("name"),vy=fs("text"),yy=fs("icon"),xy=fs("tooltip"),wy=fs("label"),Sy=fs("shortcut"),ky=vs("select"),Cy=_s("active",!1),Oy=_s("borderless",!1),_y=_s("enabled",!0),Ty=_s("primary",!1),Ey=e=>ws("columns",e),Ay=ws("meta",{}),My=Ts("onAction",b),Dy=e=>Cs("type",e),By=e=>ts("name","name",xn((()=>da(`${e}-name`))),Pn),Iy=In([ry,vy]),Fy=In([Dy("autocompleteitem"),Cy,_y,Ay,uy,vy,yy]),Ry=[_y,xy,yy,vy,fy],Ny=In([ry,py,Sy].concat(Ry)),Ly=e=>Xn("toolbarbutton",Ny,e),zy=[Cy].concat(Ry),Vy=In(zy.concat([ry,py,Sy])),Hy=e=>Xn("ToggleButton",Vy,e),Py=[Ts("predicate",T),Os("scope","node",["node","editor"]),Os("position","selection",["node","selection","line"])],Uy=Ry.concat([Dy("contextformbutton"),Ty,py,os("original",w)]),Wy=zy.concat([Dy("contextformbutton"),Ty,py,os("original",w)]),jy=Ry.concat([Dy("contextformbutton")]),Gy=zy.concat([Dy("contextformtogglebutton")]),$y=Qn("type",{contextformbutton:Uy,contextformtogglebutton:Wy}),qy=In([Dy("contextform"),Ts("initValue",x("")),wy,ms("commands",$y),ps("launch",Qn("type",{contextformbutton:jy,contextformtogglebutton:Gy}))].concat(Py)),Yy=In([Dy("contexttoolbar"),is("items")].concat(Py)),Xy=[ry,is("src"),fs("alt"),Es("classes",[],Pn)],Ky=In(Xy),Jy=[ry,ly,by,Es("classes",["tox-collection__item-label"],Pn)],Zy=In(Jy),Qy=Mn((()=>$n("type",{cardimage:Ky,cardtext:Zy,cardcontainer:ex}))),ex=In([ry,Cs("direction","horizontal"),Cs("align","left"),Cs("valign","middle"),ms("items",Qy)]),tx=[_y,vy,Sy,("menuitem",ts("value","value",xn((()=>da("menuitem-value"))),zn())),Ay];const ox=In([ry,wy,ms("items",Qy),fy,My].concat(tx)),nx=In([ry,Cy,yy].concat(tx)),sx=[ry,is("fancytype"),My],rx=[ws("initData",{})].concat(sx),ax=[vs("select"),As("initData",{},[_s("allowCustomColors",!0),Cs("storageKey","default"),ys("colors",zn())])].concat(sx),ix=Qn("fancytype",{inserttable:rx,colorswatch:ax}),lx=In([ry,fy,My,yy].concat(tx)),cx=In([ry,gy,fy,yy].concat(tx)),dx=In([ry,yy,Cy,fy,py].concat(tx)),ux=(e,t,o)=>{const n=td(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return A.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return A.none()},mx=e=>((e,t)=>Ml([sh(e,t)]))(da("unnamed-events"),e),gx=da("tooltip.exclusive"),px=da("tooltip.show"),hx=da("tooltip.hide"),fx=da("tooltip.immediateHide"),bx=da("tooltip.immediateShow"),vx=(e,t,o)=>{e.getSystem().broadcastOn([gx],{})};var yx=Object.freeze({__proto__:null,hideAllExclusive:vx,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&nh.set(e,n)}))}}),xx=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{n.getSystem().isConnected()&&(Vd(n),e.onHide(o,n),t.clearTooltip())})),t.clearTimer()},n=o=>{if(!t.isShowing()){vx(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Pr("normal"===e.mode?[jr(Xs(),(e=>{Rr(o,px)})),jr(qs(),(e=>{Rr(o,hx)}))]:[]),behaviours:Ml([nh.config({})])});t.setTooltip(s),Nd(n,s),e.onShow(o,s),Ad.position(n,s,{anchor:e.anchor(o)})}},s=o=>{t.getTooltip().each((t=>{const n=e.lazySink(o).getOrDie();Ad.position(n,t,{anchor:e.anchor(o)})}))};return Pr(q([[jr(px,(o=>{t.resetTimer((()=>{n(o)}),e.delayForShow())})),jr(hx,(n=>{t.resetTimer((()=>{o(n)}),e.delayForHide())})),jr(bx,(e=>{t.resetTimer((()=>{n(e)}),0)})),jr(fx,(e=>{t.resetTimer((()=>{o(e)}),0)})),jr(mr(),((e,t)=>{const n=t;n.universal||R(n.channels,gx)&&o(e)})),Qr((e=>{o(e)}))],(()=>{switch(e.mode){case"normal":return[jr(Ks(),(e=>{Rr(e,bx)})),jr(dr(),(e=>{Rr(e,fx)})),jr(Xs(),(e=>{Rr(e,px)})),jr(qs(),(e=>{Rr(e,hx)}))];case"follow-highlight":return[jr(Ir(),((e,t)=>{Rr(e,px)})),jr(Fr(),(e=>{Rr(e,hx)}))];case"children-normal":return[jr(Ks(),((o,n)=>{Ul(o.element).each((r=>{Je(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{Rr(o,bx)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),jr(dr(),(e=>{Ul(e.element).fold((()=>{Rr(e,fx)}),b)})),jr(Xs(),(o=>{wi(o.element,"[data-mce-tooltip]:hover").each((n=>{t.getTooltip().fold((()=>{Rr(o,px)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),jr(qs(),(e=>{wi(e.element,"[data-mce-tooltip]:hover").fold((()=>{Rr(e,hx)}),b)}))];default:return[jr(Ks(),((o,n)=>{Ul(o.element).each((r=>{Je(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{Rr(o,bx)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),jr(dr(),(e=>{Ul(e.element).fold((()=>{Rr(e,fx)}),b)}))]}})()]))}}),wx=[ss("lazySink"),ss("tooltipDom"),ws("exclusive",!0),ws("tooltipComponents",[]),Ts("delayForShow",x(300)),Ts("delayForHide",x(300)),Os("mode","normal",["normal","follow-highlight","children-keyboard-focus","children-normal"]),ws("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:x([fl,hl,ul,gl,ml,pl]),onRtl:x([fl,hl,ul,gl,ml,pl])},bubble:xc(0,-2,{})}))),zi("onHide"),zi("onShow")],Sx=Object.freeze({__proto__:null,init:()=>{const e=ac(),t=ac(),o=()=>{e.on(clearTimeout)},n=x("not-implemented");return Ea({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}});const kx=Bl({fields:wx,name:"tooltipping",active:xx,state:Sx,apis:yx}),Cx="silver.readonly",Ox=In([("readonly",rs("readonly",Un))]);const _x=(e,t)=>{const o=e.mainUi.outerContainer.element,n=[e.mainUi.mothership,...e.uiMotherships];t&&H(n,(e=>{e.broadcastOn([ou()],{target:o})})),H(n,(e=>{e.broadcastOn([Cx],{readonly:t})}))},Tx=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&_x(t,!0)})),e.on("SwitchMode",(()=>_x(t,e.mode.isReadOnly()))),fb(e)&&e.mode.set("readonly")},Ex=()=>Nl.config({channels:{[Cx]:{schema:Ox,onReceive:(e,t)=>{Um.set(e,t.readonly)}}}}),Ax=e=>Um.config({disabled:e}),Mx=e=>Um.config({disabled:e,disableClass:"tox-tbtn--disabled"}),Dx=e=>Um.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),Bx=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},Ix=(e,t)=>Zr((o=>{Bx(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),Fx=(e,t)=>Qr((o=>Bx(e,o)(t.get()))),Rx=(e,t)=>ta(((o,n)=>{Bx(e,o)(e.onAction),e.triggersSubmenu||t!==vv.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&Rr(o,br()),n.stop())})),Nx={[gr()]:["disabling","alloy.base.behaviour","toggling","item-events"]},Lx=we,zx=(e,t,o,n)=>{const s=Ms(b);return{type:"item",dom:t.dom,components:Lx(t.optComponents),data:e.data,eventOrder:Nx,hasSubmenu:e.triggersSubmenu,itemBehaviours:Ml([sh("item-events",[Rx(e,o),Ix(e,s),Fx(e,s)]),(r=()=>!e.enabled||n.isDisabled(),Um.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),Ex(),nh.config({})].concat(e.itemBehaviours))};var r},Vx=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),Hx=e=>{const t=mb.os.isMacOS()||mb.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=V(n,(e=>{const t=e.toLowerCase().trim();return ve(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},Px=(e,t,o=[Cv])=>rb(e,{tag:"div",classes:o},t),Ux=e=>({dom:{tag:"div",classes:[Ov]},components:[ai(Kf.translate(e))]}),Wx=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),jx=(e,t)=>({dom:{tag:"div",classes:[Ov]},components:[{dom:{tag:e.tag,styles:e.styles},components:[ai(Kf.translate(t))]}]}),Gx=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[ai(Hx(e))]}),$x=e=>Px("checkmark",e,["tox-collection__item-checkmark"]),qx=e=>{const t=e.map((e=>({attributes:{id:da("menu-item"),"aria-label":Kf.translate(e)}}))).getOr({});return{tag:"div",classes:[yv,xv],...t}},Yx=(e,t,o,n=A.none())=>"color"===e.presets?((e,t,o)=>{const n=e.value,s=e.iconContent.map((e=>((e,t,o)=>{const n=t();return tb(e,n).or(o).getOrThunk(Qf(n))})(e,t.icons,o))),r=e.ariaLabel.map((e=>({"aria-label":t.translate(e),"data-mce-name":e}))).getOr({});return{dom:(()=>{const e=wv,t=s.getOr(""),o={tag:"div",attributes:r,classes:[e]};return"custom"===n?{...o,tag:"button",classes:[...o.classes,"tox-swatches__picker-btn"],innerHtml:t}:"remove"===n?{...o,classes:[...o.classes,"tox-swatch--remove"],innerHtml:t}:g(n)?{...o,attributes:{...o.attributes,"data-mce-color":n},styles:{"background-color":n},innerHtml:t}:o})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[Cv]},r=o?e.iconContent.map((e=>rb(e,s,t.icons,n))).orThunk((()=>A.some({dom:s}))):A.none(),a=e.checkMark,i=A.from(e.meta).fold((()=>Ux),(e=>ve(e,"style")?k(jx,e.style):Ux)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>A.some(Wx(e,[Ov]))));return{dom:qx(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(Gx),a,e.caret]}})(e,t,o,n),Xx=(e,t,o)=>be(e,"tooltipWorker").map((e=>[kx.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:fc}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{kx.setComponents(t,[ii({element:ze(e)})])}))}})])).getOrThunk((()=>o.map((e=>[kx.config({...t.providers.tooltips.getConfig({tooltipText:e}),mode:"follow-highlight"})])).getOr([]))),Kx=(e,t)=>{const o=(e=>db.DOM.encode(e))(Kf.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},Jx=(e,t)=>V(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":Av,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[Ev,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,Jx(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>R(t.cardText.highlightOn,e))),n=o?A.from(t.cardText.matchText).getOr(""):"";return Wx(Kx(e.text,n),e.classes)}})),Zx=om(Dh(),Bh()),Qx=e=>({value:nw(e)}),ew=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,tw=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,ow=e=>ew.test(e)||tw.test(e),nw=e=>_e(e,"#").toUpperCase(),sw=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},rw=e=>{const t=sw(e.red)+sw(e.green)+sw(e.blue);return Qx(t)},aw=Math.min,iw=Math.max,lw=Math.round,cw=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,dw=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,uw=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),mw=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},gw=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=iw(0,aw(r,1)),a=iw(0,aw(a,1)),0===r)return t=o=n=lw(255*a),uw(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=lw(255*(t+d)),o=lw(255*(o+d)),n=lw(255*(n+d)),uw(t,o,n,1)},pw=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(ew,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=tw.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return uw(o,n,s,1)},hw=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return uw(s,r,a,i)},fw=e=>{if("transparent"===e)return A.some(uw(0,0,0,0));const t=cw.exec(e);if(null!==t)return A.some(hw(t[1],t[2],t[3],"1"));const o=dw.exec(e);return null!==o?A.some(hw(o[1],o[2],o[3],o[4])):A.none()},bw=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,vw=uw(255,0,0,1),yw=(e,t)=>{e.dispatch("ResizeContent",t)},xw=(e,t)=>{e.dispatch("TextColorChange",t)},ww=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),Sw=(e,t)=>()=>{e(),t()},kw=e=>Ow(e,"NodeChange",(t=>{t.setEnabled(e.selection.isEditable())})),Cw=(e,t)=>o=>{const n=kw(e)(o),s=((e,t)=>o=>{const n=rc(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}})(e,t)(o);return()=>{n(),s()}},Ow=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},_w=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},Tw=(e,t)=>()=>e.execCommand(t);var Ew=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const Aw={},Mw=e=>be(Aw,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=Ew.getItem(t);if(m(o)){const e=Ew.getItem("tinymce-custom-colors");Ew.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=Ew.getItem(e),n=r(o)?JSON.parse(o):[],s=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{s.splice(e,1)};return{add:o=>{F(s,o).each(i),s.unshift(o),s.length>t&&s.pop(),Ew.setItem(e,JSON.stringify(s))},state:()=>s.slice(0)}})(t,10);return Aw[e]=n,n})),Dw=(e,t)=>{Mw(e).add(t)},Bw=(e,t,o)=>({hue:e,saturation:t,value:o}),Iw=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,Bw(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,Bw(Math.round(t),Math.round(100*o),Math.round(100*n)))},Fw=e=>rw(gw(e)),Rw=e=>{return(t=e,ow(t)?A.some({value:nw(t)}):A.none()).orThunk((()=>fw(e).map(rw))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return rw(uw(s,r,a,i))}));var t},Nw="forecolor",Lw="hilitecolor",zw=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+Rw(e[o]).value,icon:"checkmark",type:"choiceitem"});return t},Vw=e=>t=>t.options.get(e),Hw="#000000",Pw=(e,t)=>t===Nw&&e.options.isSet("color_map_foreground")?Vw("color_map_foreground")(e):t===Lw&&e.options.isSet("color_map_background")?Vw("color_map_background")(e):Vw("color_map")(e),Uw=(e,t="default")=>Math.max(5,Math.ceil(Math.sqrt(Pw(e,t).length))),Ww=(e,t)=>{const o=Vw("color_cols")(e),n=Uw(e,t);return o===Uw(e)?n:o},jw=(e,t="default")=>Math.round(t===Nw?Vw("color_cols_foreground")(e):t===Lw?Vw("color_cols_background")(e):Vw("color_cols")(e)),Gw=Vw("custom_colors"),$w=Vw("color_default_foreground"),qw=Vw("color_default_background"),Yw=(e,t)=>{const o=ze(e.selection.getStart()),n="hilitecolor"===t?Ns(o,(e=>{if($e(e)){const t=Nt(e,"background-color");return Ce(fw(t).exists((e=>0!==e.alpha)),t)}return A.none()})).getOr("rgba(0, 0, 0, 0)"):Nt(o,"color");return fw(n).map((e=>"#"+rw(e).value))},Xw=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},Kw=(e,t,o,n)=>{"custom"===o?rS(e)((o=>{o.each((o=>{Dw(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),Yw(e,t).getOr(Hw)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},Jw=(e,t,o)=>e.concat((e=>V(Mw(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat(Xw(o))),Zw=(e,t,o)=>n=>{n(Jw(e,t,o))},Qw=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},eS=(e,t)=>{e.setTooltip(t)},tS=(e,t)=>o=>{const n=Yw(e,t);return xe(n,o.toUpperCase())},oS=(e,t,o)=>{if(Be(o))return"forecolor"===t?"Text color":"Background color";const n="forecolor"===t?"Text color {0}":"Background color {0}",s=Jw(Pw(e,t),t,!1),r=G(s,(e=>e.value===o)).getOr({text:""}).text;return e.translate([n,e.translate(r)])},nS=(e,t,o,n)=>{e.ui.registry.addSplitButton(t,{tooltip:oS(e,o,n.get()),presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:tS(e,o),columns:jw(e,o),fetch:Zw(Pw(e,o),o,Gw(e)),onAction:t=>{Kw(e,o,n.get(),b)},onItemAction:(s,r)=>{Kw(e,o,r,(o=>{n.set(o),xw(e,{name:t,color:o})}))},onSetup:s=>{Qw(s,t,n.get());const r=n=>{n.name===t&&(Qw(s,n.name,n.color),eS(s,oS(e,o,n.color)))};return e.on("TextColorChange",r),Sw(kw(e)(s),(()=>{e.off("TextColorChange",r)}))}})},sS=(e,t,o,n,s)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",onSetup:n=>(eS(n,oS(e,o,s.get())),Qw(n,t,s.get()),kw(e)(n)),getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",select:tS(e,o),initData:{storageKey:o},onAction:n=>{Kw(e,o,n.value,(o=>{s.set(o),xw(e,{name:t,color:o})}))}}]})},rS=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(A.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(A.none())}})},aS=(e,t,o,n,s,r,a,i)=>{const l=ty(t),c=iS(t,o,n,"color"!==s?"normal":"color",r,a,i);return sy(e,l,c,n,{menuType:s})},iS=(e,t,o,n,s,r,a)=>we(V(e,(i=>{return"choiceitem"===i.type?(l=i,Xn("choicemenuitem",nx,l)).fold(oy,(i=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Yx({presets:o,textContent:t?e.text:A.none(),htmlContent:A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:A.none(),checkMark:t?A.some($x(a.icons)):A.none(),caret:A.none(),value:e.value},a,i),c=e.text.filter(x(!t)).map((e=>kx.config(a.tooltips.getConfig({tooltipText:a.translate(e)}))));return vn(zx({data:Vx(e),enabled:e.enabled,getApi:e=>({setActive:t=>{bh.set(e,t)},isActive:()=>bh.isOn(e),isEnabled:()=>!Um.isDisabled(e),setEnabled:t=>Um.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[...c.toArray()]},l,r,a),{toggling:{toggleClass:kv,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(i,1===o,n,t,r(i.value),s,a,ty(e))))):A.none();var l}))),lS=(e,t)=>{const o=Iv(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group"),previousSelector:e=>"color"===t?wi(e.element,"[aria-checked=true]"):A.none()}},cS=da("cell-over"),dS=da("cell-execute"),uS=(e,t,o)=>{const n=o=>Nr(o,dS,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return di({dom:{tag:"div",attributes:{role:"button","aria-label":o}},behaviours:Ml([sh("insert-table-picker-cell",[jr(Xs(),ch.focus),jr(gr(),n),jr(or(),s),jr(hr(),s)]),bh.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),ch.config({onFocus:o=>Nr(o,cS,{row:e,col:t})})])})},mS=e=>Y(e,(e=>V(e,ui))),gS=(e,t)=>ai(`${t}x${e}`),pS={inserttable:(e,t)=>{const o=(e=>(t,o)=>e.shared.providers.translate(["{0} columns, {1} rows",o,t]))(t),n=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++){const s=e(t+1,n+1);o.push(uS(t,n,s))}n.push(o)}return n})(o),s=gS(0,0),r=Zh({dom:{tag:"span",classes:["tox-insert-table-picker__label"]},components:[s],behaviours:Ml([nh.config({})])});return{type:"widget",data:{value:da("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Zx.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:mS(n).concat(r.asSpec()),behaviours:Ml([sh("insert-table-picker",[Zr((e=>{nh.set(r.get(e),[s])})),Yr(cS,((e,t,o)=>{const{row:s,col:a}=o.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)bh.set(e[n][s],n<=t&&s<=o)})(n,s,a),nh.set(r.get(e),[gS(s+1,a+1)])})),Yr(dS,((t,o,n)=>{const{row:s,col:r}=n.event;e.onAction({numRows:s+1,numColumns:r+1}),Rr(t,br())}))]),Yp.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>Jw(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat(Xw(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),s="color",r={...aS(da("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,vv.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),markers:Iv(s),movement:lS(n,s)};return{type:"widget",data:{value:da("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Zx.widget(zh.sketch(r))]}}},hS=e=>({type:"separator",dom:{tag:"div",classes:[xv,"tox-collection__group-heading"]},components:e.text.map(ai).toArray()});var fS=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),bS=[rs("others",Yn(an.value,zn()))],vS=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===ae(t.others).length)throw new Error("Cannot find any known coupled components");return be(e,o)},o=x({});return Ea({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(be(o.others,n).getOrDie("No information found for coupled component: "+n),A.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=be(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const yS=Bl({fields:bS,name:"coupling",apis:fS,state:vS}),xS=e=>{let t=A.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=A.some(e),H(o,r),o=[])})),{get:n,map:e=>xS((t=>{n((o=>{t(e(o))}))})),isReady:s}},wS={nu:xS,pure:e=>xS((t=>{t(e)}))},SS=e=>{setTimeout((()=>{throw e}),0)},kS=e=>{const t=t=>{e().then(t,SS)};return{map:t=>kS((()=>e().then(t))),bind:t=>kS((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>kS((()=>e().then((()=>t.toPromise())))),toLazy:()=>wS.nu(t),toCached:()=>{let t=null;return kS((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},CS=e=>kS((()=>new Promise(e))),OS=e=>kS((()=>Promise.resolve(e))),_S=x("sink"),TS=x(Ju({name:_S(),overrides:x({dom:{tag:"div"},behaviours:Ml([Ad.config({useFixed:E})]),events:Pr([Xr(Zs()),Xr(Gs()),Xr(or())])})})),ES=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},AS=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=BS(n,e);return i.map((e=>e.bind((e=>A.from(qh.sketch({...r.menu(),uid:ba(""),data:e,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();Ad.position(n,t,{anchor:o}),tu.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();Ad.position(n,o,{anchor:{type:"submenu",item:t}}),tu.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();Ad.position(s,t,{anchor:o}),H(n,(e=>{Ad.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(ch.focus(n),tu.close(s),A.some(!0))}))))))})(e,t,ES(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{tu.isOpen(n)&&tu.close(n)}),(e=>{tu.cloak(n),tu.open(n,e),r(n)})),n)))},MS=(e,t,o,n,s,r,a)=>(tu.close(n),OS(n)),DS=(e,t,o,n,s,r)=>{const a=yS.getCoupled(o,"sandbox");return(tu.isOpen(a)?MS:AS)(e,t,o,a,n,s,r)},BS=(e,t)=>e.getSystem().getByUid(t.uid+"-"+_S()).map((e=>()=>an.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>an.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),IS=e=>{tu.getState(e).each((e=>{qh.repositionMenus(e)}))},FS=(e,t,o)=>{const n=Ci(),s=BS(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id,role:"listbox"}},behaviours:_u(e.sandboxBehaviours,[wu.config({store:{mode:"memory",initialValue:t}}),tu.config({onOpen:(s,r)=>{const a=ES(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=Em.getCurrent(t).getOr(t),s=Qt(e.element);o?It(n.element,"min-width",s+"px"):((e,t)=>{Zt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,r)=>{n.unlink(t.element),s().getOr(r).element.dom.dispatchEvent(new window.FocusEvent("focusout")),void 0!==o&&void 0!==o.onClose&&o.onClose(e,r)},isPartOf:(e,o,n)=>Oi(o,n)||Oi(t,n),getAttachPoint:()=>s().getOrDie()}),Em.config({find:e=>tu.getState(e).bind((e=>Em.getCurrent(e)))}),Nl.config({channels:{...au({isExtraPart:T}),...lu({doReposition:IS})}})])}},RS=e=>{const t=yS.getCoupled(e,"sandbox");IS(t)},NS=()=>[ws("sandboxClasses",[]),Ou("sandboxBehaviours",[Em,Nl,tu,wu])],LS=x([ss("dom"),ss("fetch"),zi("onOpen"),Vi("onExecute"),ws("getHotspot",A.some),ws("getAnchorOverrides",x({})),Ec(),Su("dropdownBehaviours",[bh,yS,Yp,ch]),ss("toggleClass"),ws("eventOrder",{}),gs("lazySink"),ws("matchWidth",!1),ws("useMinWidth",!1),gs("role")].concat(NS())),zS=x([Ku({schema:[Ri(),ws("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),TS()]),VS=Cm({name:"Dropdown",configFields:LS(),partFields:zS(),factory:(e,t,o,n)=>{const s=e=>{tu.getState(e).each((e=>{qh.highlightPrimary(e)}))},r=(t,o,s)=>DS(e,w,t,n,o,s),a={expand:e=>{bh.isOn(e)||r(e,b,Gh.HighlightNone).get(b)},open:e=>{bh.isOn(e)||r(e,b,Gh.HighlightMenuAndItem).get(b)},refetch:t=>yS.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,Gh.HighlightMenuAndItem).map(b)),(o=>AS(e,w,t,o,n,b,Gh.HighlightMenuAndItem).map(b))),isOpen:bh.isOn,close:e=>{bh.isOn(e)&&r(e,b,Gh.HighlightMenuAndItem).get(b)},repositionMenus:e=>{bh.isOn(e)&&RS(e)}},i=(e,t)=>(Lr(e),A.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:Cu(e.dropdownBehaviours,[bh.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),yS.config({others:{sandbox:t=>FS(e,t,{onOpen:()=>bh.on(t),onClose:()=>bh.off(t)})}}),Yp.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(VS.isOpen(e)){const t=yS.getCoupled(e,"sandbox");s(t)}else VS.open(e);return A.some(!0)},onEscape:(e,t)=>VS.isOpen(e)?(VS.close(e),A.some(!0)):A.none()}),ch.config({})]),events:yh(A.some((e=>{r(e,s,Gh.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[gr()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":"true",...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",be(e.dom,"attributes").bind((e=>be(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),HS=(e,t,o)=>{$v(e).each((e=>{var n;((e,t)=>{Et(t.element,"id").each((t=>Ot(e.element,"aria-activedescendant",t)))})(e,o),(qa((n=t).element,Xv)?A.some(n.element):wi(n.element,"."+Xv)).each((t=>{Et(t,"id").each((t=>Ot(e.element,"aria-controls",t)))}))})),Ot(o.element,"aria-selected","true")},PS=(e,t,o)=>{Ot(o.element,"aria-selected","false")},US=e=>yS.getExistingCoupled(e,"sandbox").bind(Gv).map(qv).map((e=>e.fetchPattern)).getOr("");var WS;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(WS||(WS={}));const jS=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:A.none(),icon:e.text.isSome()?A.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,Xn("menuitem",lx,i)).fold(oy,(e=>A.some(((e,t,o,n=!0)=>{const s=Yx({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.none(),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return zx({data:Vx(e),getApi:e=>({isEnabled:()=>!Um.isDisabled(e),setEnabled:t=>Um.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>Xn("nestedmenuitem",cx,e))(e).fold(oy,(e=>A.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,Px("chevron-down",a,[_v])):(e=>Px("chevron-right",e,[_v]))(o.icons);var a;const i=Yx({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.some(r),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return zx({data:Vx(e),getApi:e=>({isEnabled:()=>!Um.isDisabled(e),setEnabled:t=>Um.set(e,!t),setIconFill:(t,o)=>{wi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{Ot(e,"fill",o)}))},setTooltip:t=>{const n=o.translate(t);Ot(e.element,"aria-label",n)}}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>Xn("togglemenuitem",dx,e))(e).fold(oy,(e=>A.some(((e,t,o,n=!0)=>{const s=Yx({iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,checkMark:A.some($x(o.icons)),caret:A.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return vn(zx({data:Vx(e),enabled:e.enabled,getApi:e=>({setActive:t=>{bh.set(e,t)},isActive:()=>bh.isOn(e),isEnabled:()=>!Um.isDisabled(e),setEnabled:t=>Um.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:kv,toggleOnExecute:!1,selected:e.active}})})(a(e),t,r,n))));case"separator":return(e=>Xn("separatormenuitem",Iy,e))(e).fold(oy,(e=>A.some(hS(e))));case"fancymenuitem":return(e=>Xn("fancymenuitem",ix,e))(e).fold(oy,(e=>((e,t)=>be(pS,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),A.none()}var i},GS=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||ty(e);return we(V(e,(e=>{switch(e.type){case"separator":return(n=e,Xn("Autocompleter.Separator",Iy,n)).fold(oy,(e=>A.some(hS(e))));case"cardmenuitem":return(e=>Xn("cardmenuitem",ox,e))(e).fold(oy,(e=>A.some(((e,t,o,n)=>{const s={dom:qx(e.label),optComponents:[A.some({dom:{tag:"div",classes:[Ev,Av]},components:Jx(e.items,n)})]};return zx({data:Vx({text:A.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!Um.isDisabled(e),setEnabled:t=>{Um.set(e,!t),H(td(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(Um)&&Um.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:A.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:Xx(e.meta,r,A.none()),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>Xn("Autocompleter.Item",Fy,e))(e).fold(oy,(e=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Yx({presets:n,textContent:A.none(),htmlContent:o?e.text.map((e=>Kx(e,t))):A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:A.none(),checkMark:A.none(),caret:A.none(),value:e.value},a.providers,i,e.icon),c=e.text.filter((e=>!o&&""!==e));return zx({data:Vx(e),enabled:e.enabled,getApi:x({}),onAction:t=>s(e.value,e.meta),onSetup:x(b),triggersSubmenu:!1,itemBehaviours:Xx(e,a,c)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},$S=(e,t,o,n,s,r)=>{const a=ty(t),i=we(V(t,(e=>{const t=e=>jS(e,o,n,(e=>s?!ve(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?ny:sy)(e,a,i,1,l)},qS=e=>qh.singleData(e.value,e),YS=e=>Wc(ze(e.startContainer),e.startOffset,ze(e.endContainer),e.endOffset),XS=(e,t)=>{const o=da("autocompleter"),n=Ms(!1),s=Ms(!1),r=ac(),a=di(Yh.sketch({dom:{tag:"div",classes:["tox-autocompleter"],attributes:{id:o}},components:[],fireDismissalEventInstead:{},inlineBehaviours:Ml([sh("dismissAutocompleter",[jr(_r(),(()=>u())),jr(Ir(),((t,o)=>{Et(o.event.target,"id").each((t=>Ot(ze(e.getBody()),"aria-activedescendant",t)))}))])]),lazySink:t.getSink})),i=()=>Yh.isOpen(a),l=s.get,c=()=>{if(i()){Yh.hide(a),e.dom.remove(o,!1);const t=ze(e.getBody());Et(t,"aria-owns").filter((e=>e===o)).each((()=>{Mt(t,"aria-owns"),Mt(t,"aria-activedescendant")}))}},d=()=>Yh.getContent(a).bind((e=>te(e.components(),0))),u=()=>e.execCommand("mceAutocompleterClose"),m=s=>{const i=(o=>{const s=re(o,(e=>A.from(e.columns))).getOr(1);return Y(o,(o=>{const a=o.items;return GS(a,o.matchText,((t,s)=>{const a={hide:()=>u(),reload:t=>{c(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};e.execCommand("mceAutocompleterRefreshActiveRange"),r.get().each((e=>{n.set(!0),o.onAction(a,e,t,s),n.set(!1)}))}),s,vv.BUBBLE_TO_SANDBOX,t,o.highlightOn)}))})(s);i.length>0?(((t,o)=>{const n=re(t,(e=>A.from(e.columns))).getOr(1);Yh.showMenuAt(a,{anchor:{type:"selection",getSelection:()=>r.get().map(YS),root:ze(e.getBody())}},((e,t,o,n)=>{const s=lS(t,n),r=Iv(n);return{data:qS({...e,movement:s,menuBehaviours:mx("auto"!==t?[]:[Zr(((e,t)=>{ux(e,4,r.item).each((({numColumns:t,numRows:o})=>{Yp.setGridSize(e,o,t)}))}))])}),menu:{markers:Iv(n),fakeFocus:o===WS.ContentFocus}}})(sy("autocompleter-value",!0,o,n,{menuType:"normal"}),n,WS.ContentFocus,"normal")),d().each(Zm.highlightFirst)})(s,i),Ot(ze(e.getBody()),"aria-owns",o),e.inline||g()):c()},g=()=>{e.dom.get(o)&&e.dom.remove(o,!1);const t=e.getDoc().documentElement,n=e.selection.getNode(),s=(e=>sa(e,!0))(a.element);Ft(s,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px",top:`${n.offsetTop}px`,left:`${n.offsetLeft}px`}),e.dom.add(t,s.dom),wi(s,'[role="menu"]').each((e=>{Pt(e,"position"),Pt(e,"max-height")}))};e.on("AutocompleterStart",(({lookupData:e})=>{s.set(!0),n.set(!1),m(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>m(e))),e.on("AutocompleterUpdateActiveRange",(({range:e})=>r.set(e))),e.on("AutocompleterEnd",(()=>{c(),s.set(!1),n.set(!1),r.clear()}));((e,t)=>{const o=(e,t)=>{Nr(e,Zs(),{raw:t})},n=()=>e.getMenu().bind(Zm.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(Lr),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Zm.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(()=>{!e.isActive()||e.isProcessingAction()||t.queryCommandState("mceAutoCompleterInRange")||e.cancelIfNecessary()}))})({cancelIfNecessary:u,isMenuOpen:i,isActive:l,isProcessingAction:n.get,getMenu:d},e)},KS=["visible","hidden","clip"],JS=e=>Me(e).length>0&&!R(KS,e),ZS=e=>{if(Ge(e)){const t=Nt(e,"overflow-x"),o=Nt(e,"overflow-y");return JS(t)||JS(o)}return!1},QS=(e,t)=>pv(e)?(e=>{const t=ed(e,ZS),o=0===t.length?yt(e).map(xt).map((e=>ed(e,ZS))).getOr([]):t;return oe(o).map((e=>({element:e,others:o.slice(1)})))})(t):A.none(),ek=e=>{const t=[...V(e.others,Qo),on()];return((e,t)=>j(t,((e,t)=>tn(e,t)),e))(Qo(e.element),t)},tk=(e,t,o)=>Si(e,t,o).isSome(),ok=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},nk=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?A.none():A.some(t.touches[0])},sk=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=ac(),o=Ms(!1),n=ok((t=>{e.triggerEvent(fr(),t),o.set(!0)}),400),s=Is([{key:Ps(),value:e=>(nk(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),A.none())},{key:Us(),value:e=>(n.cancel(),nk(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),A.none())},{key:Ws(),value:s=>(n.cancel(),t.get().filter((e=>et(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(hr(),s))))}]);return{fireIfReady:(e,t)=>be(s,t).bind((t=>t(e)))}})(o),s=V(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>lc(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=ac(),a=lc(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(ur(),e)}),0))})),i=lc(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===Qm[0]&&!R(["input","textarea"],We(e.target))&&!tk(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=lc(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=ac(),d=lc(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(dr(),e)}),0))}));return{unbind:()=>{H(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},rk=(e,t)=>{const o=be(e,"target").getOr(t);return Ms(o)},ak=Ds([{stopped:[]},{resume:["element"]},{complete:[]}]),ik=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=Ms(!1),n=Ms(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),ak.complete())),(e=>{const o=e.descHandler;return Da(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),ak.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),ak.complete()):at(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),ak.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),ak.resume(n))))}))},lk=(e,t,o,n,s,r)=>ik(e,t,o,n,s,r).fold(E,(n=>lk(e,t,o,n,s,r)),T),ck=(e,t,o,n,s)=>{const r=rk(o,n);return lk(e,t,o,n,r,s)},dk=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{le(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:k.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{le(e,((e,o)=>{ve(e,t)&&delete e[t]}))},filterByType:t=>be(e,t).map((e=>pe(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>be(e,o).bind((e=>Ns(n,(t=>((e,t)=>fa(t).bind((t=>be(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{fa(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return fa(t).getOrThunk((()=>((e,t)=>{const o=da(ga+"uid-");return ha(t,o),o})(0,e.element)))})(n);ye(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+ra(s.element)+"\nCannot use it for: "+ra(e.element)+"\nThe conflicting element is"+(wt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>be(t,e)}},uk=km({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:ku(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[ws("components",[]),Su("containerBehaviours",[]),ws("events",{}),ws("domModification",{}),ws("eventOrder",{})]}),mk=e=>{const t=t=>at(e.element).fold(E,(e=>et(t,e))),o=dk(),n=(e,n)=>o.find(t,e,n),s=sk(e.element,{triggerEvent:(e,t)=>Ai(e,t.target,(o=>((e,t,o,n)=>ck(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:x("real"),triggerEvent:(e,t,o)=>{Ai(e,t,(s=>ck(n,e,o,t,s)))},triggerFocus:(e,t)=>{fa(e).fold((()=>{zl(e)}),(o=>{Ai(cr(),e,(o=>(((e,t,o,n,s)=>{const r=rk(o,n);ik(e,t,o,n,r,s)})(n,cr(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:di,buildOrPatch:ci,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:E},a=e=>{e.connect(r),qe(e.element)||(o.register(e),H(e.components(),a),r.triggerEvent(yr(),e.element,{target:e.element}))},i=e=>{qe(e.element)||(H(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{Nd(e,t)},c=e=>{Vd(e)},d=e=>{const t=o.filter(mr());H(t,(t=>{const o=t.descHandler;Da(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=Ms(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:T,event:e,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}})(t);return H(e,(e=>{const t=e.descHandler;Da(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>an.error(new Error('Could not find component with uid: "'+e+'" in system.'))),an.value),h=e=>{const t=fa(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Wo(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},gk=x([ws("prefix","form-field"),Su("fieldBehaviours",[Em,wu])]),pk=x([Ju({schema:[ss("dom")],name:"label"}),Ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[ss("text")],name:"aria-descriptor"}),Xu({factory:{sketch:e=>{const t=((e,t)=>{const o={};return le(e,((e,n)=>{R(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[ss("factory")],name:"field"})]),hk=Cm({name:"FormField",configFields:gk(),partFields:pk(),factory:(e,t,o,n)=>{const s=Cu(e.fieldBehaviours,[Em.config({find:t=>cm(t,e,"field")}),wu.config({store:{mode:"manual",getValue:e=>Em.getCurrent(e).bind(wu.getValue),setValue:(e,t)=>{Em.getCurrent(e).each((e=>{wu.setValue(e,t)}))}}})]),r=Pr([Zr(((t,o)=>{const n=um(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=da(e.prefix);n.label().each((e=>{Ot(e.element,"for",o),Ot(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=da(e.prefix);Ot(o.element,"id",n),Ot(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>cm(t,e,"field"),getLabel:t=>cm(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var fk=Object.freeze({__proto__:null,exhibit:(e,t)=>Ma({attributes:Is([{key:t.tabAttr,value:"true"}])})}),bk=[ws("tabAttr","data-alloy-tabstop")];const vk=Bl({fields:bk,name:"tabstopping",active:fk});var yk=tinymce.util.Tools.resolve("tinymce.html.Entities");const xk=(e,t,o,n)=>{const s=wk(e,t,o,n);return hk.sketch(s)},wk=(e,t,o,n)=>({dom:Sk(o),components:e.toArray().concat([t]),fieldBehaviours:Ml(n)}),Sk=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),kk=(e,t)=>hk.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ai(t.translate(e))]}),Ck=da("form-component-change"),Ok=da("form-close"),_k=da("form-cancel"),Tk=da("form-action"),Ek=da("form-submit"),Ak=da("form-block"),Mk=da("form-unblock"),Dk=da("form-tabchange"),Bk=da("form-resize"),Ik=(e,t,o)=>{const n=e.label.map((e=>kk(e,t))),s=t.icons(),r=e=>(t,o)=>{Si(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,Tt(n,"data-collection-item-value"))}))},a=r(((o,n,s,r)=>{n.stop(),t.isDisabled()||Nr(o,Tk,{name:e.name,value:r})})),i=[jr(Xs(),r(((e,t,o)=>{zl(o)}))),jr(or(),a),jr(hr(),a),jr(Ks(),r(((e,t,o)=>{wi(e.element,"."+Tv).each((e=>{$a(e,Tv)})),ja(o,Tv)}))),jr(Js(),r((e=>{wi(e.element,"."+Tv).each((e=>{$a(e,Tv),Vl(e)}))}))),ta(r(((t,o,n,s)=>{Nr(t,Tk,{name:e.name,value:s})})))],l=(e,t)=>V(td(e.element,".tox-collection__item"),t),c=hk.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:Ml([Um.config({disabled:t.isDisabled,onDisabled:e=>{l(e,(e=>{ja(e,"tox-collection__item--state-disabled"),Ot(e,"aria-disabled",!0)}))},onEnabled:e=>{l(e,(e=>{$a(e,"tox-collection__item--state-disabled"),Mt(e,"aria-disabled")}))}}),Ex(),nh.config({}),kx.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{wi(e.element,"."+Tv+"[data-mce-tooltip]").each((o=>{Et(o,"data-mce-tooltip").each((o=>{kx.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-keyboard-focus",anchor:e=>({type:"node",node:wi(e.element,"."+Tv).orThunk((()=>Qe(".tox-collection__item"))),root:e.element,layouts:{onLtr:x([fl,hl,ul,gl,ml,pl]),onRtl:x([fl,hl,ul,gl,ml,pl])},bubble:xc(0,-2,{})})}),wu.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const r=V(n,(o=>{const n=Kf.translate(o.text),r=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",a=`<div class="tox-collection__item-icon">${(e=>{var t;return null!==(t=s[e])&&void 0!==t?t:e})(o.icon)}</div>`,i={_:" "," - ":" ","-":" "},l=n.replace(/\_| \- |\-/g,(e=>i[e]));return`<div data-mce-tooltip="${l}" class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${yk.encodeAllRaw(o.value)}" aria-label="${l}">${a}${r}</div>`})),a="auto"!==e.columns&&e.columns>1?z(r,e.columns):[r],i=V(a,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));na(o.element,i.join(""))})(o,n),"auto"===e.columns&&ux(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{Yp.setGridSize(o,e,t)})),Rr(o,Bk)}}),vk.config({}),Yp.config((d=e.columns,"normal",1===d?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===d?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${xv}`}})),sh("collection-events",i)]),eventOrder:{[gr()]:["disabling","alloy.base.behaviour","collection-events"],[Ks()]:["collection-events","tooltipping"]}});var d;return xk(n,c,["tox-form__group--collection"],[])},Fk=["input","textarea"],Rk=e=>{const t=We(e);return R(Fk,t)},Nk=(e,t)=>{const o=t.getRoot(e).getOr(e.element);$a(o,t.invalidClass),t.notify.each((t=>{Rk(e.element)&&Ot(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{na(e,t.validHtml)})),t.onValid(e)}))},Lk=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);ja(s,t.invalidClass),t.notify.each((t=>{Rk(e.element)&&Ot(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{na(e,n)})),t.onInvalid(e,n)}))},zk=(e,t,o)=>t.validator.fold((()=>OS(an.value(!0))),(t=>t.validate(e))),Vk=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),zk(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(Lk(e,t,0,o),an.error(o))),(o=>(Nk(e,t),an.value(o)))):an.error("No longer in system"))));var Hk=Object.freeze({__proto__:null,markValid:Nk,markInvalid:Lk,query:zk,run:Vk,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return qa(o,t.invalidClass)}}),Pk=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Pr([jr(t.onEvent,(t=>{Vk(t,e).get(w)}))].concat(t.validateOnLoad?[Zr((t=>{Vk(t,e).get(b)}))]:[])))).getOr({})}),Uk=[ss("invalidClass"),ws("getRoot",A.none),xs("notify",[ws("aria","alert"),ws("getContainer",A.none),ws("validHtml",""),zi("onValid"),zi("onInvalid"),zi("onValidate")]),xs("validator",[ss("validate"),ws("onEvent","input"),ws("validateOnLoad",!0)])];const Wk=Bl({fields:Uk,name:"invalidating",active:Pk,apis:Hk,extra:{validation:e=>t=>{const o=wu.getValue(t);return OS(e(o))}}}),jk=Bl({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Pr([Ur(ar(),E)]),exhibit:()=>Ma({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),Gk=da("color-input-change"),$k=da("color-swatch-change"),qk=da("color-picker-cancel"),Yk=Ju({schema:[ss("dom")],name:"label"}),Xk=e=>Ju({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Pr([Gr(Ps(),((t,o,n)=>e(t,n)),[t]),Gr(Gs(),((t,o,n)=>e(t,n)),[t]),Gr($s(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),Kk=Xk("top-left"),Jk=Xk("top"),Zk=Xk("top-right"),Qk=Xk("right"),eC=Xk("bottom-right"),tC=Xk("bottom"),oC=Xk("bottom-left"),nC=Xk("left"),sC=Xu({name:"thumb",defaults:x({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Pr([qr(Ps(),e,"spectrum"),qr(Us(),e,"spectrum"),qr(Ws(),e,"spectrum"),qr(Gs(),e,"spectrum"),qr($s(),e,"spectrum"),qr(Ys(),e,"spectrum")])})}),rC=e=>pg(e.event);var aC=[Yk,nC,Qk,Jk,tC,Kk,Zk,oC,eC,sC,Xu({schema:[os("mouseIsDown",(()=>Ms(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:Ml([Yp.config({mode:"special",onLeft:(o,n)=>t.onLeft(o,e,rC(n)),onRight:(o,n)=>t.onRight(o,e,rC(n)),onUp:(o,n)=>t.onUp(o,e,rC(n)),onDown:(o,n)=>t.onDown(o,e,rC(n))}),vk.config({}),ch.config({})]),events:Pr([jr(Ps(),o),jr(Us(),o),jr(Gs(),o),jr($s(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const iC=x("slider.change.value"),lC=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?A.some(e.touches[0]).map((e=>Yt(e.clientX,e.clientY))):A.none()}{const e=t;return void 0!==e.clientX?A.some(e).map((e=>Yt(e.clientX,e.clientY))):A.none()}},cC=e=>e.model.minX,dC=e=>e.model.minY,uC=e=>e.model.minX-1,mC=e=>e.model.minY-1,gC=e=>e.model.maxX,pC=e=>e.model.maxY,hC=e=>e.model.maxX+1,fC=e=>e.model.maxY+1,bC=(e,t,o)=>t(e)-o(e),vC=e=>bC(e,gC,cC),yC=e=>bC(e,pC,dC),xC=e=>vC(e)/2,wC=e=>yC(e)/2,SC=(e,t)=>t?e.stepSize*e.speedMultiplier:e.stepSize,kC=e=>e.snapToGrid,CC=e=>e.snapStart,OC=e=>e.rounded,_C=(e,t)=>void 0!==e[t+"-edge"],TC=e=>_C(e,"left"),EC=e=>_C(e,"right"),AC=e=>_C(e,"top"),MC=e=>_C(e,"bottom"),DC=e=>e.model.value.get(),BC=(e,t)=>({x:e,y:t}),IC=(e,t)=>{Nr(e,iC(),{value:t})},FC=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),RC=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),NC=(e,t,o)=>Math.max(t,Math.min(o,e)),LC=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=NC(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return NC(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},zC=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},VC="top",HC="right",PC="bottom",UC="left",WC=e=>e.element.dom.getBoundingClientRect(),jC=(e,t)=>e[t],GC=e=>{const t=WC(e);return jC(t,UC)},$C=e=>{const t=WC(e);return jC(t,HC)},qC=e=>{const t=WC(e);return jC(t,VC)},YC=e=>{const t=WC(e);return jC(t,PC)},XC=e=>{const t=WC(e);return jC(t,"width")},KC=e=>{const t=WC(e);return jC(t,"height")},JC=(e,t,o)=>(e+t)/2-o,ZC=(e,t)=>{const o=WC(e),n=WC(t),s=jC(o,UC),r=jC(o,HC),a=jC(n,UC);return JC(s,r,a)},QC=(e,t)=>{const o=WC(e),n=WC(t),s=jC(o,VC),r=jC(o,PC),a=jC(n,VC);return JC(s,r,a)},eO=(e,t)=>{Nr(e,iC(),{value:t})},tO=(e,t,o)=>{const n={min:cC(t),max:gC(t),range:vC(t),value:o,step:SC(t),snap:kC(t),snapStart:CC(t),rounded:OC(t),hasMinEdge:TC(t),hasMaxEdge:EC(t),minBound:GC(e),maxBound:$C(e),screenRange:XC(e)};return LC(n)},oO=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?RC:FC)(DC(o),cC(o),gC(o),SC(o,n));return eO(t,s),A.some(s)})(e,t,o,n).map(E),nO=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=XC(e),a=n.bind((t=>A.some(ZC(t,e)))).getOr(0),i=s.bind((t=>A.some(ZC(t,e)))).getOr(r),l={min:cC(t),max:gC(t),range:vC(t),value:o,hasMinEdge:TC(t),hasMaxEdge:EC(t),minBound:GC(e),minOffset:0,maxBound:$C(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return zC(l)})(t,r,o,n,s);return GC(t)-GC(e)+a},sO=oO(-1),rO=oO(1),aO=A.none,iO=A.none,lO={"top-left":A.none(),top:A.none(),"top-right":A.none(),right:A.some(((e,t)=>{IC(e,hC(t))})),"bottom-right":A.none(),bottom:A.none(),"bottom-left":A.none(),left:A.some(((e,t)=>{IC(e,uC(t))}))};var cO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=tO(e,t,o);return eO(e,n),n},setToMin:(e,t)=>{const o=cC(t);eO(e,o)},setToMax:(e,t)=>{const o=gC(t);eO(e,o)},findValueOfOffset:tO,getValueFromEvent:e=>lC(e).map((e=>e.left)),findPositionOfValue:nO,setPositionFromValue:(e,t,o,n)=>{const s=DC(o),r=nO(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Qt(t.element)/2;It(t.element,"left",r-a+"px")},onLeft:sO,onRight:rO,onUp:aO,onDown:iO,edgeActions:lO});const dO=(e,t)=>{Nr(e,iC(),{value:t})},uO=(e,t,o)=>{const n={min:dC(t),max:pC(t),range:yC(t),value:o,step:SC(t),snap:kC(t),snapStart:CC(t),rounded:OC(t),hasMinEdge:AC(t),hasMaxEdge:MC(t),minBound:qC(e),maxBound:YC(e),screenRange:KC(e)};return LC(n)},mO=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?RC:FC)(DC(o),dC(o),pC(o),SC(o,n));return dO(t,s),A.some(s)})(e,t,o,n).map(E),gO=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=KC(e),a=n.bind((t=>A.some(QC(t,e)))).getOr(0),i=s.bind((t=>A.some(QC(t,e)))).getOr(r),l={min:dC(t),max:pC(t),range:yC(t),value:o,hasMinEdge:AC(t),hasMaxEdge:MC(t),minBound:qC(e),minOffset:0,maxBound:YC(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return zC(l)})(t,r,o,n,s);return qC(t)-qC(e)+a},pO=A.none,hO=A.none,fO=mO(-1),bO=mO(1),vO={"top-left":A.none(),top:A.some(((e,t)=>{IC(e,mC(t))})),"top-right":A.none(),right:A.none(),"bottom-right":A.none(),bottom:A.some(((e,t)=>{IC(e,fC(t))})),"bottom-left":A.none(),left:A.none()};var yO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=uO(e,t,o);return dO(e,n),n},setToMin:(e,t)=>{const o=dC(t);dO(e,o)},setToMax:(e,t)=>{const o=pC(t);dO(e,o)},findValueOfOffset:uO,getValueFromEvent:e=>lC(e).map((e=>e.top)),findPositionOfValue:gO,setPositionFromValue:(e,t,o,n)=>{const s=DC(o),r=gO(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=Gt(t.element)/2;It(t.element,"top",r-a+"px")},onLeft:pO,onRight:hO,onUp:fO,onDown:bO,edgeActions:vO});const xO=(e,t)=>{Nr(e,iC(),{value:t})},wO=(e,t)=>({x:e,y:t}),SO=(e,t)=>(o,n,s)=>((e,t,o,n,s)=>{const r=e>0?RC:FC,a=t?DC(n).x:r(DC(n).x,cC(n),gC(n),SC(n,s)),i=t?r(DC(n).y,dC(n),pC(n),SC(n,s)):DC(n).y;return xO(o,wO(a,i)),A.some(a)})(e,t,o,n,s).map(E),kO=SO(-1,!1),CO=SO(1,!1),OO=SO(-1,!0),_O=SO(1,!0),TO={"top-left":A.some(((e,t)=>{IC(e,BC(uC(t),mC(t)))})),top:A.some(((e,t)=>{IC(e,BC(xC(t),mC(t)))})),"top-right":A.some(((e,t)=>{IC(e,BC(hC(t),mC(t)))})),right:A.some(((e,t)=>{IC(e,BC(hC(t),wC(t)))})),"bottom-right":A.some(((e,t)=>{IC(e,BC(hC(t),fC(t)))})),bottom:A.some(((e,t)=>{IC(e,BC(xC(t),fC(t)))})),"bottom-left":A.some(((e,t)=>{IC(e,BC(uC(t),fC(t)))})),left:A.some(((e,t)=>{IC(e,BC(uC(t),wC(t)))}))};var EO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=tO(e,t,o.left),s=uO(e,t,o.top),r=wO(n,s);return xO(e,r),r},setToMin:(e,t)=>{const o=cC(t),n=dC(t);xO(e,wO(o,n))},setToMax:(e,t)=>{const o=gC(t),n=pC(t);xO(e,wO(o,n))},getValueFromEvent:e=>lC(e),setPositionFromValue:(e,t,o,n)=>{const s=DC(o),r=nO(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=gO(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Qt(t.element)/2,l=Gt(t.element)/2;It(t.element,"left",r-i+"px"),It(t.element,"top",a-l+"px")},onLeft:kO,onRight:CO,onUp:OO,onDown:_O,edgeActions:TO});const AO=Cm({name:"Slider",configFields:[ws("stepSize",1),ws("speedMultiplier",10),ws("onChange",b),ws("onChoose",b),ws("onInit",b),ws("onDragStart",b),ws("onDragEnd",b),ws("snapToGrid",!1),ws("rounded",!0),gs("snapStart"),rs("model",Qn("mode",{x:[ws("minX",0),ws("maxX",100),os("value",(e=>Ms(e.mode.minX))),ss("getInitialValue"),Ui("manager",cO)],y:[ws("minY",0),ws("maxY",100),os("value",(e=>Ms(e.mode.minY))),ss("getInitialValue"),Ui("manager",yO)],xy:[ws("minX",0),ws("maxX",100),ws("minY",0),ws("maxY",100),os("value",(e=>Ms({x:e.mode.minX,y:e.mode.minY}))),ss("getInitialValue"),Ui("manager",EO)]})),Su("sliderBehaviours",[Yp,wu]),os("mouseIsDown",(()=>Ms(!1)))],partFields:aC,factory:(e,t,o,n)=>{const s=t=>dm(t,e,"thumb"),r=t=>dm(t,e,"spectrum"),a=t=>cm(t,e,"left-edge"),i=t=>cm(t,e,"right-edge"),l=t=>cm(t,e,"top-edge"),c=t=>cm(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&cm(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)},b=t=>{cm(t,e,"spectrum").map(Yp.focusIn)};return{uid:e.uid,dom:e.dom,components:t,behaviours:Cu(e.sliderBehaviours,[Yp.config({mode:"special",focusIn:b}),wu.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),Nl.config({channels:{[su()]:{onReceive:p}}})]),events:Pr([jr(iC(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),A.some(!0)})(t,o.event.value)})),Zr(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),jr(Ps(),h),jr(Ws(),f),jr(Gs(),((e,t)=>{b(e),h(e,t)})),jr(Ys(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),MO=da("rgb-hex-update"),DO=da("slider-update"),BO=da("palette-update"),IO="form",FO=[Su("formBehaviours",[wu])],RO=e=>"<alloy.field."+e+">",NO=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Cu(e.formBehaviours,[wu.config({store:{mode:"manual",getValue:t=>{const o=mm(t,e);return ce(o,((e,t)=>e().bind((e=>{return o=Em.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+ra(e.element)),o.fold((()=>an.error(n)),an.value);var o,n})).map(wu.getValue)))},setValue:(t,o)=>{le(o,((o,n)=>{cm(t,e,n).each((e=>{Em.getCurrent(e).each((e=>{wu.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>cm(t,e,o).bind(Em.getCurrent)}}),LO={getField:_a(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),sm(IO,RO(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=V(n,(e=>Xu({name:e,pname:RO(e)})));return ym(IO,FO,s,NO,o)}},zO=da("valid-input"),VO=da("invalid-input"),HO=da("validating-input"),PO="colorcustom.rgb.",UO=(e,t,o,n)=>{const s=(o,n)=>Wk.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Nr(e,HO,{type:o})},onValid:e=>{Nr(e,zO,{type:o,value:wu.getValue(e)})},onInvalid:e=>{Nr(e,VO,{type:o,value:wu.getValue(e)})}},validator:{validate:t=>{const o=wu.getValue(t),s=n(o)?an.value(!0):an.error(e("aria.input.invalid"));return OS(s)},validateOnLoad:!1}}),r=(o,n,r,a,i)=>{const l=e(PO+"range"),c=hk.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[ai(r)]}),d=hk.parts.field({data:i,factory:Pv,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:Ml([s(n,o),vk.config({})]),onSetValue:e=>{Wk.isInvalid(e)&&Wk.run(e).get(b)}}),u=[c,d],m="hex"!==n?[hk.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,s=t.blue;wu.setValue(e,{red:o,green:n,blue:s})},i=Zh({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{It(e.element,"background-color","#"+t.value)}))},c=km({factory:()=>{const s={red:Ms(A.some(255)),green:Ms(A.some(255)),blue:Ms(A.some(255)),hex:Ms(A.some("ffffff"))},c=e=>s[e].get(),d=(e,t)=>{s[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",A.some(t)),d("green",A.some(o)),d("blue",A.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,A.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=Qx(t);d("hex",A.some(n.value));const s=pw(n);a(e,s),u(s),Nr(e,MO,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,A.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>uw(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=rw(t);return LO.getField(e,"hex").each((t=>{ch.isFocused(t)||wu.setValue(e,{hex:o.value})})),o})(e,t);Nr(e,MO,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(PO+t+".label"),description:e(PO+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return vn(LO.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",hk.sketch(r(mw,"red",h.label,h.description,255))),o.field("green",hk.sketch(r(mw,"green",f.label,f.description,255))),o.field("blue",hk.sketch(r(mw,"blue",b.label,b.description,255))),o.field("hex",hk.sketch(r(ow,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:Ml([Wk.config({invalidClass:t("form-invalid")}),sh("rgb-form-events",[jr(zO,g),jr(VO,m),jr(HO,m)])])}))),{apis:{updateHex:(e,t)=>{wu.setValue(e,{hex:t.value}),((e,t)=>{const o=pw(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},WO=(e,t)=>{const o=km({name:"ColourPicker",configFields:[ss("dom"),ws("onValidHex",b),ws("onInvalidHex",b)],factory:o=>{const n=UO(e,t,o.onValidHex,o.onInvalidHex),s=((e,t)=>{const o=AO.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=AO.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return km({factory:r=>{const a=x({x:0,y:0}),i=Ml([Em.config({find:A.some}),ch.config({})]);return AO.sketch({dom:{tag:"div",attributes:{role:"slider","aria-valuetext":e(["Saturation {0}%, Brightness {1}%",0,0])},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:a},rounded:!1,components:[o,n],onChange:(t,o,n)=>{h(n)||Ot(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",Math.floor(n.x),Math.floor(100-n.y)])),Nr(t,BO,{value:n})},onInit:(e,t,o,n)=>{s(o.element.dom,bw(vw))},sliderBehaviours:i})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=Bw(t,100,100),r=gw(n);s(o,bw(r))})(t,o)},setThumb:(t,o,n)=>{((t,o)=>{const n=Iw(pw(o));AO.setValue(t,{x:n.saturation,y:100-n.value}),Ot(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",n.saturation,n.value]))})(o,n)}},extraApis:{}})})(e,t),r={paletteRgba:Ms(vw),paletteHue:Ms(0)},a=Zh(((e,t)=>{const o=AO.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=AO.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return AO.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"slider","aria-valuemin":0,"aria-valuemax":360,"aria-valuenow":120}},rounded:!1,model:{mode:"y",getInitialValue:x(0)},components:[o,n],sliderBehaviours:Ml([ch.config({})]),onChange:(e,t,o)=>{Ot(e.element,"aria-valuenow",Math.floor(360-3.6*o)),Nr(e,DO,{value:o})}})})(0,t)),i=Zh(s.sketch({})),l=Zh(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{s.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{AO.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{s.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=pw(e);r.paletteRgba.set(o),r.paletteHue.set(t)})(t,o),H(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:Ml([sh("colour-picker-events",[jr(MO,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,s=(e=>Iw(pw(e)))(n);g(t,n,s.hue,e)}})()),jr(BO,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,s=r.paletteHue.get(),a=Bw(s,n.x,100-n.y),i=Fw(a);g(t,i,s,e)}})()),jr(DO,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=r.paletteRgba.get(),a=Iw(s),i=Bw(n,a.saturation,a.value),l=Fw(i);g(t,l,n,e)}})())]),Em.config({find:e=>l.getOpt(e)}),Yp.config({mode:"acyclic"})])}}});return o},jO=()=>Em.config({find:A.some}),GO=e=>Em.config({find:t=>dt(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),$O=In([ws("preprocess",w),ws("postprocess",w)]),qO=(e,t)=>{const o=Jn("RepresentingConfigs.memento processors",$O,t);return wu.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=wu.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);wu.setValue(r,s)}}})},YO=(e,t,o)=>wu.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),XO=(e,t,o)=>YO(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),KO=e=>wu.config({store:{mode:"memory",initialValue:e}}),JO={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var ZO=tinymce.util.Tools.resolve("tinymce.Resource");const QO=e=>ve(e,"init");var e_=tinymce.util.Tools.resolve("tinymce.util.Tools");const t_=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},o_=da("alloy-fake-before-tabstop"),n_=da("alloy-fake-after-tabstop"),s_=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:Ml([ch.config({ignore:!0}),vk.config({})])}),r_=(e,t)=>({dom:{tag:"div",classes:["tox-navobj",...e.getOr([])]},components:[s_([o_]),t,s_([n_])],behaviours:Ml([GO(1)])}),a_=(e,t)=>{Nr(e,Zs(),{raw:{which:9,shiftKey:t}})},i_=(e,t)=>{const o=t.element;qa(o,o_)?a_(e,!0):qa(o,n_)&&a_(e,!1)},l_=e=>tk(e,["."+o_,"."+n_].join(","),T),c_=da("update-dialog"),d_=da("update-title"),u_=da("update-body"),m_=da("update-footer"),g_=da("body-send-message"),p_=da("dialog-focus-shifted"),h_=Io().browser,f_=h_.isSafari(),b_=h_.isFirefox(),v_=f_||b_,y_=h_.isChromium(),x_=({scrollTop:e,scrollHeight:t,clientHeight:o})=>Math.ceil(e)+o>=t,w_=(e,t)=>e.scrollTo(0,"bottom"===t?99999999:t),S_=(e,t,o)=>{const n=e.dom;A.from(n.contentDocument).fold(o,(e=>{let o=0;const s=((e,t)=>{const o=e.body;return A.from(!/^<!DOCTYPE (html|HTML)/.test(t)&&(!y_&&!f_||g(o)&&(0!==o.scrollTop||Math.abs(o.scrollHeight-o.clientHeight)>1))?o:e.documentElement)})(e,t).map((e=>(o=e.scrollTop,e))).forall(x_),r=()=>{const e=n.contentWindow;g(e)&&(s?w_(e,"bottom"):!s&&v_&&0!==o&&w_(e,o))};f_&&n.addEventListener("load",r,{once:!0}),e.open(),e.write(t),e.close(),f_||r()}))},k_=Ce(v_,f_?500:200).map((e=>((e,t)=>{let o=null,n=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null,n=null)},throttle:(...s)=>{n=s,c(o)&&(o=setTimeout((()=>{const t=n;o=null,n=null,e.apply(null,t)}),t))}}})(S_,e))),C_=da("toolbar.button.execute"),O_=da("common-button-display-events"),__={[gr()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events","tooltipping"],[Cr()]:["toolbar-button-events",O_],[Or()]:["toolbar-button-events","dropdown-events","tooltipping"],[Gs()]:["focusing","alloy.base.behaviour",O_]},T_=e=>It(e.element,"width",Nt(e.element,"width")),E_=(e,t,o)=>rb(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),A_=(e,t)=>E_(e,t,[]),M_=(e,t)=>E_(e,t,[nh.config({})]),D_=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[ai(o.translate(e))],behaviours:Ml([nh.config({})])}),B_=da("update-menu-text"),I_=da("update-menu-icon"),F_=(e,t,o,n)=>{const s=Ms(b),r=e.text.map((e=>Zh(D_(e,t,o.providers)))),a=e.icon.map((e=>Zh(M_(e,o.providers.icons)))),i=(e,t)=>{const o=wu.getValue(e);return ch.focus(o),Nr(o,"keydown",{raw:t.event.raw}),VS.close(o),A.some(!0)},l=e.role.fold((()=>({})),(e=>({role:e}))),c=e.ariaLabel.fold((()=>({})),(e=>({"aria-label":o.providers.translate(e)}))),d=rb("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),u=da("common-button-display-events"),m="dropdown-events",p=Zh(VS.sketch({...e.uid?{uid:e.uid}:{},...l,dom:{tag:"button",classes:[t,`${t}--select`].concat(V(e.classes,(e=>`${t}--${e}`))),attributes:{...c,...g(n)?{"data-mce-name":n}:{}}},components:Lx([a.map((e=>e.asSpec())),r.map((e=>e.asSpec())),A.some(d)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{$v(e).each((e=>ch.focus(e)))})(n)},dropdownBehaviours:Ml([...e.dropdownBehaviours,Ax((()=>e.disabled||o.providers.isDisabled())),Ex(),jk.config({}),nh.config({}),...e.tooltip.map((e=>kx.config(o.providers.tooltips.getConfig({tooltipText:o.providers.translate(e)})))).toArray(),sh(m,[Ix(e,s),Fx(e,s)]),sh(u,[Zr(((e,t)=>T_(e)))]),sh("menubutton-update-display-text",[jr(B_,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{nh.set(e,[ai(o.providers.translate(t.event.text))])}))})),jr(I_,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{nh.set(e,[M_(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:vn(__,{[Gs()]:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"],[Cr()]:["toolbar-button-events",kx.name(),m,u]}),sandboxBehaviours:Ml([Yp.config({mode:"special",onLeft:i,onRight:i}),sh("dropdown-sandbox-events",[jr(Uv,((e,t)=>{(e=>{const t=wu.getValue(e),o=Gv(e).map(qv);VS.refetch(t).get((()=>{const e=yS.getCoupled(t,"sandbox");o.each((t=>Gv(e).each((e=>((e,t)=>{wu.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),jr(Wv,((e,t)=>{((e,t)=>{(e=>tu.getState(e).bind(Zm.getHighlighted).bind(Zm.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...Nv(0,e.columns,e.presets),fakeFocus:e.searchable,onHighlightItem:HS,onCollapseMenu:(e,t,o)=>{Zm.getHighlighted(o).each((t=>{HS(e,o,t)}))},onDehighlightItem:PS}},getAnchorOverrides:()=>({maxHeightFunction:(e,t)=>{hc()(e,t-10)}}),fetch:t=>CS(k(e.fetch,t))}));return p.asSpec()},R_=e=>"separator"===e.type,N_={type:"separator"},L_=(e,t)=>{const o=((e,t)=>{const o=j(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!R_(e[e.length-1])?e.concat([N_]):e:ve(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&R_(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return W(o,((e,o)=>{if((e=>ve(e,"getSubmenuItems"))(o)){const n=(e=>{const t=be(e,"value").getOrThunk((()=>da("generated-menu-item")));return vn({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=L_(o,t);return{item:e,menus:vn(n.menus,{[e.value]:n.items}),expansions:vn(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:vn(e.menus,s.menus),items:[s.item,...e.items],expansions:vn(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},z_=(e,t,o,n)=>{const s=da("primary-menu"),r=L_(e,o.shared.providers.menuItems());if(0===r.items.length)return A.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=$S(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=ce(r.menus,((e,n)=>$S(n,e,t,o,!1,l))),d=vn(c,Bs(s,i));return A.from(qh.tieredData(s,d,r.expansions))},V_=e=>!ve(e,"items"),H_="data-value",P_=(e,t,o,n)=>V(o,(o=>V_(o)?{type:"togglemenuitem",text:o.text,value:o.value,active:o.value===n,onAction:()=>{wu.setValue(e,o.value),Nr(e,Ck,{name:t}),ch.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>P_(e,t,o.items,n)})),U_=(e,t)=>re(e,(e=>V_(e)?Ce(e.value===t,e):U_(e.items,t))),W_=km({name:"HtmlSelect",configFields:[ss("options"),Su("selectBehaviours",[ch,wu]),ws("selectClasses",[]),ws("selectAttributes",{}),gs("data")],factory:(e,t)=>{const o=V(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>Bs("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:Cu(e.selectBehaviours,[ch.config({}),wu.config({store:{mode:"manual",getValue:e=>Ja(e.element),setValue:(t,o)=>{const n=oe(e.options);G(e.options,(e=>e.value===o)).isSome()?Za(t.element,o):-1===t.element.dom.selectedIndex&&""===o&&n.each((e=>Za(t.element,e.value)))},...n}})])}}}),j_=x([ws("field1Name","field1"),ws("field2Name","field2"),Hi("onLockedChange"),Ni(["lockClass"]),ws("locked",!1),Ou("coupledFieldBehaviours",[Em,wu])]),G_=(e,t)=>Xu({factory:hk,name:e,overrides:e=>({fieldBehaviours:Ml([sh("coupled-input-behaviour",[jr(er(),(o=>{((e,t,o)=>cm(e,t,o).bind(Em.getCurrent))(o,e,t).each((t=>{cm(o,e,"lock").each((n=>{bh.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),$_=x([G_("field1","field2"),G_("field2","field1"),Xu({factory:Kh,schema:[ss("dom")],name:"lock",overrides:e=>({buttonBehaviours:Ml([bh.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),q_=Cm({name:"FormCoupledInputs",configFields:j_(),partFields:$_(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:_u(e.coupledFieldBehaviours,[Em.config({find:A.some}),wu.config({store:{mode:"manual",getValue:t=>{const o=pm(t,e,["field1","field2"]);return{[e.field1Name]:wu.getValue(o.field1()),[e.field2Name]:wu.getValue(o.field2())}},setValue:(t,o)=>{const n=pm(t,e,["field1","field2"]);ye(o,e.field1Name)&&wu.setValue(n.field1(),o[e.field1Name]),ye(o,e.field2Name)&&wu.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>cm(t,e,"field1"),getField2:t=>cm(t,e,"field2"),getLock:t=>cm(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),Y_=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return an.value({value:e,unit:o})}return an.error(e)},X_=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>ve(o,e);return e.unit===t?A.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?A.some(e.value):A.some(e.value/o[e.unit]*o[t]):A.none()},K_=e=>A.none(),J_=(e,t)=>{const o=e.label.map((e=>kk(e,t))),n=[Um.config({disabled:()=>e.disabled||t.isDisabled()}),Ex(),Yp.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(Rr(e,Ek),A.some(!0))}),sh("textfield-change",[jr(er(),((t,o)=>{Nr(t,Ck,{name:e.name})})),jr(ur(),((t,o)=>{Nr(t,Ck,{name:e.name})}))]),vk.config({})],s=e.validation.map((e=>Wk.config({getRoot:e=>it(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=wu.getValue(t),n=e.validator(o);return OS(!0===n?an.value(o):an.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(x({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(x({}),(e=>({inputmode:e})))},a=hk.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:Ml(q([n,s])),selectOnFocus:!1,factory:Pv}),i=e.multiline?{dom:{tag:"div",classes:["tox-textarea-wrap"]},components:[a]}:a,l=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),c=[Um.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{hk.getField(e).each(Um.disable)},onEnabled:e=>{hk.getField(e).each(Um.enable)}}),Ex()];return xk(o,i,l,c)},Z_=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),Q_=e=>e.dimension.property,eT=(e,t)=>e.dimension.getDimension(t),tT=(e,t)=>{const o=Z_(e,t);Xa(o,[t.shrinkingClass,t.growingClass])},oT=(e,t)=>{$a(e.element,t.openClass),ja(e.element,t.closedClass),It(e.element,Q_(t),"0px"),Ut(e.element)},nT=(e,t)=>{$a(e.element,t.closedClass),ja(e.element,t.openClass),Pt(e.element,Q_(t))},sT=(e,t,o,n)=>{o.setCollapsed(),It(e.element,Q_(t),eT(t,e.element)),tT(e,t),oT(e,t),t.onStartShrink(e),t.onShrunk(e)},rT=(e,t,o,n)=>{const s=n.getOrThunk((()=>eT(t,e.element)));o.setCollapsed(),It(e.element,Q_(t),s),Ut(e.element);const r=Z_(e,t);$a(r,t.growingClass),ja(r,t.shrinkingClass),oT(e,t),t.onStartShrink(e)},aT=(e,t,o)=>{const n=eT(t,e.element);("0px"===n?sT:rT)(e,t,o,A.some(n))},iT=(e,t,o)=>{const n=Z_(e,t),s=qa(n,t.shrinkingClass),r=eT(t,e.element);nT(e,t);const a=eT(t,e.element);(s?()=>{It(e.element,Q_(t),r),Ut(e.element)}:()=>{oT(e,t)})(),$a(n,t.shrinkingClass),ja(n,t.growingClass),nT(e,t),It(e.element,Q_(t),a),o.setExpanded(),t.onStartGrow(e)},lT=(e,t,o)=>{const n=Z_(e,t);return!0===qa(n,t.growingClass)},cT=(e,t,o)=>{const n=Z_(e,t);return!0===qa(n,t.shrinkingClass)};var dT=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){Pt(e.element,Q_(t));const o=eT(t,e.element);It(e.element,Q_(t),o)}},grow:(e,t,o)=>{o.isExpanded()||iT(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&aT(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&sT(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:lT,isShrinking:cT,isTransitioning:(e,t,o)=>lT(e,t)||cT(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?aT:iT)(e,t,o)},disableTransitions:tT,immediateGrow:(e,t,o)=>{o.isExpanded()||(nT(e,t),It(e.element,Q_(t),eT(t,e.element)),tT(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),uT=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return Ma(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Bs(t.dimension.property,"0px")})},events:(e,t)=>Pr([Jr(sr(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(tT(o,e),t.isExpanded()&&Pt(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),mT=[ss("closedClass"),ss("openClass"),ss("shrinkingClass"),ss("growingClass"),gs("getAnimationRoot"),zi("onShrunk"),zi("onStartShrink"),zi("onGrown"),zi("onStartGrow"),ws("expanded",!1),rs("dimension",Qn("property",{width:[Ui("property","width"),Ui("getDimension",(e=>Qt(e)+"px"))],height:[Ui("property","height"),Ui("getDimension",(e=>Gt(e)+"px"))]}))];const gT=Bl({fields:mT,name:"sliding",active:uT,apis:dT,state:Object.freeze({__proto__:null,init:e=>{const t=Ms(e.expanded);return Ea({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:k(t.set,!1),setExpanded:k(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),pT=e=>({isEnabled:()=>!Um.isDisabled(e),setEnabled:t=>Um.set(e,!t),setActive:t=>{const o=e.element;t?(ja(o,"tox-tbtn--enabled"),Ot(o,"aria-pressed",!0)):($a(o,"tox-tbtn--enabled"),Mt(o,"aria-pressed"))},isActive:()=>qa(e.element,"tox-tbtn--enabled"),setText:t=>{Nr(e,B_,{text:t})},setIcon:t=>Nr(e,I_,{icon:t})}),hT=(e,t,o,n,s=!0,r)=>F_({text:e.text,icon:e.icon,tooltip:e.tooltip,ariaLabel:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?US(t):""};e.fetch((t=>{n(z_(t,vv.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s,pT(t))},onSetup:e.onSetup,getApi:pT,columns:1,presets:"normal",classes:[],dropdownBehaviours:[...s?[vk.config({})]:[]]},t,o.shared,r),fT=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{zl(t.element),Nr(t,Tk,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(V(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},bT=e=>({dom:{tag:"span",classes:["tox-tree__label"],attributes:{"aria-label":e}},components:[ai(e)]}),vT=da("leaf-label-event-id"),yT=({leaf:e,onLeafAction:t,visible:o,treeId:n,selectedId:s,backstage:r})=>{const a=e.menu.map((e=>hT(e,"tox-mbtn",r,A.none(),o))),i=[bT(e.title)];return a.each((e=>i.push(e))),Kh.sketch({dom:{tag:"div",classes:["tox-tree--leaf__label","tox-trbtn"].concat(o?["tox-tree--leaf__label--visible"]:[])},components:i,role:"treeitem",action:o=>{t(e.id),o.getSystem().broadcastOn([`update-active-item-${n}`],{value:e.id})},eventOrder:{[Zs()]:[vT,"keying"]},buttonBehaviours:Ml([...o?[vk.config({})]:[],bh.config({toggleClass:"tox-trbtn--enabled",toggleOnExecute:!1,aria:{mode:"selected"}}),Nl.config({channels:{[`update-active-item-${n}`]:{onReceive:(t,o)=>{(o.value===e.id?bh.on:bh.off)(t)}}}}),sh(vT,[Zr(((t,o)=>{s.each((o=>{(o===e.id?bh.on:bh.off)(t)}))})),jr(Zs(),((e,t)=>{const o="ArrowLeft"===t.event.raw.code,n="ArrowRight"===t.event.raw.code;o?(vi(e.element,".tox-tree--directory").each((t=>{e.getSystem().getByDom(t).each((e=>{xi(t,".tox-tree--directory__label").each((t=>{e.getSystem().getByDom(t).each(ch.focus)}))}))})),t.stop()):n&&t.stop()}))])])})},xT=da("directory-label-event-id"),wT=({directory:e,visible:t,noChildren:o,backstage:n})=>{const s=e.menu.map((e=>hT(e,"tox-mbtn",n,A.none()))),r=[{dom:{tag:"div",classes:["tox-chevron"]},components:[(a="chevron-right",i=n.shared.providers.icons,((e,t,o)=>rb(e,{tag:"span",classes:["tox-tree__icon-wrap","tox-icon"],behaviours:[]},t))(a,i))]},bT(e.title)];var a,i;s.each((e=>{r.push(e)}));const l=t=>{vi(t.element,".tox-tree--directory").each((o=>{t.getSystem().getByDom(o).each((o=>{const n=!bh.isOn(o);bh.toggle(o),Nr(t,"expand-tree-node",{expanded:n,node:e.id})}))}))};return Kh.sketch({dom:{tag:"div",classes:["tox-tree--directory__label","tox-trbtn"].concat(t?["tox-tree--directory__label--visible"]:[])},components:r,action:l,eventOrder:{[Zs()]:[xT,"keying"]},buttonBehaviours:Ml([...t?[vk.config({})]:[],sh(xT,[jr(Zs(),((e,t)=>{const n="ArrowRight"===t.event.raw.code,s="ArrowLeft"===t.event.raw.code;n&&o&&t.stop(),(n||s)&&vi(e.element,".tox-tree--directory").each((o=>{e.getSystem().getByDom(o).each((o=>{!bh.isOn(o)&&n||bh.isOn(o)&&s?(l(e),t.stop()):s&&!bh.isOn(o)&&(vi(o.element,".tox-tree--directory").each((e=>{xi(e,".tox-tree--directory__label").each((e=>{o.getSystem().getByDom(e).each(ch.focus)}))})),t.stop())}))}))}))])])})},ST=({children:e,onLeafAction:t,visible:o,treeId:n,expandedIds:s,selectedId:r,backstage:a})=>({dom:{tag:"div",classes:["tox-tree--directory__children"]},components:e.map((e=>"leaf"===e.type?yT({leaf:e,selectedId:r,onLeafAction:t,visible:o,treeId:n,backstage:a}):CT({directory:e,expandedIds:s,selectedId:r,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:a}))),behaviours:Ml([gT.config({dimension:{property:"height"},closedClass:"tox-tree--directory__children--closed",openClass:"tox-tree--directory__children--open",growingClass:"tox-tree--directory__children--growing",shrinkingClass:"tox-tree--directory__children--shrinking",expanded:o}),nh.config({})])}),kT=da("directory-event-id"),CT=({directory:e,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:s,expandedIds:r,selectedId:a})=>{const{children:i}=e,l=Ms(r),c=r.includes(e.id);return{dom:{tag:"div",classes:["tox-tree--directory"],attributes:{role:"treeitem"}},components:[wT({directory:e,visible:o,noChildren:0===e.children.length,backstage:s}),ST({children:i,expandedIds:r,selectedId:a,onLeafAction:t,visible:c,treeId:n,backstage:s})],behaviours:Ml([sh(kT,[Zr(((e,t)=>{bh.set(e,c)})),jr("expand-tree-node",((e,t)=>{const{expanded:o,node:n}=t.event;l.set(o?[...l.get(),n]:l.get().filter((e=>e!==n)))}))]),bh.config({...e.children.length>0?{aria:{mode:"expanded"}}:{},toggleClass:"tox-tree--directory--expanded",onToggled:(e,o)=>{const r=e.components()[1],c=(d=o,i.map((e=>"leaf"===e.type?yT({leaf:e,selectedId:a,onLeafAction:t,visible:d,treeId:n,backstage:s}):CT({directory:e,expandedIds:l.get(),selectedId:a,onLeafAction:t,labelTabstopping:d,treeId:n,backstage:s}))));var d;o?gT.grow(r):gT.shrink(r),nh.set(r,c)}})])}},OT=da("tree-event-id");var _T=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Pr([jr(e.event,o),Qr((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[jr(e,(()=>t.cancel()))])).getOr([])))}});const TT=e=>{const t=Ms(null);return Ea({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var ET=Object.freeze({__proto__:null,throttle:TT,init:e=>e.stream.streams.state(e)}),AT=[rs("stream",Qn("mode",{throttle:[ss("delay"),ws("stopEvent",!0),Ui("streams",{setup:(e,t)=>{const o=e.stream,n=t_(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:TT})]})),ws("event","input"),gs("cancelEvent"),Hi("onStream")];const MT=Bl({fields:AT,name:"streaming",active:_T,state:ET}),DT=(e,t,o)=>{const n=wu.getValue(o);wu.setValue(t,n),IT(t)},BT=(e,t)=>{const o=e.element,n=Ja(o),s=o.dom;"number"!==Tt(o,"type")&&t(s,n)},IT=e=>{BT(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},FT=x("alloy.typeahead.itemexecute"),RT=x([gs("lazySink"),ss("fetch"),ws("minChars",5),ws("responseTime",1e3),zi("onOpen"),ws("getHotspot",A.some),ws("getAnchorOverrides",x({})),ws("layouts",A.none()),ws("eventOrder",{}),As("model",{},[ws("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),ws("selectsOver",!0),ws("populateFromBrowse",!0)]),zi("onSetValue"),Vi("onExecute"),zi("onItemExecute"),ws("inputClasses",[]),ws("inputAttributes",{}),ws("inputStyles",{}),ws("matchWidth",!0),ws("useMinWidth",!1),ws("dismissOnBlur",!0),Ni(["openClass"]),gs("initialData"),Su("typeaheadBehaviours",[ch,wu,MT,Yp,bh,yS]),os("lazyTypeaheadComp",(()=>Ms(A.none))),os("previewing",(()=>Ms(!0)))].concat(Lv()).concat(NS())),NT=x([Ku({schema:[Ri()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=wu.getValue(t),s=e.getDisplayText(n),r=wu.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?A.some((()=>{DT(0,t,o),((e,t)=>{BT(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):A.none()}return A.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Zm.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&DT(e.model,t,n),Et(n.element,"id").each((e=>Ot(t.element,"aria-activedescendant",e)))}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Nr(e,FT(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&DT(e.model,t,o)}))}})})]),LT=Cm({name:"Typeahead",configFields:RT(),partFields:NT(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=yS.getCoupled(t,"sandbox");if(tu.isOpen(r))Em.getCurrent(r).each((e=>{Zm.getHighlighted(e).fold((()=>{s(e)}),(()=>{Hr(r,e.element,"keydown",o)}))}));else{const o=e=>{Em.getCurrent(e).each(s)};AS(e,a(t),t,r,n,o,Gh.HighlightMenuAndItem).get(b)}},r=zv(e),a=e=>t=>t.map((t=>{const o=fe(t.menus),n=Y(o,(e=>U(e.items,(e=>"item"===e.type))));return wu.getState(e).update(V(n,(e=>e.data))),t})),i=e=>Em.getCurrent(e),l="typeaheadevents",c=[ch.config({}),wu.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>Ja(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{Za(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>Bs("initialValue",e))).getOr({})}}),MT.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=yS.getCoupled(t,"sandbox");if(ch.isFocused(t)&&Ja(t.element).length>=e.minChars){const o=i(s).bind((e=>Zm.getHighlighted(e).map(wu.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Zm.highlightFirst(t)}),(e=>{Zm.highlightBy(t,(t=>wu.getValue(t).value===e.value)),Zm.getHighlighted(t).orThunk((()=>(Zm.highlightFirst(t),A.none())))}))}))};AS(e,a(t),t,s,n,r,Gh.HighlightJustMenu).get(b)}},cancelEvent:vr()}),Yp.config({mode:"special",onDown:(e,t)=>(s(e,t,Zm.highlightFirst),A.some(!0)),onEscape:e=>{const t=yS.getCoupled(e,"sandbox");return tu.isOpen(t)?(tu.close(t),A.some(!0)):A.none()},onUp:(e,t)=>(s(e,t,Zm.highlightLast),A.some(!0)),onEnter:t=>{const o=yS.getCoupled(t,"sandbox"),n=tu.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Zm.getHighlighted(e))).map((e=>(Nr(t,FT(),{item:e}),!0)));{const s=wu.getValue(t);return Rr(t,vr()),e.onExecute(o,t,s),n&&tu.close(o),A.some(!0)}}}),bh.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),yS.config({others:{sandbox:t=>FS(e,t,{onOpen:()=>bh.on(t),onClose:()=>{e.lazyTypeaheadComp.get().each((e=>Mt(e.element,"aria-activedescendant"))),bh.off(t)}})}}),sh(l,[Zr((t=>{e.lazyTypeaheadComp.set(A.some(t))})),Qr((t=>{e.lazyTypeaheadComp.set(A.none())})),ta((t=>{const o=b;DS(e,a(t),t,n,o,Gh.HighlightMenuAndItem).get(b)})),jr(FT(),((t,o)=>{const n=yS.getCoupled(t,"sandbox");DT(e.model,t,o.event.item),Rr(t,vr()),e.onItemExecute(t,n,o.event.item,wu.getValue(t)),tu.close(n),IT(t)}))].concat(e.dismissOnBlur?[jr(dr(),(e=>{const t=yS.getCoupled(e,"sandbox");Ul(t.element).isNone()&&tu.close(t)}))]:[]))],d={[Or()]:[wu.name(),MT.name(),l],...e.eventOrder};return{uid:e.uid,dom:Hv(vn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...Cu(e.typeaheadBehaviours,c)},eventOrder:d}}}),zT=e=>({...e,toCached:()=>zT(e.toCached()),bindFuture:t=>zT(e.bind((e=>e.fold((e=>OS(an.error(e))),(e=>t(e)))))),bindResult:t=>zT(e.map((e=>e.bind(t)))),mapResult:t=>zT(e.map((e=>e.map(t)))),mapError:t=>zT(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>zT(CS((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(an.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),VT=e=>zT(CS(e)),HT=(e,t,o=[],n,s,r,a)=>{const i=t.fold((()=>({})),(e=>({action:e}))),l={buttonBehaviours:Ml([Ax((()=>!e.enabled||a.isDisabled())),Ex(),vk.config({}),...r.map((e=>kx.config(a.tooltips.getConfig({tooltipText:a.translate(e)})))).toArray(),sh("button press",[Wr("click"),Wr("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...i},c=vn(l,{dom:n});return vn(c,{components:s})},PT=(e,t,o,n=[],s)=>{const r={tag:"button",classes:["tox-tbtn"],attributes:{...e.tooltip.map((e=>({"aria-label":o.translate(e)}))).getOr({}),"data-mce-name":s}},a=e.icon.map((e=>A_(e,o.icons))),i=Lx([a]);return HT(e,t,n,r,i,e.tooltip,o)},UT=e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}},WT=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>A_(e,o.icons))),i=[a.getOrThunk((()=>ai(r)))],l=e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary"),c={tag:"button",classes:[...UT(l),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s],attributes:{"aria-label":r,"data-mce-name":e.text}},d=e.icon.map(x(r));return HT(e,t,n,c,i,d,o)},jT=(e,t,o,n=[],s=[])=>{const r=WT(e,A.some(t),o,n,s);return Kh.sketch(r)},GT=(e,t)=>o=>{"custom"===t?Nr(o,Tk,{name:e,value:{}}):"submit"===t?Rr(o,Ek):"cancel"===t?Rr(o,_k):console.error("Unknown button type: ",t)},$T=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,type:"menubutton",search:A.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:fT(n.items,t,o)},r=Zh(hT(s,"tox-tbtn",o,A.none(),!0,e.text.or(e.tooltip).getOrUndefined()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=GT(e.name,t),s={...e,borderless:!1};return jT(s,n,o.shared.providers,[])}if(((e,t)=>"togglebutton"===t)(0,t))return((e,t,o)=>{var n,s;const r=e.icon.map((e=>M_(e,t.icons))).map(Zh),a=e.buttonType.getOr(e.primary?"primary":"secondary"),i={...e,name:null!==(n=e.name)&&void 0!==n?n:"",primary:"primary"===a,tooltip:e.tooltip,enabled:null!==(s=e.enabled)&&void 0!==s&&s,borderless:!1},l=i.tooltip.or(e.text).map((e=>({"aria-label":t.translate(e)}))).getOr({}),c=UT(null!=a?a:"secondary"),d=e.icon.isSome()&&e.text.isSome(),u={tag:"button",classes:[...c.concat(e.icon.isSome()?["tox-button--icon"]:[]),...e.active?["tox-button--enabled"]:[],...d?["tox-button--icon-and-text"]:[]],attributes:{...l,...g(o)?{"data-mce-name":o}:{}}},m=t.translate(e.text.getOr("")),p=ai(m),h=[...Lx([r.map((e=>e.asSpec()))]),...e.text.isSome()?[p]:[]],f=HT(i,A.some((o=>{Nr(o,Tk,{name:e.name,value:{setIcon:e=>{r.map((n=>n.getOpt(o).each((o=>{nh.set(o,[M_(e,t.icons)])}))))}}})})),[],u,h,e.tooltip,t);return Kh.sketch(f)})(e,o.shared.providers,e.text.or(e.tooltip).getOrUndefined());throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},qT={type:"separator"},YT=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),XT=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),KT=(e,t)=>(e=>V(e,YT))(((e,t)=>U(t,(t=>t.type===e)))(e,t)),JT=e=>KT("header",e.targets),ZT=e=>KT("anchor",e.targets),QT=e=>A.from(e.anchorTop).map((e=>XT("<top>",e))).toArray(),eE=e=>A.from(e.anchorBottom).map((e=>XT("<bottom>",e))).toArray(),tE=(e,t)=>{const o=e.toLowerCase();return U(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return Te(n.toLowerCase(),o)||Te(s.toLowerCase(),o)}))},oE=da("aria-invalid"),nE=(e,t)=>{e.dom.checked=t},sE=e=>e.dom.checked,rE=e=>(t,o,n,s)=>be(o,"name").fold((()=>e(o,s,A.none())),(r=>t.field(r,e(o,s,be(n,r))))),aE={bar:rE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:V(e.items,t.interpreter)}))(e,t.shared))),collection:rE(((e,t,o)=>Ik(e,t.shared.providers,o))),alertbanner:rE(((e,t)=>((e,t)=>{const o=ob(e.icon,t.icons);return uk.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:e.url?void 0:o},components:e.url?[Kh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:o,attributes:{title:t.translate(e.iconTooltip)}},action:t=>Nr(t,Tk,{name:"alert-banner",value:e.url}),buttonBehaviours:Ml([nb()])})]:void 0},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]})})(e,t.shared.providers))),input:rE(((e,t,o)=>((e,t,o)=>J_({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:rE(((e,t,o)=>((e,t,o)=>J_({name:e.name,multiline:!0,label:e.label,inputMode:A.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:rE(((e,t)=>((e,t)=>{const o="tox-label";return{dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"label",classes:[o,..."center"===e.align?[`${o}--center`]:[],..."end"===e.align?[`${o}--end`]:[]]},components:[ai(t.providers.translate(e.label))]},...V(e.items,t.interpreter)],behaviours:Ml([jO(),nh.config({}),(n=A.none(),XO(n,oa,na)),Yp.config({mode:"acyclic"})])};var n})(e,t.shared))),iframe:(IA=(e,t,o)=>((e,t,o)=>{const n="tox-dialog__iframe",s=e.transparent?[]:[`${n}--opaque`],r=e.border?["tox-navobj-bordered"]:[],a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...e.sandboxed?{sandbox:"allow-scripts allow-same-origin"}:{}},i=((e,t)=>{const o=Ms(e.getOr(""));return{getValue:e=>o.get(),setValue:(e,n)=>{if(o.get()!==n){const o=e.element,s=()=>Ot(o,"srcdoc",n);t?k_.fold(x(S_),(e=>e.throttle))(o,n,s):s()}o.set(n)}}})(o,e.streamContent),l=e.label.map((e=>kk(e,t))),c=hk.parts.field({factory:{sketch:e=>r_(A.from(r),{uid:e.uid,dom:{tag:"iframe",attributes:a,classes:[n,...s]},behaviours:Ml([vk.config({}),ch.config({}),YO(o,i.getValue,i.setValue),Nl.config({channels:{[p_]:{onReceive:(e,t)=>{t.newFocus.each((t=>{it(e.element).each((o=>{(et(e.element,t)?ja:$a)(o,"tox-navobj-bordered-focus")}))}))}}}})])})}});return xk(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n)=>{const s=vn(t,{source:"dynamic"});return rE(IA)(e,s,o,n)}),button:rE(((e,t)=>((e,t)=>{const o=GT(e.name,"custom");return n=A.none(),s=hk.parts.field({factory:Kh,...WT(e,A.some(o),t,[KO(""),jO()])}),xk(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:rE(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),A.some(!0)),s=hk.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:Ml([jO(),Um.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{it(e.element).each((e=>ja(e,"tox-checkbox--disabled")))},onEnabled:e=>{it(e.element).each((e=>$a(e,"tox-checkbox--disabled")))}}),vk.config({}),ch.config({}),XO(o,sE,nE),Yp.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),sh("checkbox-events",[jr(tr(),((t,o)=>{Nr(t,Ck,{name:e.name})}))])])}),r=hk.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[ai(t.translate(e.label))],behaviours:Ml([jk.config({})])}),a=e=>rb("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=Zh({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return hk.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:Ml([Um.config({disabled:()=>!e.enabled||t.isDisabled()}),Ex()])})})(e,t.shared.providers,o))),colorinput:rE(((e,t,o)=>((e,t,o,n)=>{const s=hk.parts.field({factory:Pv,inputClasses:["tox-textfield"],data:n,onSetValue:e=>Wk.run(e).get(b),inputBehaviours:Ml([Um.config({disabled:t.providers.isDisabled}),Ex(),vk.config({}),Wk.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>it(e.element),notify:{onValid:e=>{const t=wu.getValue(e);Nr(e,Gk,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=wu.getValue(e);if(0===t.length)return OS(an.value(!0));{const e=Ne("span");It(e,"background-color",t);const o=zt(e,"background-color").fold((()=>an.error("blah")),(e=>an.value(t)));return OS(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>kk(e,t.providers))),a=(e,t)=>{Nr(e,$k,{value:t})},i=Zh(((e,t)=>VS.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:Ml([Ax(t.providers.isDisabled),Ex(),jk.config({}),vk.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>CS((t=>e.fetch(t))).map((n=>A.from(qS(vn(aS(da("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,vv.CLOSE_ON_EXECUTE,T,t.providers),{movement:lS(e.columns,e.presets)}))))),parts:{menu:Nv(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[ml,ul,fl],onLtr:()=>[ul,ml,fl]},components:[],fetch:Zw(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>Rr(t,qk)),(o=>{a(t,o),Dw(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))}},t));return hk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:Ml([sh("form-field-events",[jr(Gk,((t,o)=>{i.getOpt(t).each((e=>{It(e.element,"background-color",o.event.color)})),Nr(t,Ck,{name:e.name})})),jr($k,((e,t)=>{hk.getField(e).each((o=>{wu.setValue(o,t.event.value),Em.getCurrent(e).each(ch.focus)}))})),jr(qk,((e,t)=>{hk.getField(e).each((t=>{Em.getCurrent(e).each(ch.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:rE(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=WO((e=>t=>r(t)?e.translate(JO[t]):e.translate(t))(t),n),a=Zh(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Nr(e,Tk,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Nr(e,Tk,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[a.asSpec()],behaviours:Ml([YO(o,(e=>{const t=a.get(e);return Em.getCurrent(t).bind((e=>wu.getValue(e).hex)).map((e=>"#"+_e(e,"#"))).getOr("")}),((e,t)=>{const o=A.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>te(e,1))),n=a.get(e);Em.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{wu.setValue(e,{hex:o.getOr("")}),LO.getField(e,"hex").each((e=>{Rr(e,er())}))}))})),jO()])}})(0,t.shared.providers,o))),dropzone:rE(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{H(e,(e=>{e(t,o)}))},r=(e,t)=>{var o;if(!Um.isDisabled(e)){const n=t.event.raw;i(e,null===(o=n.dataTransfer)||void 0===o?void 0:o.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{n&&(wu.setValue(o,((e,t)=>{const o=e_.explode(t.getOption("images_file_types"));return U(se(e),(e=>N(o,(t=>Ae(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),Nr(o,Ck,{name:e.name}))},l=Zh({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:Ml([sh("input-file-events",[Xr(or()),Xr(hr())])])}),c=e.label.map((e=>kk(e,t))),d=hk.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:Ml([KO(o.getOr([])),jO(),Um.config({}),bh.config({toggleClass:"dragenter",toggleOnExecute:!1}),sh("dropzone-events",[jr("dragenter",s([n,bh.toggle])),jr("dragleave",s([n,bh.toggle])),jr("dragover",n),jr("drop",s([n,r])),jr(tr(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[ai(t.translate("Drop an image here"))]},Kh.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[ai(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:Ml([vk.config({}),Ax(t.isDisabled),Ex()])})]}]})}});return xk(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:rE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:V(e.items,t.interpreter)}))(e,t.shared))),listbox:rE(((e,t,o)=>((e,t,o)=>{const n=t.shared.providers,s=o.bind((t=>U_(e.items,t))).orThunk((()=>oe(e.items).filter(V_))),r=e.label.map((e=>kk(e,n))),a=hk.parts.field({dom:{},factory:{sketch:o=>F_({uid:o.uid,text:s.map((e=>e.text)),icon:A.none(),tooltip:A.none(),role:A.none(),ariaLabel:e.label,fetch:(o,n)=>{const s=P_(o,e.name,e.items,wu.getValue(o));n(z_(s,vv.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:A.none()}))},onSetup:x(b),getApi:x({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[vk.config({}),YO(s.map((e=>e.value)),(e=>Tt(e.element,H_)),((t,o)=>{U_(e.items,o).each((e=>{Ot(t.element,H_,e.value),Nr(t,B_,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),i={dom:{tag:"div",classes:["tox-listboxfield"]},components:[a]};return hk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([r.toArray(),[i]]),fieldBehaviours:Ml([Um.config({disabled:x(!e.enabled),onDisabled:e=>{hk.getField(e).each(Um.disable)},onEnabled:e=>{hk.getField(e).each(Um.enable)}})])})})(e,t,o))),selectbox:rE(((e,t,o)=>((e,t,o)=>{const n=V(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>kk(e,t))),r=hk.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:W_,selectBehaviours:Ml([Um.config({disabled:()=>!e.enabled||t.isDisabled()}),vk.config({}),sh("selectbox-change",[jr(tr(),((t,o)=>{Nr(t,Ck,{name:e.name})}))])])}),a=e.size>1?A.none():A.some(rb("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:q([[r],a.toArray()])};return hk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([s.toArray(),[i]]),fieldBehaviours:Ml([Um.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{hk.getField(e).each(Um.disable)},onEnabled:e=>{hk.getField(e).each(Um.enable)}}),Ex()])})})(e,t.shared.providers,o))),sizeinput:rE(((e,t)=>((e,t)=>{let o=K_;const n=da("ratio-event"),s=e=>rb(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=e.label.getOr("Constrain proportions"),a=t.translate(r),i=q_.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":a,"data-mce-name":r}},components:[s("lock"),s("unlock")],buttonBehaviours:Ml([Um.config({disabled:()=>!e.enabled||t.isDisabled()}),Ex(),vk.config({}),kx.config(t.tooltips.getConfig({tooltipText:a}))])}),l=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),c=o=>hk.parts.field({factory:Pv,inputClasses:["tox-textfield"],inputBehaviours:Ml([Um.config({disabled:()=>!e.enabled||t.isDisabled()}),Ex(),vk.config({}),sh("size-input-events",[jr(Ks(),((e,t)=>{Nr(e,n,{isField1:o})})),jr(tr(),((t,o)=>{Nr(t,Ck,{name:e.name})}))])]),selectOnFocus:!1}),d=e=>({dom:{tag:"label",classes:["tox-label"]},components:[ai(t.translate(e))]}),u=q_.parts.field1(l([hk.parts.label(d("Width")),c(!0)])),m=q_.parts.field2(l([hk.parts.label(d("Height")),c(!1)]));return q_.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[u,m,l([d("\xa0"),i])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{Y_(wu.getValue(e)).each((e=>{o(e).each((e=>{wu.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:Ml([Um.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{q_.getField1(e).bind(hk.getField).each(Um.disable),q_.getField2(e).bind(hk.getField).each(Um.disable),q_.getLock(e).each(Um.disable)},onEnabled:e=>{q_.getField1(e).bind(hk.getField).each(Um.enable),q_.getField2(e).bind(hk.getField).each(Um.enable),q_.getLock(e).each(Um.enable)}}),Ex(),sh("size-input-events2",[jr(n,((e,t)=>{const n=t.event.isField1,s=n?q_.getField1(e):q_.getField2(e),r=n?q_.getField2(e):q_.getField1(e),a=s.map(wu.getValue).getOr(""),i=r.map(wu.getValue).getOr("");o=((e,t)=>{const o=Y_(e).toOptional(),n=Y_(t).toOptional();return Se(o,n,((e,t)=>X_(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>X_(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(K_))).getOr(K_)})(a,i)}))])])})})(e,t.shared.providers))),slider:rE(((e,t,o)=>((e,t,o)=>{const n=AO.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ai(t.translate(e.label))]}),s=AO.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=AO.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return AO.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:x(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:Ml([jO(),ch.config({})]),onChoose:(t,o,n)=>{Nr(t,Ck,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:rE(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=wu.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":oE,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{var n,s;const r=wu.getValue(t),a=null!==(s=null===(n=null==r?void 0:r.meta)||void 0===n?void 0:n.text)&&void 0!==s?s:r.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=tE(a,(e=>V(e,(e=>XT(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,tE(a,JT(t)),tE(a,q([QT(t),ZT(t),eE(t)]))],j(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(qT,t)),[])):n;var s}))})(e.filetype,n,o),r=z_(s,vv.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:A.none()});return OS(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(Wk)&&Wk.run(e).get(b)},typeaheadBehaviours:Ml([...o.getValidationHandler().map((t=>Wk.config({getRoot:e=>it(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{Ot(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=wu.getValue(o);return VT((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=an.error(e.message);o(t)}else{const t=an.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),Um.config({disabled:()=>!e.enabled||s.isDisabled()}),vk.config({}),sh("urlinput-events",[jr(er(),(t=>{const o=Ja(t.element),n=o.trim();n!==o&&Za(t.element,n),"file"===e.filetype&&Nr(t,Ck,{name:e.name})})),jr(tr(),(t=>{Nr(t,Ck,{name:e.name}),r(t)})),jr(ur(),(t=>{Nr(t,Ck,{name:e.name}),r(t)}))])]),eventOrder:{[er()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:Nv(0,0,"normal")},onExecute:(e,t,o)=>{Nr(t,Ek,{})},onItemExecute:(t,o,n,s)=>{r(t),Nr(t,Ck,{name:e.name})}},i=hk.parts.field({...a,factory:LT}),l=e.label.map((e=>kk(e,s))),c=Zh(((e,t,o=e,n=e)=>rb(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",A.some(oE),"warning")),d=Zh({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=da("browser.url.event"),g=Zh({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:Ml([Um.config({disabled:()=>!e.enabled||s.isDisabled()})])}),p=Zh(jT({name:e.name,icon:A.some("browse"),text:e.picker_text.or(e.label).getOr(""),enabled:e.enabled,primary:!1,buttonType:A.none(),borderless:!0},(e=>Rr(e,m)),s,[],["tox-browse-url"]));return hk.sketch({dom:Sk([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:q([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:Ml([Um.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{hk.getField(e).each(Um.disable),p.getOpt(e).each(Um.disable)},onEnabled:e=>{hk.getField(e).each(Um.enable),p.getOpt(e).each(Um.enable)}}),Ex(),sh("url-input-events",[jr(m,(t=>{Em.getCurrent(t).each((o=>{const n=wu.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{wu.setValue(o,n),Nr(t,Ck,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:rE((e=>{const t=ac(),o=Zh({dom:{tag:e.tag}}),n=ac(),s=!QO(e)&&e.onFocus.isSome()?[ch.config({onFocus:t=>{e.onFocus.each((e=>{e(t.element.dom)}))}}),vk.config({})]:[];return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:Ml([sh("custom-editor-events",[Zr((s=>{o.getOpt(s).each((o=>{(QO(e)?e.init(o.element.dom):ZO.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),YO(A.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),jO()].concat(s)),components:[o.asSpec()]}})),htmlpanel:rE(((e,t)=>((e,t)=>"presentation"===e.presets?uk.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html},containerBehaviours:Ml([kx.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{wi(e.element,"[data-mce-tooltip]:hover").orThunk((()=>Ul(e.element))).each((o=>{Et(o,"data-mce-tooltip").each((o=>{kx.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-normal",anchor:e=>({type:"node",node:wi(e.element,"[data-mce-tooltip]:hover").orThunk((()=>Ul(e.element).filter((e=>Et(e,"data-mce-tooltip").isSome())))),root:e.element,layouts:{onLtr:x([fl,hl,ul,gl,ml,pl]),onRtl:x([fl,hl,ul,gl,ml,pl])},bubble:xc(0,-2,{})})})])}):uk.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:Ml([vk.config({}),ch.config({})])}))(e,t.shared.providers))),imagepreview:rE(((e,t,o)=>((e,t)=>{const o=Ms(t.getOr({url:""})),n=Zh({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=Zh({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:A.from(e.zoom),cachedWidth:A.from(e.cachedWidth),cachedHeight:A.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:Ml([jO(),YO(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=Qt(e),s=Gt(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Qt(e.element),Gt(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{Ft(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==Tt(n,"src")&&(Ot(n,"src",t.url),$a(e.element,"tox-imagepreview__loaded")),a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[lc(s,"load",o),lc(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>H(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(ja(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:rE(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:V(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:V(n,(e=>({dom:{tag:"tr"},components:V(e,o)})))})],behaviours:Ml([vk.config({}),ch.config({})])};var n,s})(e,t.shared.providers))),tree:rE(((e,t)=>((e,t)=>{const o=e.onLeafAction.getOr(b),n=e.onToggleExpand.getOr(b),s=e.defaultExpandedIds,r=Ms(s),a=Ms(e.defaultSelectedId),i=da("tree-id"),l=(n,s)=>e.items.map((e=>"leaf"===e.type?yT({leaf:e,selectedId:n,onLeafAction:o,visible:!0,treeId:i,backstage:t}):CT({directory:e,selectedId:n,onLeafAction:o,expandedIds:s,labelTabstopping:!0,treeId:i,backstage:t})));return{dom:{tag:"div",classes:["tox-tree"],attributes:{role:"tree"}},components:l(a.get(),r.get()),behaviours:Ml([Yp.config({mode:"flow",selector:".tox-tree--leaf__label--visible, .tox-tree--directory__label--visible",cycles:!1}),sh(OT,[jr("expand-tree-node",((e,t)=>{const{expanded:o,node:s}=t.event;r.set(o?[...r.get(),s]:r.get().filter((e=>e!==s))),n(r.get(),{expanded:o,node:s})}))]),Nl.config({channels:{[`update-active-item-${i}`]:{onReceive:(e,t)=>{a.set(A.some(t.value)),nh.set(e,l(A.some(t.value),r.get()))}}}}),nh.config({})])}})(e,t))),panel:rE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:V(e.items,t.shared.interpreter)}))(e,t)))},iE={field:(e,t)=>t,record:x([])},lE=(e,t,o,n)=>{const s=vn(n,{shared:{interpreter:t=>cE(e,t,o,s)}});return cE(e,t,o,s)},cE=(e,t,o,n)=>be(aE,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(s=>s(e,t,o,n))),dE=(e,t,o)=>cE(iE,e,t,o),uE="layout-inset",mE=e=>e.x,gE=(e,t)=>e.x+e.width/2-t.width/2,pE=(e,t)=>e.x+e.width-t.width,hE=e=>e.y,fE=(e,t)=>e.y+e.height-t.height,bE=(e,t)=>e.y+e.height/2-t.height/2,vE=(e,t,o)=>Gi(pE(e,t),fE(e,t),o.insetSouthwest(),Ki(),"southwest",nl(e,{right:0,bottom:3}),uE),yE=(e,t,o)=>Gi(mE(e),fE(e,t),o.insetSoutheast(),Xi(),"southeast",nl(e,{left:1,bottom:3}),uE),xE=(e,t,o)=>Gi(pE(e,t),hE(e),o.insetNorthwest(),Yi(),"northwest",nl(e,{right:0,top:2}),uE),wE=(e,t,o)=>Gi(mE(e),hE(e),o.insetNortheast(),qi(),"northeast",nl(e,{left:1,top:2}),uE),SE=(e,t,o)=>Gi(gE(e,t),hE(e),o.insetNorth(),Ji(),"north",nl(e,{top:2}),uE),kE=(e,t,o)=>Gi(gE(e,t),fE(e,t),o.insetSouth(),Zi(),"south",nl(e,{bottom:3}),uE),CE=(e,t,o)=>Gi(pE(e,t),bE(e,t),o.insetEast(),el(),"east",nl(e,{right:0}),uE),OE=(e,t,o)=>Gi(mE(e),bE(e,t),o.insetWest(),Qi(),"west",nl(e,{left:1}),uE),_E=e=>{switch(e){case"north":return SE;case"northeast":return wE;case"northwest":return xE;case"south":return kE;case"southeast":return yE;case"southwest":return vE;case"east":return CE;case"west":return OE}},TE=(e,t,o,n,s)=>tc(n).map(_E).getOr(SE)(e,t,o,n,s),EE=e=>{switch(e){case"north":return kE;case"northeast":return yE;case"northwest":return vE;case"south":return SE;case"southeast":return wE;case"southwest":return xE;case"east":return OE;case"west":return CE}},AE=(e,t,o,n,s)=>tc(n).map(EE).getOr(SE)(e,t,o,n,s),ME={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},DE=(e,t,o)=>{const n={maxHeightFunction:fc()};return()=>o()?{type:"node",root:vt(bt(e())),node:A.from(e()),bubble:xc(12,12,ME),layouts:{onRtl:()=>[wE],onLtr:()=>[xE]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:xc(-12,12,ME),layouts:{onRtl:()=>[ul,ml,fl],onLtr:()=>[ml,ul,fl]},overrides:n}},BE=(e,t,o,n)=>{const s={maxHeightFunction:fc()};return()=>n()?{type:"node",root:vt(bt(t())),node:A.from(t()),bubble:xc(12,12,ME),layouts:{onRtl:()=>[SE],onLtr:()=>[SE]},overrides:s}:e?{type:"node",root:vt(bt(t())),node:A.from(t()),bubble:xc(0,-$t(t()),ME),layouts:{onRtl:()=>[hl],onLtr:()=>[hl]},overrides:s}:{type:"hotspot",hotspot:o(),bubble:xc(0,0,ME),layouts:{onRtl:()=>[hl],onLtr:()=>[hl]},overrides:s}},IE=(e,t,o)=>()=>o()?{type:"node",root:vt(bt(e())),node:A.from(e()),layouts:{onRtl:()=>[SE],onLtr:()=>[SE]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[fl],onLtr:()=>[fl]}},FE=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng(),o=e.model.table.getSelectedCells();if(o.length>1){const e=o[0],t=o[o.length-1],n={firstCell:ze(e),lastCell:ze(t)};return A.some(n)}return A.some(qc.range(ze(t.startContainer),t.startOffset,ze(t.endContainer),t.endOffset))}}),RE=e=>t=>({type:"node",root:e(),node:t}),NE=(e,t,o,n)=>{const s=dv(e),r=()=>ze(e.getBody()),a=()=>ze(e.getContentAreaContainer()),i=()=>s||!n();return{inlineDialog:DE(a,t,i),inlineBottomDialog:BE(e.inline,a,o,i),banner:IE(a,t,i),cursor:FE(e,r),node:RE(r)}},LE=e=>(t,o)=>{rS(e)(t,o)},zE=e=>()=>Gw(e),VE=e=>t=>Pw(e,t),HE=e=>t=>jw(e,t),PE=e=>()=>jb(e),UE=e=>ye(e,"items"),WE=e=>ye(e,"format"),jE=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],GE=e=>j(e,((e,t)=>{if(ve(t,"items")){const o=GE(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(ve(t,"inline")||(e=>ve(e,"block"))(t)||(e=>ve(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),$E=e=>kb(e).map((t=>{const o=((e,t)=>{const o=GE(t),n=t=>{H(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return Cb(e)?jE.concat(o):o})).getOr(jE),qE=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),YE=(e,t,o,n)=>{const s=t=>V(t,(t=>UE(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:x(t)}})(t):WE(t)?(e=>qE(e,o,n))(t):(e=>{const t=ae(e);return 1===t.length&&R(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:da(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},XE=e=>{let t=0;const o=e=>[{dom:{tag:"div",classes:["tox-tooltip__body"]},components:[ai(e.tooltipText)]}];return{getConfig:n=>({delayForShow:()=>t>0?60:300,delayForHide:x(300),exclusive:!0,lazySink:e,tooltipDom:{tag:"div",classes:["tox-tooltip","tox-tooltip--up"]},tooltipComponents:o(n),onShow:(e,o)=>{t++,n.onShow&&n.onShow(e,o)},onHide:()=>{t--}}),getComponents:o}},KE=e_.trim,JE=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},ZE=JE("true"),QE=JE("false"),eA=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),tA=e=>e.innerText||e.textContent,oA=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&sA(e),nA=e=>e&&/^(H[1-6])$/.test(e.nodeName),sA=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return ZE(t)}return!1})(e)&&!QE(e),rA=e=>nA(e)&&sA(e),aA=e=>{var t;const o=(e=>e.id?e.id:da("h"))(e);return eA("header",null!==(t=tA(e))&&void 0!==t?t:"","#"+o,(e=>nA(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},iA=e=>{const t=e.id||e.name,o=tA(e);return eA("anchor",o||"#"+t,"#"+t,0,b)},lA=e=>KE(e.title).length>0,cA=e=>{const t=(e=>{const t=V(td(ze(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return U((e=>V(U(e,rA),aA))(t).concat((e=>V(U(e,oA),iA))(t)),lA)},dA="tinymce-url-history",uA=e=>r(e)&&/^https?/.test(e),mA=e=>a(e)&&he(e,(e=>{return!(l(t=e)&&t.length<=5&&X(t,uA));var t})).isNone(),gA=()=>{const e=Ew.getItem(dA);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+dA+" was not valid JSON",e),{};throw e}return mA(t)?t:(console.log("Local storage "+dA+" was not valid format",t),{})},pA=e=>{const t=gA();return be(t,e).getOr([])},hA=(e,t)=>{if(!uA(e))return;const o=gA(),n=be(o,t).getOr([]),s=U(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!mA(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));Ew.setItem(dA,JSON.stringify(e))})(o)},fA=e=>!!e,bA=e=>ce(e_.makeMap(e,/[, ]/),fA),vA=e=>A.from(Lb(e)),yA=e=>A.from(e).filter(r).getOrUndefined(),xA=e=>({getHistory:pA,addToHistory:hA,getLinkInformation:()=>(e=>Pb(e)?A.some({targets:cA(e.getBody()),anchorTop:yA(Ub(e)),anchorBottom:yA(Wb(e))}):A.none())(e),getValidationHandler:()=>(e=>A.from(zb(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=A.from(Hb(e)).filter(fA).map(bA);return vA(e).fold(T,(e=>t.fold(E,(e=>ae(e).length>0&&e))))})(e);return d(o)?o?vA(e):A.none():o[t]?vA(e):A.none()})(e,t).map((o=>n=>CS((s=>{const i={filetype:t,fieldname:n.fieldname,...A.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),wA=bm,SA=em,kA=x([ws("shell",!1),ss("makeItem"),ws("setupItem",b),Ou("listBehaviours",[nh])]),CA=Ju({name:"items",overrides:()=>({behaviours:Ml([nh.config({})])})}),OA=x([CA]),_A=Cm({name:x("CustomList")(),configFields:kA(),partFields:OA(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[nh.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Cu(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?A.some(n):cm(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=nh.contents(n),r=o.length,a=r-s.length,i=a>0?L(a,(()=>e.makeItem())):[],l=s.slice(r);H(l,(e=>nh.remove(n,e))),H(i,(e=>nh.append(n,e)));const c=nh.contents(n);H(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),TA=x([ss("dom"),ws("shell",!0),Su("toolbarBehaviours",[nh])]),EA=x([Ju({name:"groups",overrides:()=>({behaviours:Ml([nh.config({})])})})]),AA=Cm({name:"Toolbar",configFields:TA(),partFields:EA(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[nh.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Cu(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?A.some(n):cm(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{nh.set(e,o)}))},refresh:b},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),MA=b,DA=T,BA=x([]);var IA,FA=Object.freeze({__proto__:null,setup:MA,isDocked:DA,getBehaviours:BA});const RA=e=>(xe(zt(e,"position"),"fixed")?A.none():lt(e)).orThunk((()=>{const t=Ne("span");return at(e).bind((e=>{Ho(e,t);const o=lt(t);return Wo(t),o}))})),NA=e=>RA(e).map(Kt).getOrThunk((()=>Yt(0,0))),LA=(e,t)=>{const o=e.element;ja(o,t.transitionClass),$a(o,t.fadeOutClass),ja(o,t.fadeInClass),t.onShow(e)},zA=(e,t)=>{const o=e.element;ja(o,t.transitionClass),$a(o,t.fadeInClass),ja(o,t.fadeOutClass),t.onHide(e)},VA=(e,t)=>e.y>=t.y,HA=(e,t)=>e.bottom<=t.bottom,PA=(e,t,o)=>({location:"top",leftX:t,topY:o.bounds.y-e.y}),UA=(e,t,o)=>({location:"bottom",leftX:t,bottomY:e.bottom-o.bounds.bottom}),WA=e=>e.box.x-e.win.x,jA=(e,t,o)=>o.getInitialPos().map((o=>{const n=((e,t)=>{const o=t.optScrollEnv.fold(x(e.bounds.y),(t=>t.scrollElmTop+(e.bounds.y-t.currentScrollTop)));return Yt(e.bounds.x,o)})(o,t);return{box:Zo(n.left,n.top,Qt(e),Gt(e)),location:o.location}})),GA=(e,t,o,n,s)=>{const r=((e,t)=>{const o=t.optScrollEnv.fold(x(e.y),(t=>e.y+t.currentScrollTop-t.scrollElmTop));return Yt(e.x,o)})(t,o),a=Zo(r.left,r.top,t.width,t.height);n.setInitialPos({style:Vt(e),position:Nt(e,"position")||"static",bounds:a,location:s.location})},$A=(e,t,o)=>o.getInitialPos().bind((n=>{var s;switch(o.clearInitialPos(),n.position){case"static":return A.some({morph:"static"});case"absolute":const o=RA(e).getOr(St()),r=Qo(o),a=null!==(s=o.dom.scrollTop)&&void 0!==s?s:0;return A.some({morph:"absolute",positionCss:jl("absolute",be(n.style,"left").map((e=>t.x-r.x)),be(n.style,"top").map((e=>t.y-r.y+a)),be(n.style,"right").map((e=>r.right-t.right)),be(n.style,"bottom").map((e=>r.bottom-t.bottom)))});default:return A.none()}})),qA=e=>{switch(e.location){case"top":return A.some({morph:"fixed",positionCss:jl("fixed",A.some(e.leftX),A.some(e.topY),A.none(),A.none())});case"bottom":return A.some({morph:"fixed",positionCss:jl("fixed",A.some(e.leftX),A.none(),A.none(),A.some(e.bottomY))});default:return A.none()}},YA=(e,t,o)=>{const n=e.element;return xe(zt(n,"position"),"fixed")?((e,t,o)=>((e,t,o)=>jA(e,t,o).filter((({box:e})=>((e,t,o)=>X(e,(e=>{switch(e){case"bottom":return HA(t,o.bounds);case"top":return VA(t,o.bounds)}})))(o.getModes(),e,t))).bind((({box:t})=>$A(e,t,o))))(e,t,o).orThunk((()=>t.optScrollEnv.bind((n=>jA(e,t,o))).bind((({box:e,location:o})=>{const n=on(),s=WA({win:n,box:e}),r="top"===o?PA(n,s,t):UA(n,s,t);return qA(r)})))))(n,t,o):((e,t,o)=>{const n=Qo(e),s=on(),r=((e,t,o)=>{const n=t.win,s=t.box,r=WA(t);return re(e,(e=>{switch(e){case"bottom":return HA(s,o.bounds)?A.none():A.some(UA(n,r,o));case"top":return VA(s,o.bounds)?A.none():A.some(PA(n,r,o));default:return A.none()}})).getOr({location:"no-dock"})})(o.getModes(),{win:s,box:n},t);return"top"===r.location||"bottom"===r.location?(GA(e,n,t,o,r),qA(r)):A.none()})(n,t,o)},XA=(e,t,o)=>{o.setDocked(!1),H(["left","right","top","bottom","position"],(t=>Pt(e.element,t))),t.onUndocked(e)},KA=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),Gl(e.element,n),(s?t.onDocked:t.onUndocked)(e)},JA=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n.bounds);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Ya(e.element,[t.fadeOutClass]),t.onHide(e)):(a?LA:zA)(e,t))}))}))},ZA=(e,t,o,n,s)=>{JA(e,t,o,n,!0),KA(e,t,o,s.positionCss)},QA=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);JA(e,t,o,n),YA(e,n,o).each((s=>{((e,t,o,n,s)=>{switch(s.morph){case"static":return XA(e,t,o);case"absolute":return KA(e,t,o,s.positionCss);case"fixed":ZA(e,t,o,n,s)}})(e,t,o,n,s)}))})(e,t,o)},eM=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1);const s=t.lazyViewport(e);((e,t,o)=>{const n=e.element;return jA(n,t,o).bind((({box:e})=>$A(n,e,o)))})(e,s,o).each((n=>{switch(n.morph){case"static":XA(e,t,o);break;case"absolute":KA(e,t,o,n.positionCss)}})),o.setVisible(!0),t.contextual.each((t=>{Xa(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),QA(e,t,o)})(e,t,o)},tM=e=>(t,o,n)=>{const s=o.lazyViewport(t);((e,t,o,n)=>{const s=Qo(e),r=on(),a=n(r,WA({win:r,box:s}),t);return"bottom"===a.location||"top"===a.location?(((e,t,o,n,s)=>{n.getInitialPos().fold((()=>GA(e,t,o,n,s)),(()=>b))})(e,s,t,o,a),qA(a)):A.none()})(t.element,s,n,e).each((e=>{ZA(t,o,n,s,e)}))},oM=tM(PA),nM=tM(UA);var sM=Object.freeze({__proto__:null,refresh:QA,reset:eM,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n),forceDockToTop:oM,forceDockToBottom:nM}),rM=Object.freeze({__proto__:null,events:(e,t)=>Pr([Jr(sr(),((o,n)=>{e.contextual.each((e=>{qa(o.element,e.transitionClass)&&(Xa(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),jr(Sr(),((o,n)=>{QA(o,e,t)})),jr(Mr(),((o,n)=>{QA(o,e,t)})),jr(kr(),((o,n)=>{eM(o,e,t)}))])}),aM=[xs("contextual",[is("fadeInClass"),is("fadeOutClass"),is("transitionClass"),cs("lazyContext"),zi("onShow"),zi("onShown"),zi("onHide"),zi("onHidden")]),Ts("lazyViewport",(()=>({bounds:on(),optScrollEnv:A.none()}))),Es("modes",["top","bottom"],Pn),zi("onDocked"),zi("onUndocked")];const iM=Bl({fields:aM,name:"docking",active:rM,apis:sM,state:Object.freeze({__proto__:null,init:e=>{const t=Ms(!1),o=Ms(!0),n=ac(),s=Ms(e.modes);return Ea({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),lM=x(da("toolbar-height-change")),cM={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},dM="tox-tinymce--toolbar-sticky-on",uM="tox-tinymce--toolbar-sticky-off",mM=(e,t)=>R(iM.getModes(e),t),gM=e=>{const t=e.element;it(t).each((o=>{const n="padding-"+iM.getModes(e)[0];if(iM.isDocked(e)){const e=Qt(o);It(t,"width",e+"px"),It(o,n,(e=>$t(e)+(parseInt(Nt(e,"margin-top"),10)||0)+(parseInt(Nt(e,"margin-bottom"),10)||0))(t)+"px")}else Pt(t,"width"),Pt(o,n)}))},pM=(e,t)=>{t?($a(e,cM.fadeOutClass),Ya(e,[cM.transitionClass,cM.fadeInClass])):($a(e,cM.fadeInClass),Ya(e,[cM.fadeOutClass,cM.transitionClass]))},hM=(e,t)=>{const o=ze(e.getContainer());t?(ja(o,dM),$a(o,uM)):(ja(o,uM),$a(o,dM))},fM=(e,t)=>{const o=ac(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||gM(t),hM(e,iM.isDocked(t)),t.getSystem().broadcastOn([nu()],{}),n().each((e=>e.getSystem().broadcastOn([nu()],{})))},a=e.inline?[]:[Nl.config({channels:{[lM()]:{onReceive:gM}}})];return[ch.config({}),iM.config({contextual:{lazyContext:t=>{const o=$t(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer();return A.from(n).map((n=>{const s=Qo(ze(n));return QS(e,t.element).fold((()=>{const e=s.height-o,n=s.y+(mM(t,"top")?0:o);return Zo(s.x,n,s.width,e)}),(e=>{const n=tn(s,ek(e)),r=mM(t,"top")?n.y:n.y+o;return Zo(n.x,r,n.width,n.height-o)}))}))},onShow:()=>{s((e=>pM(e,!0)))},onShown:e=>{s((e=>Xa(e,[cM.transitionClass,cM.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=ot(t);Pl(o).filter((e=>!et(t,e))).filter((t=>et(t,ze(o.dom.body))||tt(e,t))).each((()=>zl(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>Ul(e).orThunk((()=>t().toOptional().bind((e=>Ul(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>pM(e,!1)))},onHidden:()=>{s((e=>Xa(e,[cM.transitionClass])))},...cM},lazyViewport:t=>QS(e,t.element).fold((()=>{const o=on(),n=Fb(e),s=o.y+(mM(t,"top")?n:0),r=o.height-(mM(t,"bottom")?n:0);return{bounds:Zo(o.x,s,o.width,r),optScrollEnv:A.none()}}),(e=>({bounds:ek(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Kt(e.element).top})}))),modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var bM=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(iM.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(gM)})),e.on("SkinLoaded",(()=>{o().each((e=>{iM.isDocked(e)?iM.reset(e):iM.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(iM.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{iM.refresh(t);const o=t.element;Pg(o)&&((e,t)=>{const o=ot(t),n=rt(t).dom.innerHeight,s=jo(o),r=ze(e.elm),a=en(r),i=Gt(r),l=a.y,c=l+i,d=Kt(t),u=Gt(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Go(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Go(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{hM(e,!1)}))},isDocked:e=>e().map(iM.isDocked).getOr(!1),getBehaviours:fM});const vM=In([ry,rs("items",Rn([Ln([ay,ms("items",Pn)]),Pn]))].concat(Ry)),yM=[fs("text"),fs("tooltip"),fs("icon"),Ss("search",!1,Rn([Un,In([fs("placeholder")])],(e=>d(e)?e?A.some({placeholder:A.none()}):A.none():A.some(e)))),cs("fetch"),Ts("onSetup",(()=>b))],xM=In([ry,...yM]),wM=e=>Xn("menubutton",xM,e),SM=In([ry,xy,yy,vy,ky,my,fy,Os("presets","normal",["normal","color","listpreview"]),Ey(1),py,hy]);var kM=km({factory:(e,t)=>{const o={focus:Yp.focusIn,setMenus:(e,o)=>{const n=V(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=wM(o).mapError((e=>Zn(e))).getOrDie();return hT(n,"tox-mbtn",t.backstage,A.some("menuitem"))}));nh.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:Ml([nh.config({}),sh("menubar-events",[Zr((t=>{e.onSetup(t)})),jr(Xs(),((e,t)=>{wi(e.element,".tox-mbtn--active").each((o=>{Si(t.event.target,".tox-mbtn").each((t=>{et(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{VS.expand(e),VS.close(o),ch.focus(e)}))}))}))}))})),jr(Er(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{VS.isOpen(o)&&(VS.expand(e),VS.close(o))}))}))}))]),Yp.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),A.some(!0))}),vk.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[ss("dom"),ss("uid"),ss("onEscape"),ss("backstage"),ws("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const CM="container",OM=[Su("slotBehaviours",[])],_M=e=>"<alloy.field."+e+">",TM=(e,t)=>{const o=t=>gm(e),n=(t,o)=>(n,s)=>cm(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==Tt(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;It(o,"display","none"),Ot(o,"aria-hidden","true"),Nr(e,Ar(),{name:t,visible:!1})}})),i=(e=>(t,o)=>{H(o,(o=>e(t,o)))})(a),l=n(((e,t)=>{if(!s(e)){const o=e.element;Pt(o,"display"),Mt(o,"aria-hidden"),Nr(e,Ar(),{name:t,visible:!0})}})),c={getSlotNames:o,getSlot:(t,o)=>cm(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:l};return{uid:e.uid,dom:e.dom,components:t,behaviours:ku(e.slotBehaviours),apis:c}},EM=ce({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>_a(e))),AM={...EM,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),sm(CM,_M(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=V(n,(e=>Xu({name:e,pname:_M(e)})));return ym(CM,OM,s,TM,o)}},MM=In([yy,xy,Ts("onShow",b),Ts("onHide",b),fy]),DM=e=>({element:()=>e.element.dom}),BM=(e,t)=>{const o=V(ae(t),(e=>{const o=t[e],n=Kn((e=>Xn("sidebar",MM,e))(o));return{name:e,getApi:DM,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return V(o,(t=>{const n=Ms(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:mx([Ix(t,n),Fx(t,n),jr(Ar(),((e,t)=>{const n=t.event,s=G(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},IM=e=>AM.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:BM(t,e),slotBehaviours:mx([Zr((e=>AM.hideAllSlots(e)))])}))),FM=(e,t)=>{Ot(e,"role",t)},RM=e=>Em.getCurrent(e).bind((e=>gT.isGrowing(e)||gT.hasGrown(e)?Em.getCurrent(e).bind((e=>G(AM.getSlotNames(e),(t=>AM.isShowing(e,t))))):A.none())),NM=da("FixSizeEvent"),LM=da("AutoSizeEvent");var zM=Object.freeze({__proto__:null,block:(e,t,o,n)=>{Ot(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=Ml([Yp.config({mode:"special",onTab:()=>A.some(!0),onShiftTab:()=>A.some(!0)}),ch.config({})]),a=n(s,r),i=s.getSystem().build(a);nh.append(s,ui(i)),i.hasConfigured(Yp)&&t.focus&&Yp.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>nh.remove(s,i)))},unblock:(e,t,o)=>{Mt(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()},isBlocked:(e,t,o)=>o.isBlocked()}),VM=[Ts("getRoot",A.none),_s("focus",!0),zi("onBlock"),zi("onUnblock")];const HM=Bl({fields:VM,name:"blocking",apis:zM,state:Object.freeze({__proto__:null,init:()=>{const e=sc((e=>e.destroy()));return Ea({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),PM=e=>Em.getCurrent(e).each((e=>zl(e.element,!0))),UM=(e,t,o)=>{const n=Ms(!1),s=ac(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?oe(s.composedPath()):A.from(s.target)).map(ze).filter($e).exists((e=>qa(e,"mce-pastebin"))))&&(o.preventDefault(),PM(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n=`data-mce-${o}`;A.from(e.iframeElement).map(ze).each((e=>{t?(Et(e,o).each((t=>Ot(e,n,t))),Ot(e,o,-1)):(Mt(e,o),Et(e,n).each((t=>{Ot(e,o,t),Mt(e,n)})))}))})(e,o),o)HM.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:Jh('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),Pt(s,"display"),Mt(s,"aria-hidden"),e.hasFocus()&&PM(t);else{const o=Em.getCurrent(t).exists((e=>Hl(e.element)));HM.unblock(t),It(s,"display","none"),Ot(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=Xh.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},WM=(e,t,o)=>({within:e,extra:t,withinWidth:o}),jM=(e,t,o)=>{const n=j(e,((e,t)=>((e,t)=>{const n=o(e);return A.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(x(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=U(n,(e=>e.finish<=t)),r=W(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},GM=e=>V(e,(e=>e.element)),$M=(e,t)=>{const o=V(t,(e=>ui(e)));AA.setGroups(e,o)},qM=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=dm(e,t,"primary"),r=yS.getCoupled(e,"overflowGroup");It(s.element,"visibility","hidden");const a=n.concat([r]),i=re(a,(e=>Ul(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),$M(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=jM(t,e,o);return 0===n.extra.length?A.some(n):A.none()})(e,t,o).getOrThunk((()=>jM(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=GM(e.concat(t));return WM(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=GM(e).concat([o]);return WM(s,GM(t),n)})(r,a,n,i):((e,t,o)=>WM(GM(e),[],o))(r,0,i)})(Qt(s.element),t.builtGroups.get(),(e=>Math.ceil(e.element.dom.getBoundingClientRect().width)),r);0===l.extra.length?(nh.remove(s,r),o([])):($M(s,l.within),o(l.extra)),Pt(s.element,"visibility"),Ut(s.element),i.each(ch.focus)},YM=x([Su("splitToolbarBehaviours",[yS]),os("builtGroups",(()=>Ms([])))]),XM=x([Ni(["overflowToggledClass"]),vs("getOverflowBounds"),ss("lazySink"),os("overflowGroups",(()=>Ms([]))),zi("onOpened"),zi("onClosed")].concat(YM())),KM=x([Xu({factory:AA,schema:TA(),name:"primary"}),Ku({schema:TA(),name:"overflow"}),Ku({name:"overflow-button"}),Ku({name:"overflow-group"})]),JM=x(((e,t)=>{((e,t)=>{const o=Zt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);It(e,"max-width",o+"px")})(e,Math.floor(t))})),ZM=x([Ni(["toggledClass"]),ss("lazySink"),cs("fetch"),vs("getBounds"),xs("fireDismissalEventInstead",[ws("event",_r())]),Ec(),zi("onToggled")]),QM=x([Ku({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:Ml([bh.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),Ku({factory:AA,schema:TA(),name:"toolbar",overrides:e=>({toolbarBehaviours:Ml([Yp.config({mode:"cyclic",onEscape:t=>(cm(t,e,"button").each(ch.focus),A.none())})])})})]),eD=ac(),tD=(e,t)=>{const o=yS.getCoupled(e,"toolbarSandbox");tu.isOpen(o)?tu.close(o):tu.open(o,t.toolbar())},oD=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();Ad.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:JM()}}},s)},nD=(e,t,o,n,s)=>{AA.setGroups(t,s),oD(e,t,o,n),bh.on(e)},sD=Cm({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Kh.sketch({...n.button(),action:e=>{tD(e,n)},buttonBehaviours:_u({dump:n.button().buttonBehaviours},[yS.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=Ci();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:Ml([Yp.config({mode:"special",onEscape:e=>(tu.close(e),A.some(!0))}),tu.config({onOpen:(s,r)=>{const a=eD.get().getOr(!1);o.fetch().get((s=>{nD(e,r,o,t.layouts,s),n.link(e.element),a||Yp.focusIn(r)}))},onClose:()=>{bh.off(e),eD.get().getOr(!1)||ch.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>Oi(o,n)||Oi(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),Nl.config({channels:{...au({isExtraPart:T,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...lu({doReposition:()=>{tu.getState(yS.getCoupled(e,"toolbarSandbox")).each((n=>{oD(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{tu.getState(yS.getCoupled(t,"toolbarSandbox")).each((s=>{nD(t,s,e,o.layouts,n)}))},reposition:t=>{tu.getState(yS.getCoupled(t,"toolbarSandbox")).each((n=>{oD(t,n,e,o.layouts)}))},toggle:e=>{tD(e,n)},toggleWithoutFocusing:e=>{((e,t)=>{eD.set(!0),tD(e,t),eD.clear()})(e,n)},getToolbar:e=>tu.getState(yS.getCoupled(e,"toolbarSandbox")),isOpen:e=>tu.isOpen(yS.getCoupled(e,"toolbarSandbox"))}}),configFields:ZM(),partFields:QM(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggleWithoutFocusing(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),rD=x([ss("items"),Ni(["itemSelector"]),Su("tgroupBehaviours",[Yp])]),aD=x([Zu({name:"items",unit:"item"})]),iD=Cm({name:"ToolbarGroup",configFields:rD(),partFields:aD(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Cu(e.tgroupBehaviours,[Yp.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),lD=e=>V(e,(e=>ui(e))),cD=(e,t,o)=>{qM(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{sD.setGroups(e,lD(n))}))}))},dD=Cm({name:"SplitFloatingToolbar",configFields:XM(),partFields:KM(),factory:(e,t,o,n)=>{const s=Zh(sD.sketch({fetch:()=>CS((t=>{t(lD(e.overflowGroups.get()))})),layouts:{onLtr:()=>[ml,ul],onRtl:()=>[ul,ml],onBottomLtr:()=>[pl,gl],onBottomRtl:()=>[gl,pl]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:Cu(e.splitToolbarBehaviours,[yS.config({others:{overflowGroup:()=>iD.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(V(o,t.getSystem().build)),cD(t,s,e)},refresh:t=>cD(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{sD.toggle(e)}))},toggleWithoutFocusing:e=>{s.getOpt(e).each(sD.toggleWithoutFocusing)},isOpen:e=>s.getOpt(e).map(sD.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{sD.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(sD.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),uD=x([Ni(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),zi("onOpened"),zi("onClosed")].concat(YM())),mD=x([Xu({factory:AA,schema:TA(),name:"primary"}),Xu({factory:AA,schema:TA(),name:"overflow",overrides:e=>({toolbarBehaviours:Ml([gT.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{cm(t,e,"overflow-button").each((e=>{bh.off(e),ch.focus(e)})),e.onClosed(t)},onGrown:t=>{Yp.focusIn(t),e.onOpened(t)},onStartGrow:t=>{cm(t,e,"overflow-button").each(bh.on)}}),Yp.config({mode:"acyclic",onEscape:t=>(cm(t,e,"overflow-button").each(ch.focus),A.some(!0))})])})}),Ku({name:"overflow-button",overrides:e=>({buttonBehaviours:Ml([bh.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])})}),Ku({name:"overflow-group"})]),gD=(e,t)=>{cm(e,t,"overflow-button").bind((()=>cm(e,t,"overflow"))).each((o=>{pD(e,t),gT.toggleGrow(o)}))},pD=(e,t)=>{cm(e,t,"overflow").each((o=>{qM(e,t,(e=>{const t=V(e,(e=>ui(e)));AA.setGroups(o,t)})),cm(e,t,"overflow-button").each((e=>{gT.hasGrown(o)&&bh.on(e)})),gT.refresh(o)}))},hD=Cm({name:"SplitSlidingToolbar",configFields:uD(),partFields:mD(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:Cu(e.splitToolbarBehaviours,[yS.config({others:{overflowGroup:e=>iD.sketch({...n["overflow-group"](),items:[Kh.sketch({...n["overflow-button"](),action:t=>{Rr(e,s)}})]})}}),sh("toolbar-toggle-events",[jr(s,(t=>{gD(t,e)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=V(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),pD(t,e)},refresh:t=>pD(t,e),toggle:t=>gD(t,e),isOpen:t=>((e,t)=>cm(e,t,"overflow").map(gT.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),fD=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[iD.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled]), .tox-number-input:not([disabled])"},tgroupBehaviours:Ml([vk.config({}),ch.config({})])}},bD=e=>iD.sketch(fD(e)),vD=(e,t)=>{const o=Zr((t=>{const o=V(e.initGroups,bD);AA.setGroups(t,o)}));return Ml([Dx(e.providers.isDisabled),Ex(),Yp.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),sh("toolbar-events",[o])])},yD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":fD({title:A.none(),items:[]}),"overflow-button":PT({name:"more",icon:A.some("more-drawer"),enabled:!0,tooltip:A.some("Reveal or hide additional toolbar items"),primary:!1,buttonType:A.none(),borderless:!1},A.none(),e.providers,[],"overflow-button")},splitToolbarBehaviours:vD(e,t)}},xD=e=>{const t=yD(e),o=dD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return dD.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=en(t),n=st(t),s=en(n),r=Math.max(n.dom.scrollHeight,s.height);return Zo(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},wD=e=>{const t=hD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=hD.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=yD(e);return hD.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([lM()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([lM()],{type:"closed"}),e.onToggled(t,!1)}})},SD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return AA.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===lb.scrolling?["tox-toolbar--scrolling"]:[])},components:[AA.parts.groups({})],toolbarBehaviours:vD(e,t)})},kD=[vy,yy,fs("tooltip"),Os("buttonType","secondary",["primary","secondary"]),_s("borderless",!1),cs("onAction")],CD={button:[...kD,ly,ls("type",["button"])],togglebutton:[...kD,_s("active",!1),ls("type",["togglebutton"])]},OD=[ls("type",["group"]),Es("buttons",[],Qn("type",CD))],_D=Qn("type",{...CD,group:OD}),TD=In([Es("buttons",[],_D),cs("onShow"),cs("onHide")]),ED=(e,t)=>((e,t)=>{var o,n;const s="togglebutton"===e.type,r=e.icon.map((e=>M_(e,t.icons))).map(Zh),a={...e,name:s?e.text.getOr(e.icon.getOr("")):null!==(o=e.text)&&void 0!==o?o:e.icon.getOr(""),primary:"primary"===e.buttonType,buttonType:A.from(e.buttonType),tooltip:e.tooltip,icon:e.icon,enabled:!0,borderless:e.borderless},i=UT(null!==(n=e.buttonType)&&void 0!==n?n:"secondary"),l=s?e.text.map(t.translate):A.some(t.translate(e.text)),c=l.map(ai),d=a.tooltip.or(l).map((e=>({"aria-label":t.translate(e)}))).getOr({}),u=r.map((e=>e.asSpec())),m=Lx([u,c]),g=e.icon.isSome()&&c.isSome(),p={tag:"button",classes:i.concat(...e.icon.isSome()&&!g?["tox-button--icon"]:[]).concat(...g?["tox-button--icon-and-text"]:[]).concat(...e.borderless?["tox-button--naked"]:[]).concat(..."togglebutton"===e.type&&e.active?["tox-button--enabled"]:[]),attributes:d},h=HT(a,A.some((o=>{const n=e=>{r.map((n=>n.getOpt(o).each((o=>{nh.set(o,[M_(e,t.icons)])}))))};return s?e.onAction({setIcon:n,setActive:e=>{const t=o.element;e?(ja(t,"tox-button--enabled"),Ot(t,"aria-pressed",!0)):($a(t,"tox-button--enabled"),Mt(t,"aria-pressed"))},isActive:()=>qa(o.element,"tox-button--enabled")}):"button"===e.type?e.onAction({setIcon:n}):void 0})),[],p,m,e.tooltip,t);return Kh.sketch(h)})(e,t),AD=Io().deviceType,MD=AD.isPhone(),DD=AD.isTablet();var BD=Cm({name:"silver.View",configFields:[ss("viewConfig")],partFields:[Ju({factory:{sketch:e=>{let t=!1;const o=V(e.buttons,(o=>"group"===o.type?(t=!0,((e,t)=>({dom:{tag:"div",classes:["tox-view__toolbar__group"]},components:V(e.buttons,(e=>ED(e,t)))}))(o,e.providers)):ED(o,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:[t?"tox-view__toolbar":"tox-view__header",...MD||DD?["tox-view--mobile","tox-view--scrolling"]:[]]},behaviours:Ml([ch.config({}),Yp.config({mode:"flow",selector:"button, .tox-button",focusInside:wg.OnEnterOrSpaceMode})]),components:t?o:[uk.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),uk.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:o})]}}},schema:[ss("buttons"),ss("providers")],name:"header"}),Ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const s={getPane:t=>wA.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,apis:s}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const ID=(e,t,o)=>pe(t,((t,n)=>{const s=Kn(Xn("view",TD,t));return e.slot(n,BD.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:s,components:[...s.buttons.length>0?[BD.parts.header({buttons:s.buttons,providers:o})]:[],BD.parts.pane({})]}))})),FD=(e,t)=>AM.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:ID(o,e,t),slotBehaviours:mx([Zr((e=>AM.hideAllSlots(e)))])}))),RD=e=>G(AM.getSlotNames(e),(t=>AM.isShowing(e,t))),ND=(e,t,o)=>{AM.getSlot(e,t).each((e=>{BD.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:x(n)}))}))}))};var LD=km({factory:(e,t)=>{const o={setViews:(e,o)=>{nh.set(e,[FD(o,t.backstage.shared.providers)])},whichView:e=>Em.getCurrent(e).bind(RD),toggleView:(e,t,o,n)=>Em.getCurrent(e).exists((s=>{const r=RD(s),a=r.exists((e=>n===e)),i=AM.getSlot(s,n).isSome();return i&&(AM.hideAllSlots(s),a?((e=>{const t=e.element;It(t,"display","none"),Ot(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;Pt(t,"display"),Mt(t,"aria-hidden")})(e),AM.showSlot(s,n),((e,t)=>{ND(e,t,BD.getOnShow)})(s,n)),r.each((e=>((e,t)=>ND(e,t,BD.getOnHide))(s,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:Ml([nh.config({}),Em.config({find:e=>{const t=nh.contents(e);return oe(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[ss("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,s)=>e.toggleView(t,o,n,s),whichView:(e,t)=>e.whichView(t)}});const zD=SA.optional({factory:kM,name:"menubar",schema:[ss("backstage")]}),VD=SA.optional({factory:{sketch:e=>_A.sketch({uid:e.uid,dom:e.dom,listBehaviours:Ml([Yp.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>SD({type:e.type,uid:da("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),A.some(!0))}),setupItem:(e,t,o,n)=>{AA.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[ss("dom"),ss("onEscape")]}),HD=SA.optional({factory:{sketch:e=>{const t=(e=>e.type===lb.sliding?wD:e.type===lb.floating?xD:SD)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),A.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[ss("dom"),ss("onEscape"),ss("getSink")]}),PD=SA.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?fM:BA;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:Ml(o(t,e.sharedBackstage))}}},name:"header",schema:[ss("dom")]}),UD=SA.optional({factory:{sketch:e=>({uid:e.uid,dom:e.dom,components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-self-hosted-premium-features/?utm_campaign=self_hosted_upgrade_promo&utm_source=tiny&utm_medium=referral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u26a1\ufe0fUpgrade"}}]})},name:"promotion",schema:[ss("dom")]}),WD=SA.optional({name:"socket",schema:[ss("dom")]}),jD=SA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"presentation"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:Ml([vk.config({}),ch.config({}),gT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{Em.getCurrent(e).each(AM.hideAllSlots),Rr(e,LM)},onGrown:e=>{Rr(e,LM)},onStartGrow:e=>{Nr(e,NM,{width:zt(e.element,"width").getOr("")})},onStartShrink:e=>{Nr(e,NM,{width:Qt(e.element)+"px"})}}),nh.config({}),Em.config({find:e=>{const t=nh.contents(e);return oe(t)}})])}],behaviours:Ml([GO(0),sh("sidebar-sliding-events",[jr(NM,((e,t)=>{It(e.element,"width",t.event.width)})),jr(LM,((e,t)=>{Pt(e.element,"width")}))])])})},name:"sidebar",schema:[ss("dom")]}),GD=SA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:Ml([nh.config({}),HM.config({focus:!1}),Em.config({find:e=>oe(e.components())})]),components:[]})},name:"throbber",schema:[ss("dom")]}),$D=SA.optional({factory:LD,name:"viewWrapper",schema:[ss("backstage")]}),qD=SA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var YD=Cm({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const s=e=>{yi(e,".tox-statusbar").each((e=>{"none"===Nt(e,"display")&&"true"===Tt(e,"aria-hidden")?(Pt(e,"display"),Mt(e,"aria-hidden")):(It(e,"display","none"),Ot(e,"aria-hidden","true"))}))},a={getSocket:t=>wA.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{wA.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{Em.getCurrent(e).each((n=>{nh.set(n,[IM(t)]);const s=null==o?void 0:o.toLowerCase();r(s)&&ve(t,s)&&Em.getCurrent(n).each((t=>{AM.showSlot(t,s),gT.immediateGrow(n),Pt(n.element,"width"),FM(e.element,"region")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{wA.getPart(t,e,"sidebar").each((e=>((e,t)=>{Em.getCurrent(e).each((o=>{Em.getCurrent(o).each((n=>{gT.hasGrown(o)?AM.isShowing(n,t)?(gT.shrink(o),FM(e.element,"presentation")):(AM.hideAllSlots(n),AM.showSlot(n,t),FM(e.element,"region")):(AM.hideAllSlots(n),AM.showSlot(n,t),gT.grow(o),FM(e.element,"region"))}))}))})(e,o)))},whichSidebar:t=>wA.getPart(t,e,"sidebar").bind(RM).getOrNull(),getHeader:t=>wA.getPart(t,e,"header"),getToolbar:t=>wA.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{wA.getPart(t,e,"toolbar").each((e=>{const t=V(o,bD);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{wA.getPart(t,e,"multiple-toolbar").each((e=>{const t=V(o,(e=>V(e,bD)));_A.setItems(e,t)}))},refreshToolbar:t=>{wA.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{wA.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggle,(t=>t(e)))}))},toggleToolbarDrawerWithoutFocusing:t=>{wA.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggleWithoutFocusing,(t=>t(e)))}))},isToolbarDrawerToggled:t=>wA.getPart(t,e,"toolbar").bind((e=>A.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>wA.getPart(t,e,"throbber"),focusToolbar:t=>{wA.getPart(t,e,"toolbar").orThunk((()=>wA.getPart(t,e,"multiple-toolbar"))).each((e=>{Yp.focusIn(e)}))},setMenubar:(t,o)=>{wA.getPart(t,e,"menubar").each((e=>{kM.setMenus(e,o)}))},focusMenubar:t=>{wA.getPart(t,e,"menubar").each((e=>{kM.focus(e)}))},setViews:(t,o)=>{wA.getPart(t,e,"viewWrapper").each((e=>{LD.setViews(e,o)}))},toggleView:(t,o)=>wA.getPart(t,e,"viewWrapper").exists((e=>LD.toggleView(e,(()=>a.showMainView(t)),(()=>a.hideMainView(t)),o))),whichView:t=>wA.getPart(t,e,"viewWrapper").bind(LD.whichView).getOrNull(),hideMainView:t=>{n=a.isToolbarDrawerToggled(t),n&&a.toggleToolbarDrawer(t),wA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),It(t,"display","none"),Ot(t,"aria-hidden","true")}))},showMainView:t=>{n&&a.toggleToolbarDrawer(t),wA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),Pt(t,"display"),Mt(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:a,behaviours:e.behaviours}},configFields:[ss("dom"),ss("behaviours")],partFields:[PD,zD,HD,VD,WD,jD,UD,GD,$D,qD],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},toggleToolbarDrawerWithoutFocusing:(e,t)=>{e.toggleToolbarDrawerWithoutFocusing(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const XD={file:{title:"File",items:"newdocument restoredraft | preview | importword exportpdf exportword | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code revisionhistory | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed inserttemplate codesample inserttable accordion | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"aidialog aishortcuts | spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code typography wordcount addtemplate"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},KD=e=>e.split(" "),JD=(e,t)=>{const o={...XD,...t.menus},n=ae(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?KD("file edit view insert format tools table help"):KD(!1===t.menubar?"":t.menubar),a=U(s,(e=>{const o=ve(XD,e);return n?o||be(t.menus,e).exists((e=>ve(e,"items"))):o})),i=V(a,(n=>{const s=o[n];return((e,t,o)=>{const n=Tb(o).split(/[ ,]/);return{text:e.title,getItems:()=>Y(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||N(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:KD(s.items)},t,e)}));return U(i,(e=>e.getItems().length>0&&N(e.getItems(),(e=>r(e)||"separator"!==e.type))))},ZD=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),QD=(e,t,o,n)=>(e.on("remove",(()=>n.unloadRawCss(t))),n.loadRawCss(t,o)),eB=async(e,t)=>{const o="ui/"+nv(e).getOr("default")+"/skin.css",n=tinymce.Resource.get(o);return r(n)?Promise.resolve(QD(e,o,n,e.ui.styleSheetLoader)):ZD(e,t+"/skin.min.css",e.ui.styleSheetLoader)},tB=async(e,t)=>{var o;if(o=ze(e.getElement()),yt(o).isSome()){const o="ui/"+nv(e).getOr("default")+"/skin.shadowdom.css",n=tinymce.Resource.get(o);return r(n)?(QD(e,o,n,db.DOM.styleSheetLoader),Promise.resolve()):ZD(e,t+"/skin.shadowdom.min.css",db.DOM.styleSheetLoader)}},oB=(e,t)=>(async(e,t)=>{nv(t).fold((()=>{const o=ov(t);o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css")}),(o=>{const n="ui/"+o+(e?"/content.inline":"/content")+".css",s=tinymce.Resource.get(n);if(r(s))QD(t,n,s,t.ui.styleSheetLoader);else{const o=ov(t);o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css")}}));const o=ov(t);if(!ev(t)&&r(o))return Promise.all([eB(t,o),tB(t,o)]).then()})(e,t).then((e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}})(t),((e,t)=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)),nB=k(oB,!1),sB=k(oB,!0),rB=(e,t,o)=>Be(o)?e.translate(t):e.translate([t,e.translate(o)]),aB=(e,t)=>{const o=(o,s,r,a)=>{const i=e.shared.providers.translate(o.title);if("separator"===o.type)return A.some({type:"separator",text:i});if("submenu"===o.type){const e=Y(o.getStyleItems(),(e=>n(e,s,a)));return 0===s&&e.length<=0?A.none():A.some({type:"nestedmenuitem",text:i,enabled:e.length>0,getSubmenuItems:()=>Y(o.getStyleItems(),(e=>n(e,s,a)))})}return A.some({type:"togglemenuitem",text:i,icon:o.icon,active:o.isSelected(a),enabled:!r,onAction:t.onAction(o),...o.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},n=(e,n,s)=>{const r="formatter"===e.type&&t.isInvalid(e);return 0===n?r?[]:o(e,n,!1,s).toArray():o(e,n,r,s).toArray()},s=e=>{const o=t.getCurrentValue(),s=t.shouldHide?0:1;return Y(e,(e=>n(e,s,o)))};return{validateItems:s,getFetch:(e,t)=>(o,n)=>{const r=t(),a=s(r);n(z_(a,vv.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:A.none()}))}}},iB=(e,t)=>{const o=t.dataset,n="basic"===o.type?()=>V(o.data,(e=>qE(e,t.isSelectedFor,t.getPreviewFor))):o.getData;return{items:aB(e,t),getStyleItems:n}},lB=(e,t,o,n,s,r)=>{const{items:a,getStyleItems:i}=iB(t,o),l=Ms(o.tooltip);return F_({text:o.icon.isSome()?A.none():o.text,icon:o.icon,ariaLabel:A.some(o.tooltip),tooltip:A.none(),role:A.none(),fetch:a.getFetch(t,i),onSetup:t=>{const r=o=>t.setTooltip(rB(e,n(o.value),o.value));return e.on(s,r),Sw(Ow(e,"NodeChange",(t=>{const n=t.getComponent();o.updateText(n),Um.set(t.getComponent(),!e.selection.isEditable())}))(t),(()=>e.off(s,r)))},getApi:e=>({getComponent:x(e),setTooltip:o=>{const n=t.shared.providers.translate(o);Ot(e.element,"aria-label",n),l.set(o)}}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[kx.config({...t.shared.providers.tooltips.getConfig({tooltipText:t.shared.providers.translate(o.tooltip),onShow:e=>{if(o.tooltip!==l.get()){const o=t.shared.providers.translate(l.get());kx.setComponents(e,t.shared.providers.tooltips.getComponents({tooltipText:o}))}}})})]},"tox-tbtn",t.shared,r)};var cB;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(cB||(cB={}));const dB=(e,t,o)=>{const n=(s=((e,t)=>t===cB.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),V(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},uB=x("Alignment {0}"),mB="left",gB=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],pB=e=>{const t={type:"basic",data:gB};return{tooltip:rB(e,uB(),mB),text:A.none(),icon:A.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:e=>A.none,onAction:t=>()=>G(gB,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=G(gB,(t=>e.formatter.match(t.format))).fold(x(mB),(e=>e.title.toLowerCase()));Nr(t,I_,{icon:`align-${o}`}),((e,t)=>{e.dispatch("AlignTextUpdate",t)})(e,{value:o})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},hB=(e,t)=>{const o=t(),n=V(o,(e=>e.format));return A.from(e.formatter.closest(n)).bind((e=>G(o,(t=>t.format===e))))},fB=x("Block {0}"),bB="Paragraph",vB=e=>{const t=dB(e,"block_formats",cB.SemiColon);return{tooltip:rB(e,fB(),bB),text:A.some(bB),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:_w(e),updateText:o=>{const n=hB(e,(()=>t.data)).fold(x(bB),(e=>e.title));Nr(o,B_,{text:n}),((e,t)=>{e.dispatch("BlocksTextUpdate",t)})(e,{value:n})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},yB=x("Font {0}"),xB="System Font",wB=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],SB=e=>{const t=e.split(/\s*,\s*/);return V(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},kB=(e,t)=>t.length>0&&X(t,(t=>e.indexOf(t.toLowerCase())>-1)),CB=e=>{const t=()=>{const t=e=>e?SB(e)[0]:"",n=e.queryCommandValue("FontName"),s=o.data,r=n?n.toLowerCase():"",a=Qb(e),i=G(s,(e=>{const o=e.format;return o.toLowerCase()===r||t(o).toLowerCase()===t(r).toLowerCase()})).orThunk((()=>Ce(((e,t)=>{if(0===e.indexOf("-apple-system")||t.length>0){const o=SB(e.toLowerCase());return kB(o,wB)||kB(o,t)}return!1})(r,a),{title:xB,format:r})));return{matchOpt:i,font:n}},o=dB(e,"font_family_formats",cB.SemiColon);return{tooltip:rB(e,yB(),xB),text:A.some(xB),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=t();return e},getPreviewFor:e=>()=>A.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:o=>{const{matchOpt:n,font:s}=t(),r=n.fold(x(s),(e=>e.title));Nr(o,B_,{text:r}),((e,t)=>{e.dispatch("FontFamilyTextUpdate",t)})(e,{value:r})},dataset:o,shouldHide:!1,isInvalid:T}},OB={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},_B=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),TB=(e,t)=>A.from(_B.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>N(t,(t=>N(OB[t],(t=>e===t)))))(n,t)?A.some({value:o,unit:n}):A.none()})),EB={tab:x(9),escape:x(27),enter:x(13),backspace:x(8),delete:x(46),left:x(37),up:x(38),right:x(39),down:x(40),space:x(32),home:x(36),end:x(35),pageUp:x(33),pageDown:x(34)},AB=x("Font size {0}"),MB="12pt",DB={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},BB={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},IB=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":be(BB,e).getOr(e),FB=e=>be(DB,e).getOr(""),RB=e=>{const t=()=>{let t=A.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=IB(s,e),r=FB(n);t=G(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=x(A.none),n=dB(e,"font_size_formats",cB.Space);return{tooltip:rB(e,AB(),MB),text:A.some(MB),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:o=>{const{matchOpt:n,size:s}=t(),r=n.fold(x(s),(e=>e.title));Nr(o,B_,{text:r}),((e,t)=>{e.dispatch("FontSizeTextUpdate",t)})(e,{value:r})},dataset:n,shouldHide:!1,isInvalid:T}},NB=e=>Be(e)?"Formats":"Format {0}",LB=(e,t)=>{const o="Formats";return{tooltip:rB(e,NB(""),""),text:A.some(o),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:_w(e),updateText:t=>{const n=e=>UE(e)?Y(e.items,n):WE(e)?[{title:e.title,format:e.format}]:[],s=Y($E(e),n),r=hB(e,x(s)).fold(x({title:o,tooltipLabel:""}),(e=>({title:e.title,tooltipLabel:e.title})));Nr(t,B_,{text:r.title}),((e,t)=>{e.dispatch("StylesTextUpdate",t)})(e,{value:r.tooltipLabel})},shouldHide:Ob(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}},zB=x([ss("toggleClass"),ss("fetch"),Hi("onExecute"),ws("getHotspot",A.some),ws("getAnchorOverrides",x({})),Ec(),Hi("onItemExecute"),gs("lazySink"),ss("dom"),zi("onOpen"),Su("splitDropdownBehaviours",[yS,Yp,ch]),ws("matchWidth",!1),ws("useMinWidth",!1),ws("eventOrder",{}),gs("role")].concat(NS())),VB=Xu({factory:Kh,schema:[ss("dom")],name:"arrow",defaults:()=>({buttonBehaviours:Ml([ch.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(Lr)},buttonBehaviours:Ml([bh.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),HB=Xu({factory:Kh,schema:[ss("dom")],name:"button",defaults:()=>({buttonBehaviours:Ml([ch.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),PB=x([VB,HB,Ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[ss("text")],name:"aria-descriptor"}),Ku({schema:[Ri()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),TS()]),UB=Cm({name:"SplitDropdown",configFields:zB(),partFields:PB(),factory:(e,t,o,n)=>{const s=e=>{Em.getCurrent(e).each((e=>{Zm.highlightFirst(e),Yp.focusIn(e)}))},r=t=>{DS(e,w,t,n,s,Gh.HighlightMenuAndItem).get(b)},a=t=>{const o=dm(t,e,"button");return Lr(o),A.some(!0)},i={...Pr([Zr(((t,o)=>{cm(t,e,"aria-descriptor").each((e=>{const o=da("aria");Ot(e.element,"id",o),Ot(t.element,"aria-describedby",o)}))}))]),...yh(A.some(r))},l={repositionMenus:e=>{bh.isOn(e)&&RS(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[gr()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:Cu(e.splitDropdownBehaviours,[yS.config({others:{sandbox:t=>{const o=dm(t,e,"arrow");return FS(e,t,{onOpen:()=>{bh.on(o),bh.on(t)},onClose:()=>{bh.off(o),bh.off(t)}})}}}),Yp.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),A.some(!0))}),ch.config({}),bh.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),WB=e=>({isEnabled:()=>!Um.isDisabled(e),setEnabled:t=>Um.set(e,!t),setText:t=>Nr(e,B_,{text:t}),setIcon:t=>Nr(e,I_,{icon:t})}),jB=e=>({setActive:t=>{bh.set(e,t)},isActive:()=>bh.isOn(e),isEnabled:()=>!Um.isDisabled(e),setEnabled:t=>Um.set(e,!t),setText:t=>Nr(e,B_,{text:t}),setIcon:t=>Nr(e,I_,{icon:t})}),GB=(e,t)=>e.map((e=>({"aria-label":t.translate(e)}))).getOr({}),$B=da("focus-button"),qB=(e,t,o,n,s,r)=>{const a=t.map((e=>Zh(D_(e,"tox-tbtn",s)))),i=e.map((e=>Zh(M_(e,s.icons))));return{dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:{...GB(o,s),...g(r)?{"data-mce-name":r}:{}}},components:Lx([i.map((e=>e.asSpec())),a.map((e=>e.asSpec()))]),eventOrder:{[Gs()]:["focusing","alloy.base.behaviour",O_],[Cr()]:[O_,"toolbar-group-button-events"]},buttonBehaviours:Ml([Dx(s.isDisabled),Ex(),sh(O_,[Zr(((e,t)=>T_(e))),jr(B_,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{nh.set(e,[ai(s.translate(t.event.text))])}))})),jr(I_,((e,t)=>{i.bind((t=>t.getOpt(e))).each((e=>{nh.set(e,[M_(t.event.icon,s.icons)])}))})),jr(Gs(),((e,t)=>{t.event.prevent(),Rr(e,$B)}))])].concat(n.getOr([])))}},YB=(e,t,o,n)=>{var s;const r=Ms(b),a=qB(e.icon,e.text,e.tooltip,A.none(),o,n);return Kh.sketch({dom:a.dom,components:a.components,eventOrder:__,buttonBehaviours:{...Ml([sh("toolbar-button-events",[(i={onAction:e.onAction,getApi:t.getApi},ta(((e,t)=>{Bx(i,e)((t=>{Nr(e,C_,{buttonApi:t}),i.onAction(t)}))}))),Ix(t,r),Fx(t,r)]),...e.tooltip.map((t=>kx.config(o.tooltips.getConfig({tooltipText:o.translate(t)+e.shortcut.map((e=>` (${Hx(e)})`)).getOr("")})))).toArray(),Dx((()=>!e.enabled||o.isDisabled())),Ex()].concat(t.toolbarButtonBehaviours)),[O_]:null===(s=a.buttonBehaviours)||void 0===s?void 0:s[O_]}});var i},XB=(e,t,o,n)=>YB(e,{toolbarButtonBehaviours:o.length>0?[sh("toolbarButtonWith",o)]:[],getApi:WB,onSetup:e.onSetup},t,n),KB=(e,t,o,n)=>YB(e,{toolbarButtonBehaviours:[nh.config({}),bh.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[sh("toolbarToggleButtonWith",o)]:[]),getApi:jB,onSetup:e.onSetup},t,n),JB=(e,t,o)=>n=>CS((e=>t.fetch(e))).map((s=>A.from(qS(vn(aS(da("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,vv.CLOSE_ON_EXECUTE,t.select.getOr(T),o),{movement:lS(t.columns,t.presets),menuBehaviours:mx("auto"!==t.columns?[]:[Zr(((e,o)=>{ux(e,4,Mv(t.presets)).each((({numRows:t,numColumns:o})=>{Yp.setGridSize(e,t,o)}))}))])}))))),ZB=[{name:"history",items:["undo","redo"]},{name:"ai",items:["aidialog","aishortcuts"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],QB=(e,t)=>(o,n,s,r)=>{const a=e(o).mapError((e=>Zn(e))).getOrDie();return t(a,n,s,r)},eI={button:QB(Ly,((e,t,o,n)=>((e,t,o)=>XB(e,t,[],o))(e,t.shared.providers,n))),togglebutton:QB(Hy,((e,t,o,n)=>((e,t,o)=>KB(e,t,[],o))(e,t.shared.providers,n))),menubutton:QB(wM,((e,t,o,n)=>hT(e,"tox-tbtn",t,A.none(),!1,n))),splitbutton:QB((e=>Xn("SplitButton",SM,e)),((e,t,o,n)=>((e,t,o)=>{const n=Ms(e.tooltip.getOr("")),s=e=>({isEnabled:()=>!Um.isDisabled(e),setEnabled:t=>Um.set(e,!t),setIconFill:(t,o)=>{wi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{Ot(e,"fill",o)}))},setActive:t=>{Ot(e.element,"aria-pressed",t),wi(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>bh.set(e,t)))}))},isActive:()=>wi(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(bh.isOn))),setText:t=>wi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Nr(e,B_,{text:t}))))),setIcon:t=>wi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Nr(e,I_,{icon:t}))))),setTooltip:o=>{const s=t.providers.translate(o);Ot(e.element,"aria-label",s),n.set(o)}}),r=Ms(b),a={getApi:s,onSetup:e.onSetup};return UB.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...GB(e.tooltip,t.providers),...g(o)?{"data-mce-name":o}:{}}},onExecute:t=>{const o=s(t);o.isEnabled()&&e.onAction(o)},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:Ml([Mx(t.providers.isDisabled),Ex(),sh("split-dropdown-events",[Zr(((e,t)=>T_(e))),jr($B,ch.focus),Ix(a,r),Fx(a,r)]),jk.config({}),...e.tooltip.map((e=>kx.config({...t.providers.tooltips.getConfig({tooltipText:t.providers.translate(e),onShow:o=>{if(n.get()!==e){const e=t.providers.translate(n.get());kx.setComponents(o,t.providers.tooltips.getComponents({tooltipText:e}))}}})}))).toArray()]),eventOrder:{[Cr()]:["alloy.base.behaviour","split-dropdown-events","tooltipping"],[Or()]:["split-dropdown-events","tooltipping"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:JB(s,e,t.providers),parts:{menu:Nv(0,e.columns,e.presets)},components:[UB.parts.button(qB(e.icon,e.text,A.none(),A.some([bh.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),UB.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:ob("chevron-down",t.providers.icons)},buttonBehaviours:Ml([Mx(t.providers.isDisabled),Ex(),nb()])}),UB.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared,n))),grouptoolbarbutton:QB((e=>Xn("GroupToolbarButton",vM,e)),((e,t,o,n)=>{const s=o.ui.registry.getAll().buttons,r={[_c]:t.shared.header.isPositionedAtTop()?Oc.TopToBottom:Oc.BottomToTop};if(Eb(o)===lb.floating)return((e,t,o,n,s)=>{const r=t.shared,a=Ms(b),i={toolbarButtonBehaviours:[],getApi:WB,onSetup:e.onSetup},l=[sh("toolbar-group-button-events",[Ix(i,a),Fx(i,a)])];return sD.sketch({lazySink:r.getSink,fetch:()=>CS((t=>{t(V(o(e.items),bD))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:qB(e.icon,e.text,e.tooltip,A.some(l),r.providers,s),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>oI(o,{buttons:s,toolbar:e,allowToolbarGroups:!1},t,A.none())),r,n);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},tI={styles:(e,t)=>{const o={type:"advanced",...t.styles};return lB(e,t,LB(e,o),NB,"StylesTextUpdate","styles")},fontsize:(e,t)=>lB(e,t,RB(e),AB,"FontSizeTextUpdate","fontsize"),fontsizeinput:(e,t)=>((e,t,o,n)=>{let s=A.none();const r=Ow(e,"NodeChange SwitchMode",(t=>{const n=t.getComponent();s=A.some(n),o.updateInputValue(n),Um.set(n,!e.selection.isEditable())})),a=e=>({getComponent:x(e)}),i=Ms(b),l=da("custom-number-input-events"),c=(e,t,n)=>{const r=s.map((e=>wu.getValue(e))).getOr(""),a=o.getNewValue(r,e),i=r.length-`${a}`.length,l=s.map((e=>e.element.dom.selectionStart-i)),c=s.map((e=>e.element.dom.selectionEnd-i));o.onAction(a,n),s.each((e=>{wu.setValue(e,a),t&&(l.each((t=>e.element.dom.selectionStart=t)),c.each((t=>e.element.dom.selectionEnd=t)))}))},d=(e,t)=>c(((e,t)=>e-t),e,t),u=(e,t)=>c(((e,t)=>e+t),e,t),m=e=>it(e.element).fold(A.none,(e=>(zl(e),A.some(!0)))),p=e=>Hl(e.element)?(ut(e.element).each((e=>zl(e))),A.some(!0)):A.none(),h=(o,n,s,r)=>{const i=Ms(b),l=t.shared.providers.translate(s),c=da("altExecuting"),d=Ow(e,"NodeChange SwitchMode",(t=>{Um.set(t.getComponent(),!e.selection.isEditable())})),u=e=>{Um.isDisabled(e)||o(!0)};return Kh.sketch({dom:{tag:"button",attributes:{"aria-label":l,"data-mce-name":n},classes:r.concat(n)},components:[A_(n,t.shared.providers.icons)],buttonBehaviours:Ml([Um.config({}),kx.config(t.shared.providers.tooltips.getConfig({tooltipText:l})),sh(c,[Ix({onSetup:d,getApi:a},i),Fx({getApi:a},i),jr(Zs(),((e,t)=>{t.event.raw.keyCode!==EB.space()&&t.event.raw.keyCode!==EB.enter()||Um.isDisabled(e)||o(!1)})),jr(or(),u),jr(Ws(),u)])]),eventOrder:{[Zs()]:[c,"keying"],[or()]:[c,"alloy.base.behaviour"],[Ws()]:[c,"alloy.base.behaviour"],[Cr()]:["alloy.base.behaviour",c,"tooltipping"],[Or()]:[c,"tooltipping"]}})},f=Zh(h((e=>d(!1,e)),"minus","Decrease font size",[])),v=Zh(h((e=>u(!1,e)),"plus","Increase font size",[])),y=Zh({dom:{tag:"div",classes:["tox-input-wrapper"]},components:[Pv.sketch({inputBehaviours:Ml([Um.config({}),sh(l,[Ix({onSetup:r,getApi:a},i),Fx({getApi:a},i)]),sh("input-update-display-text",[jr(B_,((e,t)=>{wu.setValue(e,t.event.text)})),jr(Js(),(e=>{o.onAction(wu.getValue(e))})),jr(tr(),(e=>{o.onAction(wu.getValue(e))}))]),Yp.config({mode:"special",onEnter:e=>(c(w,!0,!0),A.some(!0)),onEscape:m,onUp:e=>(u(!0,!1),A.some(!0)),onDown:e=>(d(!0,!1),A.some(!0)),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})],behaviours:Ml([ch.config({}),Yp.config({mode:"special",onEnter:p,onSpace:p,onEscape:m}),sh("input-wrapper-events",[jr(Xs(),(e=>{H([f,v],(t=>{const o=ze(t.get(e).element.dom);Hl(o)&&Vl(o)}))}))])])});return{dom:{tag:"div",classes:["tox-number-input"],attributes:{...g(n)?{"data-mce-name":n}:{}}},components:[f.asSpec(),y.asSpec(),v.asSpec()],behaviours:Ml([ch.config({}),Yp.config({mode:"flow",focusInside:wg.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-input-wrapper",onEscape:e=>Hl(e.element)?A.none():(zl(e.element),A.some(!0))})])}})(e,t,(e=>{const t=()=>e.queryCommandValue("FontSize");return{updateInputValue:e=>Nr(e,B_,{text:t()}),onAction:(t,o)=>e.execCommand("FontSize",!1,t,{skip_focus:!o}),getNewValue:(o,n)=>{TB(o,["unsupportedLength","empty"]);const s=t(),r=TB(o,["unsupportedLength","empty"]).or(TB(s,["unsupportedLength","empty"])),a=r.map((e=>e.value)).getOr(16),i=Vb(e),l=r.map((e=>e.unit)).filter((e=>""!==e)).getOr(i),c=n(a,(e=>{var t;return null!==(t={em:{step:.1},cm:{step:.1},in:{step:.1},pc:{step:.1},ch:{step:.1},rem:{step:.1}}[e])&&void 0!==t?t:{step:1}})(l).step),d=`${(e=>e>=0)(c)?c:a}${l}`;return d!==s&&((e,t)=>{e.dispatch("FontSizeInputTextUpdate",t)})(e,{value:d}),d}}})(e),"fontsizeinput"),fontfamily:(e,t)=>lB(e,t,CB(e),yB,"FontFamilyTextUpdate","fontfamily"),blocks:(e,t)=>lB(e,t,vB(e),fB,"BlocksTextUpdate","blocks"),align:(e,t)=>lB(e,t,pB(e),uB,"AlignTextUpdate","align")},oI=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=V(ZB,(t=>{const o=U(t.items,(t=>ve(e,t)||ve(tI,t)));return{name:t.name,items:o}}));return U(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return V(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>ve(e,"name")&&ve(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=V(s,(s=>{const r=Y(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>be(t,o.toLowerCase()).orThunk((()=>r.bind((e=>re(e,(e=>be(t,e+o.toLowerCase()))))))).fold((()=>be(tI,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o,n)=>be(eI,e.type).fold((()=>(console.error("skipping button defined by",e),A.none())),(s=>A.some(s(e,t,o,n)))))(t,s,e,o.toLowerCase()):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),A.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:A.from(e.translate(s.name)),items:r}}));return U(a,(e=>e.items.length>0))},nI=(e,t,o,n)=>{const s=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return oI(e,s,n,A.none())}));YD.setToolbars(s,t)}else YD.setToolbar(s,oI(e,o,n,A.none()))},sI=Io(),rI=sI.os.isiOS()&&sI.os.version.major<=12;var aI=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=t,i=Ms(0),l=r.outerContainer;nB(e);const d=ze(s.targetNode),u=vt(bt(d));Ud(d,r.mothership),((e,t,o)=>{pv(e)&&Ud(o.mainUi.mothership.element,o.popupUi.mothership),Pd(t,o.dialogUi.mothership)})(e,u,t),e.on("SkinLoaded",(()=>{YD.setSidebar(l,o.sidebar,Kb(e)),nI(e,t,o,n),i.set(e.getWin().innerWidth),YD.setMenubar(l,JD(e,o)),YD.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let s=e.getWin();const r=e.getDoc().documentElement,a=Ms(Yt(s.innerWidth,s.innerHeight)),i=Ms(Yt(r.offsetWidth,r.offsetHeight)),l=()=>{const t=a.get();t.left===s.innerWidth&&t.top===s.innerHeight||(a.set(Yt(s.innerWidth,s.innerHeight)),yw(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set(Yt(t.offsetWidth,t.offsetHeight)),yw(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(s,"resize",l),n.bind(s,"scroll",d);const u=cc(ze(e.getBody()),"load",c);e.on("hide",(()=>{H(o,(e=>{It(e.element,"display","none")}))})),e.on("show",(()=>{H(o,(e=>{Pt(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(s,"resize",l),n.unbind(s,"scroll",d),s=null}))})(e,t)}));const m=YD.getSocket(l).getOrDie("Could not find expected socket element");if(rI){Ft(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=lc(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}Tx(e,t),e.addCommand("ToggleSidebar",((t,o)=>{YD.toggleSidebar(l,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=YD.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if(YD.toggleView(l,o)){const t=l.element;r.mothership.broadcastOn([ou()],{target:t}),H(a,(e=>{e.broadcastOn([ou()],{target:t})})),c(YD.whichView(l))&&(e.focus(),e.nodeChanged(),YD.refreshToolbar(l))}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=YD.whichView(l))&&void 0!==e?e:""}));const g=Eb(e);g!==lb.sliding&&g!==lb.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&(YD.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{_x(t,!e)},isEnabled:()=>!Um.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const iI=e=>/^[0-9\.]+(|px)$/i.test(""+e)?A.some(parseInt(""+e,10)):A.none(),lI=e=>h(e)?e+"px":e,cI=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},dI=e=>{const t=vb(e),o=yb(e),n=wb(e);return iI(t).map((e=>cI(e,o,n)))},{ToolbarLocation:uI,ToolbarMode:mI}=fv,gI=(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=o,i=db.DOM,l=dv(e),c=gv(e),d=wb(e).or(dI(e)),u=n.shared.header,m=u.isPositionedAtTop,g=Eb(e),p=g===mI.sliding||g===mI.floating,h=Ms(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(x(0),(e=>e.components().length>1?Gt(e.components()[1].element):0)):0,v=()=>{H(a,(e=>{e.broadcastOn([nu()],{})}))},y=o=>{if(!f())return;l||s.on((e=>{const o=d.getOrThunk((()=>qo().width-Jt(t).left-10));It(e.element,"max-width",o+"px")}));const n=jo(),a=!(l||l||!(Kt(r.outerContainer.element).left+eo(r.outerContainer.element)>=window.innerWidth-40||zt(r.outerContainer.element,"width").isSome())||(It(r.outerContainer.element,"position","absolute"),It(r.outerContainer.element,"left","0px"),Pt(r.outerContainer.element,"width"),0));if(p&&YD.refreshToolbar(r.outerContainer),!l){const o=jo(),i=Ce(n.left!==o.left,n);((o,n)=>{s.on((s=>{const a=YD.getToolbar(r.outerContainer),i=b(a),l=Qo(t),c=((e,t)=>pv(e)?RA(t):A.none())(e,r.outerContainer.element),d=c.fold((()=>l.x),(e=>{const t=Qo(e);return et(e,St())?l.x:l.x-t.x})),u=Ce(o,Math.ceil(r.outerContainer.element.dom.getBoundingClientRect().width)).filter((e=>e>150)).map((e=>{const t=n.getOr(jo()),o=window.innerWidth-(d-t.left),s=Math.max(Math.min(e,o),150);return o<e&&It(r.outerContainer.element,"width",s+"px"),{width:s+"px"}})).getOr({width:"max-content"}),g={position:"absolute",left:Math.round(d)+"px",top:c.fold((()=>m()?Math.max(l.y-Gt(s.element)+i,0):l.bottom),(e=>{var t;const o=Qo(e),n=null!==(t=e.dom.scrollTop)&&void 0!==t?t:0,r=et(e,St())?Math.max(l.y-Gt(s.element)+i,0):l.y-o.y+n-Gt(s.element)+i;return m()?r:l.bottom}))+"px"};Ft(r.outerContainer.element,{...g,...u})}))})(a,i),i.each((e=>{Go(e.left,o.top)}))}c&&s.on(o),v()},w=()=>!(l||!c||!f())&&s.get().exists((o=>{const n=u.getDockingMode(),a=(o=>{switch(Mb(e)){case uI.auto:const e=YD.getToolbar(r.outerContainer),n=b(e),s=Gt(o.element)-n,a=Qo(t);if(a.y>s)return"top";{const e=st(t),o=Math.max(e.dom.scrollHeight,Gt(e));return a.bottom<o-s||on().bottom<a.bottom-s?"bottom":"top"}case uI.bottom:return"bottom";case uI.top:default:return"top"}})(o);return a!==n&&(i=a,s.on((e=>{iM.setModes(e,[i]),u.setDockingMode(i);const t=m()?Oc.TopToBottom:Oc.BottomToTop;Ot(e.element,_c,t)})),!0);var i}));return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),It(r.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),H(a,(e=>{Pt(e.element,"display")})),w(),pv(e)?y((e=>iM.isDocked(e)?iM.reset(e):iM.refresh(e))):y(iM.refresh)},hide:()=>{h.set(!1),It(r.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),H(a,(e=>{It(e.element,"display","none")}))},update:y,updateMode:()=>{w()&&y(iM.reset)},repositionPopups:v}},pI=(e,t)=>{const o=Qo(e);return{pos:t?o.y:o.bottom,bounds:o}};var hI=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r}=t,a=ac(),i=ze(s.targetNode),l=gI(e,i,t,n,a),c=Ib(e);sB(e);const d=()=>{if(a.isSet())return void l.show();a.set(YD.getHeader(r.outerContainer).getOrDie());const s=uv(e);pv(e)?(Ud(i,r.mothership),Ud(i,t.popupUi.mothership)):Pd(s,r.mothership),Pd(s,t.dialogUi.mothership);const d=()=>{nI(e,t,o,n),YD.setMenubar(r.outerContainer,JD(e,o)),l.show(),((e,t,o,n)=>{const s=Ms(pI(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=pI(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&yw(e,n),o.isVisible()&&(i!==r?o.update(iM.reset):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(iM.reset))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))}));let a=0;const i=t_((()=>o.update(iM.refresh)),33);e.on("ScrollWindow",(()=>{const e=jo().left;e!==a&&(a=e,i.throttle()),o.updateMode()})),pv(e)&&e.on("ElementScroll",(e=>{o.update(iM.refresh)}));const l=rc();l.set(cc(ze(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{l.clear()}))})(e,i,l,c),e.nodeChanged()};c?e.once("SkinLoaded",d):d()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),Tx(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{_x(t,!e)},isEnabled:()=>!Um.isDisabled(r.outerContainer)};return{editorContainer:r.outerContainer.element.dom,api:u}}});const fI="contexttoolbar-hide",bI=(e,t)=>jr(C_,((o,n)=>{const s=(e=>({hide:()=>Rr(e,br()),getValue:()=>wu.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),vI=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=Zh(Pv.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:Ml([Yp.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(Lr(e),!0))),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})),s=((e,t,o)=>{const n=V(t,(t=>Zh(((e,t,o)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o)=>{const{primary:n,...s}=t.original,r=Kn(Hy({...s,type:"togglebutton",onAction:b}));return KB(r,o,[bI(e,t)])})(e,t,o):((e,t,o)=>{const{primary:n,...s}=t.original,r=Kn(Ly({...s,type:"button",onAction:b}));return XB(r,o,[bI(e,t)])})(e,t,o))(e,t,o))));return{asSpecs:()=>V(n,(e=>e.asSpec())),findPrimary:e=>re(t,((t,o)=>t.primary?A.from(n[o]).bind((t=>t.getOpt(e))).filter(C(Um.isDisabled)):A.none()))}})(n,e.commands,t);return[{title:A.none(),items:[n.asSpec()]},{title:A.none(),items:s.asSpecs()}]},yI=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,xI=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=gt(ze(e.startContainer),e.startOffset).element;return(qe(o)?at(o):A.some(o)).filter($e).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=jo();return Zo(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=en(ze(e.getBody()));return Zo(o.x+t.left,o.y+t.top,t.width,t.height)}},wI=(e,t,o,n=0)=>{const s=qo(window),r=Qo(ze(e.getContentAreaContainer())),a=tv(e)||rv(e)||iv(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Zo(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=ze(e.getContainer()),i=wi(a,".tox-editor-header").getOr(a),l=Qo(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Qo(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Zo(i,c,l,d-c)}},SI={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},kI={maxHeightFunction:fc(),maxWidthFunction:JM()},CI=e=>"node"===e,OI=(e,t,o,n,s)=>{const r=xI(e),a=n.lastElement().exists((e=>et(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=gt(ze(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&et(n.element,t)})(e,o)?a?TE:SE:a?((e,o,s)=>{const a=zt(e,"position");It(e,"position",o);const i=yI(r,Qo(t),-20)&&!n.isReposition()?AE:TE;return a.each((t=>It(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+jo().top:s.y)+(Gt(t)+12)<=r.y?SE:kE},_I=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...OI(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>CI(n)?[s(e)]:[];return t?{onLtr:e=>[fl,ul,ml,gl,pl,hl].concat(r(e)),onRtl:e=>[fl,ml,ul,pl,gl,hl].concat(r(e))}:{onLtr:e=>[hl,fl,gl,ul,pl,ml].concat(r(e)),onRtl:e=>[hl,fl,pl,ml,gl,ul].concat(r(e))}},TI=(e,t)=>{const o=U(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=P(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},EI=(e,t)=>{const o={},n=[],s=[],r={},a={},i=ae(e);return H(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Kn(Xn("ContextForm",qy,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,Xn("ContextToolbar",Yy,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},AI=da("forward-slide"),MI=da("backward-slide"),DI=da("change-slide-event"),BI="tox-pop--resizing",II="tox-pop--transition",FI=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=Io().deviceType.isTouch,i=ac(),l=ac(),c=ac(),d=di((e=>{const t=Ms([]);return Yh.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),Yh.getContent(e).each((e=>{Pt(e.element,"visibility")})),$a(e.element,BI),Pt(e.element,"width")},inlineBehaviours:Ml([sh("context-toolbar-events",[Jr(sr(),((e,t)=>{"width"===t.event.raw.propertyName&&($a(e.element,BI),Pt(e.element,"width"))})),jr(DI,((e,t)=>{const o=e.element;Pt(o,"width");const n=Qt(o);Yh.setContent(e,t.event.contents),ja(o,BI);const s=Qt(o);It(o,"width",n+"px"),Yh.getContent(e).each((e=>{t.event.focus.bind((e=>(zl(e),Ul(o)))).orThunk((()=>(Yp.focusIn(e),Pl(bt(o)))))})),setTimeout((()=>{It(e.element,"width",s+"px")}),0)})),jr(AI,((e,o)=>{Yh.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:Pl(bt(e.element))}]))})),Nr(e,DI,{contents:o.event.forwardContents,focus:A.none()})})),jr(MI,((e,o)=>{ne(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),Nr(e,DI,{contents:ui(o.bar),focus:o.focus})}))}))]),Yp.config({mode:"special",onEscape:o=>ne(t.get()).fold((()=>e.onEscape()),(e=>(Rr(o,MI),A.some(!0))))})]),lazySink:()=>an.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),A.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=CI(t)?1:0;return wI(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=xe(c.get(),"node")?((e,t)=>t.filter((e=>wt(e)&&Ge(e))).map(en).getOrThunk((()=>xI(e))))(e,i.get()):xI(e);return t.height<=0||!yI(o,t,.01)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),Yh.hide(d)},h=()=>{if(Yh.isOpen(d)){const e=d.element;Pt(e,"display"),g()?It(e,"display","none"):(l.set(0),Yh.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:Ml([Yp.config({mode:"acyclic"}),sh("pop-dialog-wrap-events",[Zr((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>Yp.focusIn(t)))})),Qr((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=to((()=>EI(t,(e=>{const t=y([e]);Nr(d,AI,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=Eb(e)===lb.scrolling?lb.scrolling:lb.default,i=q(V(t,(t=>"contexttoolbar"===t.type?((t,o)=>oI(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,A.some(["form:"])))(s,t):((e,t)=>vI(e,t))(t,r.providers))));return SD({type:a,uid:da("context-toolbar"),initGroups:i,onEscape:A.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(S.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:xc(12,0,SI),layouts:{onLtr:()=>[bl],onRtl:()=>[vl]},overrides:kI}:{bubble:xc(0,12,SI,1/12),layouts:_I(e,o,n,t),overrides:kI})(e,t,a(),{lastElement:i.get,isReposition:()=>xe(l.get(),0),getMode:()=>Ad.getMode(o)});return vn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;Pt(b,"display"),(e=>xe(Se(e,i.get(),et),!0))(n)||($a(b,II),Ad.reset(o,d)),Yh.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[II],mode:"placement"}},(()=>A.some(u()))),n.fold(i.clear,i.set),g()&&It(b,"display","none")};let w=!1;const S=t_((()=>{!e.hasFocus()||e.removed||w||(qa(d.element,II)?S.throttle():((e,t)=>{const o=ze(t.getBody()),n=e=>et(e,o),s=ze(t.selection.getNode());return(e=>!n(e)&&!tt(o,e))(s)?A.none():((e,t,o)=>{const n=TI(e,t);if(n.contextForms.length>0)return A.some({elem:e,toolbars:[n.contextForms[0]]});{const t=TI(e,o);if(t.contextForms.length>0)return A.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>N(e,(e=>e.position===t)),o=t=>U(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=V(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return A.some({elem:e,toolbars:o})}return A.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?A.none():Rs(t,(e=>{if($e(e)){const{contextToolbars:t,contextForms:n}=TI(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>G(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>U(e,(e=>e.position===t))))}})(t);return s.length>0?A.some({elem:e,toolbars:s}):A.none()}return A.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,A.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",S.throttle),e.on(fI,p),e.on("contexttoolbar-show",(t=>{const o=v();be(o.lookupTable,t.toolbarKey).each((o=>{x([o],Ce(t.target!==e,t.target)),Yh.getContent(d).each(Yp.focusIn)}))})),e.on("focusout",(t=>{Xh.setEditorTimeout(e,(()=>{Ul(o.element).isNone()&&Ul(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("ExecCommand",(({command:e})=>{"toggleview"===e.toLowerCase()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&S.throttle()})),e.on("dragstart",(()=>{w=!0})),e.on("dragend drop",(()=>{w=!1})),e.on("NodeChange",(e=>{Ul(d.element).fold(S.throttle,b)}))}))},RI=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=ac();return V(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(xe(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},NI=e=>{RI(e,(e=>({name:"lineheight",text:"Line height",icon:"line-height",getOptions:sv,hash:e=>((e,t)=>TB(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:w,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>A.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t),onToolbarSetup:kw(e),onMenuSetup:kw(e)}))(e)),(e=>A.from(_b(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:x(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=ze(e.selection.getNode());return Ns(t,(e=>A.some(e).filter($e).bind((e=>Et(e,"lang").map((t=>({code:t,customCode:Et(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=rc();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),Sw(o.clear,kw(e)(t))},onMenuSetup:kw(e)}))))(e).each((t=>RI(e,t)))},LI=e=>Ow(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent")&&e.selection.isEditable())})),zI=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),Sw((()=>e.off("PastePlainTextToggle",n)),kw(e)(o))},VI=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},HI=e=>{(e=>{(e=>{e_.each([{name:"bold",text:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:Cw(e,t.name),onAction:VI(e,t.name),shortcut:t.shortcut})}));for(let t=1;t<=6;t++){const o="h"+t,n=`Access+${t}`;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:Cw(e,o),onAction:VI(e,o),shortcut:n})}})(e),(e=>{e_.each([{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"help",text:"Help",action:"mceHelp",icon:"help",shortcut:"Alt+0"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"print",text:"Print",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:Tw(e,t.action),shortcut:t.shortcut})})),e_.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:kw(e),onAction:Tw(e,t.action)})}))})(e),(e=>{e_.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:Tw(e,t.action),onSetup:Cw(e,t.name)})}))})(e)})(e),(e=>{e_.each([{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:Tw(e,t.action)})})),e_.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onSetup:kw(e),onAction:Tw(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onSetup:kw(e),onAction:VI(e,"code")})})(e)},PI=(e,t)=>Ow(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),UI=e=>Ow(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),WI=(e,t)=>{(e=>{H([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:Tw(e,t.cmd),onSetup:Cw(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onSetup:kw(e),onAction:Tw(e,"JustifyNone")})})(e),HI(e),((e,t)=>{((e,t)=>{const o=iB(t,pB(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),onSetup:kw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=iB(t,CB(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),onSetup:kw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=iB(t,LB(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",onSetup:kw(e),getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=iB(t,vB(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",onSetup:kw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=iB(t,RB(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",onSetup:kw(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:PI(e,"hasUndo"),onAction:Tw(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:PI(e,"hasRedo"),onAction:Tw(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:PI(e,"hasUndo"),onAction:Tw(e,"undo"),shortcut:"Meta+Z"}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:PI(e,"hasRedo"),onAction:Tw(e,"redo"),shortcut:"Meta+Y"})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=$w(e),o=qw(e),n=Ms(t),s=Ms(o);nS(e,"forecolor","forecolor",n),nS(e,"backcolor","hilitecolor",s),sS(e,"forecolor","forecolor","Text color",n),sS(e,"backcolor","hilitecolor","Background color",s)})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:Tw(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:UI(e),onAction:Tw(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:LI(e),onAction:Tw(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onSetup:kw(e),onAction:Tw(e,"indent")})})(e)})(e),NI(e),(e=>{const t=Ms(Xb(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:zI(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:zI(e,t)})})(e)},jI=e=>r(e)?e.split(/[ ,]/):e,GI=e=>t=>t.options.get(e),$I=GI("contextmenu_never_use_native"),qI=GI("contextmenu_avoid_overlap"),YI=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:U(o,(e=>ve(t,e)))},XI=(e,t)=>({type:"makeshift",x:e,y:t}),KI=e=>"longpress"===e.type||0===e.type.indexOf("touch"),JI=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(KI(e)){const t=e.touches[0];return XI(t.pageX,t.pageY)}return XI(e.pageX,e.pageY)})(t):((e,t)=>{const o=db.DOM.getPos(e);return((e,t,o)=>XI(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(KI(e)){const t=e.touches[0];return XI(t.clientX,t.clientY)}return XI(e.clientX,e.clientY)})(t)):ZI(e),ZI=e=>({type:"selection",root:ze(e.selection.getNode())}),QI=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:A.some(ze(e.selection.getNode())),root:ze(e.getBody())}))(e);case"point":return JI(e,t);case"selection":return ZI(e)}},eF=(e,t,o,n,s,r)=>{const a=o(),i=QI(e,t,r);z_(a,vv.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:A.none()}).map((e=>{t.preventDefault(),Yh.showMenuAt(s,{anchor:i},{menu:{markers:Iv("normal")},data:e})}))},tF={onLtr:()=>[fl,ul,ml,gl,pl,hl,SE,kE,wE,yE,xE,vE],onRtl:()=>[fl,ml,ul,pl,gl,hl,SE,kE,xE,vE,wE,yE]},oF={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},nF=(e,t,o,n,s,r)=>{const a=Io(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=QI(e,t,o);return{bubble:xc(0,"point"===o?12:0,oF),layouts:tF,overrides:{maxWidthFunction:JM(),maxHeightFunction:fc()},...n}})(e,t,r);z_(o,vv.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:A.none()}).map((o=>{t.preventDefault();const l=a?Gh.HighlightMenuAndItem:Gh.HighlightNone;Yh.showMenuWithinBounds(s,{anchor:i},{menu:{markers:Iv("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>A.some(wI(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(fI)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{Xh.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return sd(e.getWin(),qc.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},sF=e=>r(e)?"|"===e:"separator"===e.type,rF={type:"separator"},aF=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return rF;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:V(t,aF)}};default:const o=e;return{type:"menuitem",...t(o),onAction:v(o.onAction)}}},iF=(e,t)=>{if(0===t.length)return e;const o=ne(e).filter((e=>!sF(e))).fold((()=>[]),(e=>[rF]));return e.concat(o).concat(t).concat([rF])},lF=(e,t)=>!(e=>"longpress"===e.type||ve(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),cF=(e,t)=>lF(e,t)?e.selection.getStart(!0):t.target,dF=(e,t,o)=>{const n=Io().deviceType.isTouch,s=di(Yh.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:Ml([sh("dismissContextMenu",[jr(_r(),((t,o)=>{tu.close(t),e.focus()}))])])})),a=()=>Yh.hide(s),i=t=>{if($I(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!$I(e))(e,t)||(e=>0===YI(e).length)(e))return;const a=((e,t)=>{const o=qI(e),n=lF(e,t)?"selection":"point";if(De(o)){const s=cF(e,t);return tk(ze(s),o)?"node":n}return n})(e,t);(n()?nF:eF)(e,t,(()=>{const o=cF(e,t),n=e.ui.registry.getAll(),s=YI(e);return((e,t,o)=>{const n=j(t,((t,n)=>be(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n)&&De(Me(n)))return iF(t,n.split(" "));if(l(n)&&n.length>0){const e=V(n,aF);return iF(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&sF(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},uF=Ds([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),mF=e=>t=>t.translate(-e.left,-e.top),gF=e=>t=>t.translate(e.left,e.top),pF=e=>(t,o)=>j(e,((e,t)=>t(e)),Yt(t,o)),hF=(e,t,o)=>e.fold(pF([gF(o),mF(t)]),pF([mF(t)]),pF([])),fF=(e,t,o)=>e.fold(pF([gF(o)]),pF([]),pF([gF(t)])),bF=(e,t,o)=>e.fold(pF([]),pF([mF(o)]),pF([gF(t),mF(o)])),vF=(e,t,o)=>{const n=e.fold(((e,t)=>({position:A.some("absolute"),left:A.some(e+"px"),top:A.some(t+"px")})),((e,t)=>({position:A.some("absolute"),left:A.some(e-o.left+"px"),top:A.some(t-o.top+"px")})),((e,t)=>({position:A.some("fixed"),left:A.some(e+"px"),top:A.some(t+"px")})));return{right:A.none(),bottom:A.none(),...n}},yF=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(bF,xF),s(fF,wF),s(hF,SF))},xF=uF.offset,wF=uF.absolute,SF=uF.fixed,kF=(e,t)=>{const o=Tt(e,t);return u(o)?NaN:parseInt(o,10)},CF=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=kF(o,t.leftAttr),s=kF(o,t.topAttr);return isNaN(n)||isNaN(s)?A.none():A.some(Yt(n,s))})(e,t).fold((()=>o),(e=>SF(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?_F(e,t,a,s,r):TF(e,t,a,s,r),l=hF(a,s,r);return((e,t,o)=>{const n=e.element;Ot(n,t.leftAttr,o.left+"px"),Ot(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:SF(l.left,l.top),extra:A.none()})),(e=>({coord:e.output,extra:e.extra})))},OF=(e,t,o,n)=>re(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=fF(e,s,r),i=fF(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?A.some({output:yF(e.output,t,o,n),extra:e.extra}):A.none()})),_F=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return OF(r,o,n,s).orThunk((()=>{const e=j(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=fF(e,s,r),i=fF(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return Yt(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:A.some(a),snap:A.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:A.some(a),snap:A.some(t)}:e))}),{deltas:A.none(),snap:A.none()});return e.snap.map((e=>({output:yF(e.output,o,n,s),extra:e.extra})))}))},TF=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return OF(r,o,n,s)};var EF=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=ot(e.element),o=jo(t),r=NA(s),a=((e,t,o)=>({coord:yF(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=vF(a.coord,0,r);Rt(s,i)}}});const AF="data-initial-z-index",MF=(e,t)=>{e.getSystem().addToGui(t),(e=>{at(e.element).filter($e).each((t=>{zt(t,"z-index").each((e=>{Ot(t,AF,e)})),It(t,"z-index",Nt(e.element,"z-index"))}))})(t)},DF=e=>{(e=>{at(e.element).filter($e).each((e=>{Et(e,AF).fold((()=>Pt(e,"z-index")),(t=>It(e,"z-index",t))),Mt(e,AF)}))})(e),e.getSystem().removeFromGui(e)},BF=(e,t,o)=>e.getSystem().build(uk.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var IF=xs("snaps",[ss("getSnapPoints"),zi("onSensor"),ss("leftAttr"),ss("topAttr"),ws("lazyViewport",on),ws("mustSnap",!1)]);const FF=[ws("useFixed",T),ss("blockerClass"),ws("getTarget",w),ws("onDrag",b),ws("repositionTarget",!0),ws("onDrop",b),Ts("getBounds",on),IF],RF=e=>{return(t=zt(e,"left"),o=zt(e,"top"),n=zt(e,"position"),t.isSome()&&o.isSome()&&n.isSome()?A.some(((e,t,o)=>("fixed"===o?SF:xF)(parseInt(e,10),parseInt(t,10)))(t.getOrDie(),o.getOrDie(),n.getOrDie())):A.none()).getOrThunk((()=>{const t=Kt(e);return wF(t.left,t.top)}));var t,o,n},NF=(e,t)=>({bounds:e.getBounds(),height:$t(t.element),width:eo(t.element)}),LF=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>NF(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=ot(e.element),a=jo(r),i=NA(s),l=RF(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=fF(t,o,n),i=ol(a.left,r.x,r.x+r.width-s.width),l=ol(a.top,r.y,r.y+r.height-s.height),c=wF(i,l);return t.fold((()=>{const e=bF(c,o,n);return xF(e.left,e.top)}),x(c),(()=>{const e=hF(c,o,n);return SF(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>xF(e+a,t+i)),((e,t)=>wF(e+a,t+i)),((e,t)=>SF(e+a,t+i))));var t,a,i;const l=hF(e,n,s);return SF(l.left,l.top)}),(t=>{const a=CF(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=vF(c,0,i);Rt(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},zF=(e,t,o,n)=>{t.each(DF),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;Mt(o,t.leftAttr),Mt(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},VF=e=>(t,o)=>{const n=e=>{o.setStartData(NF(t,e))};return Pr([jr(Sr(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var HF=Object.freeze({__proto__:null,getData:e=>A.from(Yt(e.x,e.y)),getDelta:(e,t)=>Yt(t.left-e.left,t.top-e.top)});const PF=(e,t,o)=>[jr(Gs(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>zF(n,A.some(l),e,t),a=ok(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),LF(n,e,t,HF,o)}},l=BF(n,e.blockerClass,(e=>Pr([jr(Gs(),e.forceDrop),jr(Ys(),e.drop),jr($s(),((t,o)=>{e.move(o.event)})),jr(qs(),e.delayDrop)]))(i));o(n),MF(n,l)}))],UF=[...FF,Ui("dragger",{handlers:VF(PF)})];var WF=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return A.some(Yt(t.clientX,t.clientY))})(t):A.none()},getDelta:(e,t)=>Yt(t.left-e.left,t.top-e.top)});const jF=(e,t,o)=>{const n=ac(),s=o=>{zF(o,n.get(),e,t),n.clear()};return[jr(Ps(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{LF(r,e,t,WF,o)}},c=BF(r,e.blockerClass,(e=>Pr([jr(Ps(),e.forceDrop),jr(Ws(),e.drop),jr(js(),e.drop),jr(Us(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),MF(r,c)})),jr(Us(),((o,n)=>{n.stop(),LF(o,e,t,WF,n.event)})),jr(Ws(),((e,t)=>{t.stop(),s(e)})),jr(js(),s)]},GF=UF,$F=[...FF,Ui("dragger",{handlers:VF(jF)})],qF=[...FF,Ui("dragger",{handlers:VF(((e,t,o)=>[...PF(e,t,o),...jF(e,t,o)]))})];var YF=Object.freeze({__proto__:null,mouse:GF,touch:$F,mouseOrTouch:qF}),XF=Object.freeze({__proto__:null,init:()=>{let e=A.none(),t=A.none();const o=x({});return Ea({readState:o,reset:()=>{e=A.none(),t=A.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=A.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=A.some(e)}})}});const KF=Fl({branchKey:"mode",branches:YF,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:A.from(e.extra)})},state:XF,apis:EF}),JF=(e,t,o,n,s,r)=>e.fold((()=>KF.snap({sensor:wF(o-20,n-20),range:Yt(s,r),output:wF(A.some(o),A.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return KF.snap({sensor:wF(s,r),range:Yt(40,40),output:wF(A.some(o-a.width/2),A.some(n-a.height/2)),extra:{td:t}})})),ZF=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>et(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),QF=e=>Zh(Kh.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:Ml([KF.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),jk.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),eR=(e,t)=>{const o=Ms([]),n=Ms([]),s=Ms(!1),r=ac(),a=ac(),i=e=>{const o=en(e);return JF(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=en(e);return JF(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=ZF((()=>V(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=ZF((()=>V(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=QF(c),m=QF(d),g=di(u.asSpec()),p=di(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);KF.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();Pt(t.element,"display");const i=rt(ze(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&It(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");if(Io().deviceType.isTouch()){const i=e=>V(e,ze);e.on("TableSelectionChange",(e=>{s.get()||(Nd(t,g),Nd(t,p),s.set(!0));const l=ze(e.start),c=ze(e.finish);r.set(l),a.set(c),A.from(e.otherCells).each((e=>{o.set(i(e.upOrLeftCells)),n.set(i(e.downOrRightCells)),f(l),b(c)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(Vd(g),Vd(p),s.set(!1)),r.clear(),a.clear()}))}},tR=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:Ml([Yp.config({mode:"flow",selector:"div[role=button]"}),Um.config({disabled:o.isDisabled}),Ex(),vk.config({}),nh.config({}),sh("elementPathEvents",[Zr(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>Yp.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=ww(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?j(r,((t,n,r)=>{const a=((t,n,s)=>Kh.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s,"aria-level":s+1}},components:[ai(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:Ml([Ax(o.isDisabled),Ex()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[ai(` ${s} `)]},a])}),[]):[];nh.set(t,a)}))}))])]),components:[]}};var oR;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(oR||(oR={}));const nR=(e,t,o)=>{const n=ze(e.getContainer()),s=((e,t,o,n,s)=>{const r={height:cI(n+t.top,xb(e),Sb(e))};return o===oR.Both&&(r.width=cI(s+t.left,yb(e),wb(e))),r})(e,t,o,Gt(n),Qt(n));le(s,((e,t)=>{h(e)&&It(n,t,lI(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},sR=(e,t,o,n)=>{const s=Yt(20*o,20*n);return nR(e,s,t),A.some(!0)},rR=(e,t)=>{const o=()=>{const o=[],n=Zb(e),s=$b(e),r=qb(e)||e.hasPlugin("wordcount");return s&&o.push(tR(e,{},t)),n&&o.push((()=>{const e=Hx("Alt+0");return{dom:{tag:"div",classes:["tox-statusbar__help-text"]},components:[ai(Kf.translate(["Press {0} for help",e]))]}})()),r&&o.push((()=>{const o=[];return e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>nh.set(e,[ai(t.translate(["{0} "+n,o[n]]))]);return Kh.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:Ml([Ax(t.isDisabled),Ex(),vk.config({}),nh.config({}),wu.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),sh("wordcount-events",[ta((e=>{const t=wu.getValue(e),n="words"===t.mode?"characters":"words";wu.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Zr((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=wu.getValue(t);wu.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[gr()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),qb(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=poweredby&utm_source=tiny&utm_medium=referral&utm_content=v7",rel:"noopener",target:"_blank","aria-label":Kf.translate(["Powered by {0}","Tiny"])},innerHtml:'<svg width="50px" height="16px" viewBox="0 0 50 16" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.143 0c2.608.015 5.186 2.178 5.186 5.331 0 0 .077 3.812-.084 4.87-.361 2.41-2.164 4.074-4.65 4.496-1.453.284-2.523.49-3.212.623-.373.071-.634.122-.785.152-.184.038-.997.145-1.35.145-2.732 0-5.21-2.04-5.248-5.33 0 0 0-3.514.03-4.442.093-2.4 1.758-4.342 4.926-4.963 0 0 3.875-.752 4.036-.782.368-.07.775-.1 1.15-.1Zm1.826 2.8L5.83 3.989v2.393l-2.455.475v5.968l6.137-1.189V9.243l2.456-.476V2.8ZM5.83 6.382l3.682-.713v3.574l-3.682.713V6.382Zm27.173-1.64-.084-1.066h-2.226v9.132h2.456V7.743c-.008-1.151.998-2.064 2.149-2.072 1.15-.008 1.987.92 1.995 2.072v5.065h2.455V7.359c-.015-2.18-1.657-3.929-3.837-3.913a3.993 3.993 0 0 0-2.908 1.296Zm-6.3-4.266L29.16 0v2.387l-2.456.475V.476Zm0 3.2v9.132h2.456V3.676h-2.456Zm18.179 11.787L49.11 3.676H46.58l-1.612 4.527-.46 1.382-.384-1.382-1.611-4.527H39.98l3.3 9.132L42.15 16l2.732-.537ZM22.867 9.738c0 .752.568 1.075.921 1.075.353 0 .668-.047.998-.154l.537 1.765c-.23.154-.92.537-2.225.537-1.305 0-2.655-.997-2.686-2.686a136.877 136.877 0 0 1 0-4.374H18.8V3.676h1.612v-1.98l2.455-.476v2.456h2.302V5.9h-2.302v3.837Z"/>\n</svg>\n'.trim()},behaviours:Ml([ch.config({})])}]}),{dom:{tag:"div",classes:["tox-statusbar__right-container"]},components:o}})()),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container",...(()=>{const e="tox-statusbar__text-container--flex-start",t="tox-statusbar__text-container--flex-end";if(n){const o="tox-statusbar__text-container-3-cols";return r||s?r&&!s?[o,t]:[o,e]:[o,"tox-statusbar__text-container--space-around"]}return[r&&!s?t:e]})()]},components:o}]:[]};return{dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const n=o(),s=((e,t)=>{const o=(e=>{const t=Yb(e);return!1===t?oR.None:"both"===t?oR.Both:oR.Vertical})(e);if(o===oR.None)return A.none();const n=o===oR.Both?"Press the arrow keys to resize the editor.":"Press the Up and Down arrow keys to resize the editor.";return A.some(rb("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{"aria-label":t.translate(n),"data-mce-name":"resize-handle"},behaviours:[KF.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>nR(e,s,o),blockerClass:"tox-blocker"}),Yp.config({mode:"special",onLeft:()=>sR(e,o,-1,0),onRight:()=>sR(e,o,1,0),onUp:()=>sR(e,o,0,-1),onDown:()=>sR(e,o,0,1)}),vk.config({}),ch.config({}),kx.config(t.tooltips.getConfig({tooltipText:t.translate("Resize")}))]},t.icons))})(e,t);return n.concat(s.toArray())})()}},aR=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),iR=(e,t)=>{const o=e.inline,n=o?hI:aI,s=gv(e)?bM:FA,r=(()=>{const e=ac(),t=ac(),o=ac();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>{const o=e.get().map((e=>e.mothership)),n=t.get().map((e=>e.mothership));return o.fold((()=>n.toArray()),(e=>n.fold((()=>[e]),(t=>et(e.element,t.element)?[e]:[e,t]))))},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),a=ac(),i=ac(),l=ac(),c=Io().deviceType.isTouch()?["tox-platform-touch"]:[],d=lv(e),u=Eb(e),m=Zh({dom:{tag:"div",classes:["tox-anchorbar"]}}),g=Zh({dom:{tag:"div",classes:["tox-bottom-anchorbar"]}}),p=()=>r.mainUi.get().map((e=>e.outerContainer)).bind(YD.getHeader),h=r.lazyGetInOuterOrDie("anchor bar",m.getOpt),f=r.lazyGetInOuterOrDie("bottom anchor bar",g.getOpt),b=r.lazyGetInOuterOrDie("toolbar",YD.getToolbar),v=r.lazyGetInOuterOrDie("throbber",YD.getThrobber),y=((e,t,o,n)=>{const s=Ms(!1),r=(e=>{const t=Ms(lv(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),a={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Kf.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get,tooltips:XE(e.dialog)},i=xA(t),l=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},n=Ms([]),s=Ms([]),r=Ms(!1);return e.on("PreInit",(s=>{const r=$E(e),a=YE(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=YE(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}})(t),c=(e=>({colorPicker:LE(e),hasCustomColors:zE(e),getColors:VE(e),getColorCols:HE(e)}))(t),d=(e=>({isDraggableModal:PE(e)}))(t),u={shared:{providers:a,anchors:NE(t,o,n,r.isPositionedAtTop),header:r},urlinput:i,styles:l,colorinput:c,dialog:d,isContextMenuOpen:()=>s.get(),setContextMenuState:e=>s.set(e)},m={...u,shared:{...u.shared,interpreter:e=>dE(e,{},m),getSink:e.popup}},g={...u,shared:{...u.shared,interpreter:e=>dE(e,{},g),getSink:e.dialog}};return{popup:m,dialog:g}})({popup:()=>an.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>an.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,h,f),x=()=>{const t=(()=>{const t={attributes:{[_c]:d?Oc.BottomToTop:Oc.TopToBottom}},o=YD.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:y.popup,onEscape:()=>{e.focus()}}),n=YD.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:y.popup.shared.getSink,providers:y.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:u,lazyToolbar:b,lazyHeader:()=>p().getOrDie("Could not find header element"),...t}),s=YD.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:y.popup.shared.providers,onEscape:()=>{e.focus()},type:u}),r=iv(e),a=rv(e),i=tv(e),l=Jb(e),c=YD.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]}}),g=r||a||i,h=l?[c,o]:[o];return YD.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(g?[]:["tox-editor-header--empty"]),...t},components:q([i?h:[],r?[s]:a?[n]:[],dv(e)?[]:[m.asSpec()]]),sticky:gv(e),editor:e,sharedBackstage:y.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[YD.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),YD.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=YD.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:y.popup}),r=YD.parts.viewWrapper({backstage:y.popup}),i=Gb(e)&&!o?A.some(rR(e,y.popup.shared.providers)):A.none(),l=q([d?[]:[t],o?[]:[n],d?[t]:[]]),h=YD.parts.editorContainer({components:q([l,o?[]:[g.asSpec()]])}),f=mv(e),v={role:"application",...Kf.isRtl()?{dir:"rtl"}:{},...f?{"aria-hidden":"true"}:{}},x=di(YD.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(o?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(c),styles:{visibility:"hidden",...f?{opacity:"0",border:"0"}:{}},attributes:v},components:[h,...o?[]:[r,...i.toArray()],s],behaviours:Ml([Ex(),Um.config({disableClass:"tox-tinymce--disabled"}),Yp.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=mk(x);return a.set(w),{mothership:w,outerContainer:x}},w=t=>{const o=lI((e=>{const t=(e=>{const t=bb(e),o=xb(e),n=Sb(e);return iI(t).map((e=>cI(e,o,n)))})(e);return t.getOr(bb(e))})(e)),n=lI((e=>dI(e).getOr(vb(e)))(e));return e.inline||(Ht("div","width",n)&&It(t.element,"width",n),Ht("div","height",o)?It(t.element,"height",o):It(t.element,"height","400px")),o};return{popups:{backstage:y.popup,getMothership:()=>aR("popups",l)},dialogs:{backstage:y.dialog,getMothership:()=>aR("dialogs",i)},renderUI:()=>{const o=x(),a=(()=>{const t=uv(e),o=et(St(),t)&&"grid"===Nt(t,"display"),n={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c),attributes:{...Kf.isRtl()?{dir:"rtl"}:{}}},behaviours:Ml([Ad.config({useFixed:()=>s.isDocked(p)})])},r={dom:{styles:{width:document.body.clientWidth+"px"}},events:Pr([jr(kr(),(e=>{It(e.element,"width",document.body.clientWidth+"px")}))])},a=di(vn(n,o?r:{})),l=mk(a);return i.set(l),{sink:a,mothership:l}})(),d=pv(e)?(()=>{const e={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-silver-popup-sink","tox-tinymce-aux"].concat(c),attributes:{...Kf.isRtl()?{dir:"rtl"}:{}}},behaviours:Ml([Ad.config({useFixed:()=>s.isDocked(p),getBounds:()=>t.getPopupSinkBounds()})])},o=di(e),n=mk(o);return l.set(n),{sink:o,mothership:n}})():(e=>(l.set(e.mothership),e))(a);r.dialogUi.set(a),r.popupUi.set(d),r.mainUi.set(o);return(t=>{const{mainUi:o,popupUi:r,uiMotherships:a}=t;ce(Ab(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:c,sidebars:d,views:m}=e.ui.registry.getAll(),g=av(e),h={menuItems:l,menus:hv(e),menubar:Rb(e),toolbar:g.getOrThunk((()=>Nb(e))),allowToolbarGroups:u===lb.floating,buttons:i,sidebar:d,views:m};var f;f=o.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{YD.focusMenubar(f)})),e.addShortcut("alt+F10","focus toolbar",(()=>{YD.focusToolbar(f)})),e.addCommand("ToggleToolbarDrawer",((e,t)=>{(null==t?void 0:t.skipFocus)?YD.toggleToolbarDrawerWithoutFocusing(f):YD.toggleToolbarDrawer(f)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>YD.isToolbarDrawerToggled(f))),((e,t,o)=>{const n=(e,n)=>{H([t,...o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{H([t,...o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(ou(),{target:e.target}),a=Yo(),i=lc(a,"touchstart",r),l=lc(a,"touchmove",(e=>n(xr(),e))),c=lc(a,"touchend",(e=>n(wr(),e))),d=lc(a,"mousedown",r),u=lc(a,"mouseup",(e=>{0===e.raw.button&&s(su(),{target:e.target})})),m=e=>s(ou(),{target:ze(e.target)}),g=e=>{0===e.button&&s(su(),{target:ze(e.target)})},p=()=>{H(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(Sr(),dc(e)),f=e=>{s(nu(),{}),n(kr(),dc(e))},b=bt(ze(e.getElement())),v=cc(b,"scroll",(o=>{requestAnimationFrame((()=>{if(null!=e.getContainer()){const s=QS(e,t.element).map((e=>[e.element,...e.others])).getOr([]);N(s,(e=>et(e,o.target)))&&(e.dispatch("ElementScroll",{target:o.target.dom}),n(Mr(),o))}}))})),y=()=>s(nu(),{}),x=t=>{t.state&&s(ou(),{target:ze(e.getContainer())})},w=e=>{s(ou(),{target:ze(e.relatedTarget.getContainer())})},S=t=>e.dispatch("focusin",t),k=t=>e.dispatch("focusout",t);e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",y),e.on("AfterProgressState",x),e.on("DismissPopups",w),H([t,...o],(e=>{e.element.dom.addEventListener("focusin",S),e.element.dom.addEventListener("focusout",k)}))})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",y),e.off("AfterProgressState",x),e.off("DismissPopups",w),H([t,...o],(e=>{e.element.dom.removeEventListener("focusin",S),e.element.dom.removeEventListener("focusout",k)})),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind(),v.unbind()})),e.on("detach",(()=>{H([t,...o],jd),H([t,...o],(e=>e.destroy()))}))})(e,o.mothership,a),s.setup(e,y.popup.shared,p),WI(e,y.popup),dF(e,y.popup.shared.getSink,y.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();H(ae(t),(o=>{const n=t[o],s=()=>xe(A.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),UM(e,v,y.popup.shared),FI(e,c,r.sink,{backstage:y.popup}),eR(e,r.sink);const b={targetNode:e.getElement(),height:w(o.outerContainer)};return n.render(e,t,h,y.popup,b)})({popupUi:d,dialogUi:a,mainUi:o,uiMotherships:r.getUiMotherships()})}}},lR=x([ss("lazySink"),gs("dragBlockClass"),Ts("getBounds",on),ws("useTabstopAt",E),ws("firstTabstop",0),ws("eventOrder",{}),Su("modalBehaviours",[Yp]),Vi("onExecute"),Pi("onEscape")]),cR={sketch:w},dR=x([Ju({name:"draghandle",overrides:(e,t)=>({behaviours:Ml([KF.config({mode:"mouse",getTarget:e=>vi(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),Xu({schema:[ss("dom")],name:"title"}),Xu({factory:cR,schema:[ss("dom")],name:"close"}),Xu({factory:cR,schema:[ss("dom")],name:"body"}),Ju({factory:cR,schema:[ss("dom")],name:"footer"}),Ku({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[ws("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),ws("components",[])],name:"blocker"})]),uR=Cm({name:"ModalDialog",configFields:lR(),partFields:dR(),factory:(e,t,o,n)=>{const s=ac(),r=da("modal-events"),a={...e.eventOrder,[Cr()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])};return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([ui(t)]),behaviours:Ml([ch.config({}),sh("dialog-blocker-events",[Jr(Ks(),(()=>{HM.isBlocked(t)||Yp.focusIn(t)}))])])});Nd(o,a),Yp.focusIn(t)},hide:e=>{s.clear(),at(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{Vd(e)}))}))},getBody:t=>dm(t,e,"body"),getFooter:t=>cm(t,e,"footer"),setIdle:e=>{HM.unblock(e)},setBusy:(e,t)=>{HM.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Cu(e.modalBehaviours,[nh.config({}),Yp.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt,firstTabstop:e.firstTabstop}),HM.config({getRoot:s.get}),sh(r,[Zr((t=>{((e,t)=>{const o=Et(e,"id").fold((()=>{const e=da("dialog-label");return Ot(t,"id",e),e}),w);Ot(e,"aria-labelledby",o)})(t.element,dm(t,e,"title").element)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),mR=In([ry,ay].concat(tx)),gR=Un,pR=[By("button"),yy,Os("align","end",["start","end"]),Ty,_y,bs("buttonType",["primary","secondary"])],hR=[...pR,ly],fR=[ls("type",["submit","cancel","custom"]),...hR],bR=[ls("type",["menu"]),vy,xy,yy,ms("items",mR),...pR],vR=[...pR,ls("type",["togglebutton"]),xy,yy,vy,_s("active",!1)],yR=Qn("type",{submit:fR,cancel:fR,custom:fR,menu:bR,togglebutton:vR}),xR=[ry,ly,ls("level",["info","warn","error","success"]),dy,ws("url","")],wR=In(xR),SR=[ry,ly,_y,By("button"),yy,Oy,bs("buttonType",["primary","secondary","toolbar"]),Ty],kR=In(SR),CR=[ry,ay],OR=CR.concat([wy]),_R=CR.concat([iy,_y]),TR=In(_R),ER=Un,AR=OR.concat([Ey("auto")]),MR=In(AR),DR=Ln([uy,ly,dy]),BR=OR.concat([Cs("storageKey","default")]),IR=In(BR),FR=Pn,RR=In(OR),NR=Pn,LR=CR.concat([Cs("tag","textarea"),is("scriptId"),is("scriptUrl"),vs("onFocus"),Ss("settings",void 0,Gn)]),zR=CR.concat([Cs("tag","textarea"),cs("init")]),VR=qn((e=>Xn("customeditor.old",Bn(zR),e).orThunk((()=>Xn("customeditor.new",Bn(LR),e))))),HR=Pn,PR=In(OR),UR=Fn(Tn),WR=e=>[ry,as("columns"),e],jR=[ry,is("html"),Os("presets","presentation",["presentation","document"])],GR=In(jR),$R=OR.concat([_s("border",!1),_s("sandboxed",!0),_s("streamContent",!1),_s("transparent",!0)]),qR=In($R),YR=Pn,XR=In(CR.concat([fs("height")])),KR=In([is("url"),hs("zoom"),hs("cachedWidth"),hs("cachedHeight")]),JR=OR.concat([fs("inputMode"),fs("placeholder"),_s("maximized",!1),_y]),ZR=In(JR),QR=Pn,eN=e=>[ry,iy,e,Os("align","start",["start","center","end"])],tN=[ly,uy],oN=[ly,ms("items",es(0,(()=>nN)))],nN=Rn([In(tN),In(oN)]),sN=OR.concat([ms("items",nN),_y]),rN=In(sN),aN=Pn,iN=OR.concat([us("items",[ly,uy]),ks("size",1),_y]),lN=In(iN),cN=Pn,dN=OR.concat([_s("constrain",!0),_y]),uN=In(dN),mN=In([is("width"),is("height")]),gN=CR.concat([iy,ks("min",0),ks("max",0)]),pN=In(gN),hN=Hn,fN=[ry,ms("header",Pn),ms("cells",Fn(Pn))],bN=In(fN),vN=OR.concat([fs("placeholder"),_s("maximized",!1),_y]),yN=In(vN),xN=Pn,wN=[ls("type",["directory","leaf"]),cy,is("id"),ps("menu",xM)],SN=In(wN),kN=wN.concat([ms("children",es(0,(()=>$n("type",{directory:CN,leaf:SN}))))]),CN=In(kN),ON=$n("type",{directory:CN,leaf:SN}),_N=[ry,ms("items",ON),vs("onLeafAction"),vs("onToggleExpand"),Es("defaultExpandedIds",[],Pn),fs("defaultSelectedId")],TN=In(_N),EN=OR.concat([Os("filetype","file",["image","media","file"]),_y,fs("picker_text")]),AN=In(EN),MN=In([uy,Ay]),DN=e=>ts("items","items",{tag:"required",process:{}},Fn(qn((t=>Xn(`Checking item of ${e}`,BN,t).fold((e=>an.error(Zn(e))),(e=>an.value(e))))))),BN=Mn((()=>{return $n("type",{alertbanner:wR,bar:In((e=DN("bar"),[ry,e])),button:kR,checkbox:TR,colorinput:IR,colorpicker:RR,dropzone:PR,grid:In(WR(DN("grid"))),iframe:qR,input:ZR,listbox:rN,selectbox:lN,sizeinput:uN,slider:pN,textarea:yN,urlinput:AN,customeditor:VR,htmlpanel:GR,imagepreview:XR,collection:MR,label:In(eN(DN("label"))),table:bN,tree:TN,panel:FN});var e})),IN=[ry,ws("classes",[]),ms("items",BN)],FN=In(IN),RN=[By("tab"),cy,ms("items",BN)],NN=[ry,us("tabs",RN)],LN=In(NN),zN=hR,VN=yR,HN=In([is("title"),rs("body",$n("type",{panel:FN,tabpanel:LN})),Cs("size","normal"),Es("buttons",[],VN),ws("initialData",{}),Ts("onAction",b),Ts("onChange",b),Ts("onSubmit",b),Ts("onClose",b),Ts("onCancel",b),Ts("onTabChange",b)]),PN=In([ls("type",["cancel","custom"]),...zN]),UN=In([is("title"),is("url"),hs("height"),hs("width"),ys("buttons",PN),Ts("onAction",b),Ts("onCancel",b),Ts("onClose",b),Ts("onMessage",b)]),WN=e=>a(e)?[e].concat(Y(fe(e),WN)):l(e)?Y(e,WN):[],jN=e=>r(e.type)&&r(e.name),GN={checkbox:ER,colorinput:FR,colorpicker:NR,dropzone:UR,input:QR,iframe:YR,imagepreview:KR,selectbox:cN,sizeinput:mN,slider:hN,listbox:aN,size:mN,textarea:xN,urlinput:MN,customeditor:HR,collection:DR,togglemenuitem:gR},$N=e=>{const t=(e=>U(WN(e),jN))(e),o=Y(t,(e=>(e=>A.from(GN[e.type]))(e).fold((()=>[]),(t=>[rs(e.name,t)]))));return In(o)},qN=e=>{var t;return{internalDialog:Kn(Xn("dialog",HN,e)),dataValidator:$N(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},YN={open:(e,t)=>{const o=qN(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Kn(Xn("dialog",UN,t))),redial:e=>qN(e)};var XN=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?Kp:Xp)(o,r)}))};return Pr([jr(mr(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;R(s.channels,n)&&o(t,s.data)}})),Zr(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),KN=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),JN=[ss("channel"),gs("renderComponents"),gs("updateState"),gs("initialData"),_s("reuseDom",!0)];const ZN=Bl({fields:JN,name:"reflecting",active:XN,apis:KN,state:Object.freeze({__proto__:null,init:()=>{const e=Ms(A.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(A.none())}}})}),QN=e=>{const t=[],o={};return le(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?an.error(t):an.value(o)},eL=(e,t,o)=>{const n=Zh(LO.sketch((n=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:V(e.items,(e=>lE(n,e,t,o)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[n.asSpec()]}],behaviours:Ml([Yp.config({mode:"acyclic",useTabstopAt:C(l_)}),(s=n,Em.config({find:s.getOpt})),qO(n,{postprocess:e=>QN(e).fold((e=>(console.error(e),{})),w)}),sh("dialog-body-panel",[jr(Ks(),((e,t)=>{e.getSystem().broadcastOn([p_],{newFocus:A.some(t.event.target)})}))])])};var s},tL=km({name:"TabButton",configFields:[ws("uid",void 0),ss("value"),ts("dom","dom",Sn((()=>({attributes:{role:"tab",id:da("aria"),"aria-selected":"false"}}))),zn()),gs("action"),ws("domModification",{}),Su("tabButtonBehaviours",[ch,Yp,wu]),ss("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:yh(e.action),behaviours:Cu(e.tabButtonBehaviours,[ch.config({}),Yp.config({mode:"execution",useSpace:!0,useEnter:!0}),wu.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),oL=x([ss("tabs"),ss("dom"),ws("clickToDismiss",!1),Su("tabbarBehaviours",[Zm,Yp]),Ni(["tabClass","selectedClass"])]),nL=Zu({factory:tL,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Zm.dehighlight(e,t),Nr(e,Br(),{tabbar:e,button:t})},o=(e,t)=>{Zm.highlight(e,t),Nr(e,Dr(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Zm.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),sL=x([nL]),rL=Cm({name:"Tabbar",configFields:oL(),partFields:sL(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Cu(e.tabbarBehaviours,[Zm.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{Ot(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{Ot(t.element,"aria-selected","false")}}),Yp.config({mode:"flow",getInitial:e=>Zm.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),aL=km({name:"Tabview",configFields:[Su("tabviewBehaviours",[nh])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:Cu(e.tabviewBehaviours,[nh.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),iL=x([ws("selectFirst",!0),zi("onChangeTab"),zi("onDismissTab"),ws("tabs",[]),Su("tabSectionBehaviours",[])]),lL=Xu({factory:rL,schema:[ss("dom"),ds("markers",[ss("tabClass"),ss("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),cL=Xu({factory:aL,name:"tabview"}),dL=x([lL,cL]),uL=Cm({name:"TabSection",configFields:iL(),partFields:dL(),factory:(e,t,o,n)=>{const s=(t,o)=>{cm(t,e,"tabbar").each((e=>{o(e).each(Lr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:ku(e.tabSectionBehaviours),events:Pr(q([e.selectFirst?[Zr(((e,t)=>{s(e,Zm.getFirst)}))]:[],[jr(Dr(),((t,o)=>{(t=>{const o=wu.getValue(t);cm(t,e,"tabview").each((n=>{G(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();Et(t.element,"id").each((e=>{Ot(n.element,"aria-labelledby",e)})),nh.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),jr(Br(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>cm(t,e,"tabview").map((e=>nh.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Zm.getCandidates(e);return G(o,(e=>wu.getValue(e)===t)).filter((t=>!Zm.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),mL=(e,t)=>{It(e,"height",t+"px"),It(e,"flex-basis",t+"px")},gL=(e,t,o)=>{vi(e,'[role="dialog"]').each((e=>{wi(e,'[role="tablist"]').each((n=>{o.get().map((o=>(It(t,"height","0"),It(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=st(e).dom,s=vi(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===Nt(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=Gt(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Qt(o)?Math.max(Gt(o),a):a,l=parseInt(Nt(e,"margin-top"),10)||0,c=parseInt(Nt(e,"margin-bottom"),10)||0;return r-(Gt(e)+l+c-i)})(e,t,n))))).each((e=>{mL(t,e)}))}))}))},pL=e=>wi(e,'[role="tabpanel"]'),hL="send-data-to-section",fL="send-data-to-view",bL=(e,t,o)=>{const n=Ms({}),s=e=>{const t=wu.getValue(e),o=QN(t).getOr({}),s=n.get(),r=vn(s,o);n.set(r)},r=e=>{const t=n.get();wu.setValue(e,t)},a=Ms(null),i=V(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[ai(o.shared.providers.translate(e.title))],view:()=>[LO.sketch((n=>({dom:{tag:"div",classes:["tox-form"]},components:V(e.items,(e=>lE(n,e,t,o))),formBehaviours:Ml([Yp.config({mode:"acyclic",useTabstopAt:C(l_)}),sh("TabView.form.events",[Zr(r),Qr(s)]),Nl.config({channels:Is([{key:hL,value:{onReceive:s}},{key:fL,value:{onReceive:r}}])})])})))]}))),l=(e=>{const t=ac(),o=[Zr((o=>{const n=o.element;pL(n).each((s=>{It(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>V(e,((n,s)=>{nh.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return nh.set(o,[]),r.height})))(e,s,o),r=(e=>oe(ee(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),gL(n,s,t),Pt(s,"visibility"),((e,t)=>{oe(e).each((e=>uL.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{gL(n,s,t)}))}))})),jr(kr(),(e=>{const o=e.element;pL(o).each((e=>{gL(o,e,t)}))})),jr(Bk,((e,o)=>{const n=e.element;pL(n).each((e=>{const o=Pl(bt(e));It(e,"visibility","hidden");const s=zt(e,"height").map((e=>parseInt(e,10)));Pt(e,"height"),Pt(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),gL(n,e,t)):s.each((t=>{mL(e,t)})),Pt(e,"visibility"),o.each(zl)}))}))];return{extraEvents:o,selectFirst:!1}})(i);return uL.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=wu.getValue(t);Nr(e,Dk,{name:n,oldName:a.get()}),a.set(n)},tabs:i,components:[uL.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[rL.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:Ml([vk.config({})])}),uL.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:Ml([sh("tabpanel",l.extraEvents),Yp.config({mode:"acyclic"}),Em.config({find:e=>oe(uL.getViewItems(e))}),YO(A.none(),(e=>(e.getSystem().broadcastOn([hL],{}),n.get())),((e,t)=>{n.set(t),e.getSystem().broadcastOn([fL],{})}))])})},vL=(e,t,o,n,s)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:Ml([GO(0),ZN.config({channel:`${u_}-${t}`,updateState:(e,t)=>A.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[bL(t,e.initialData,n)]:[eL(t,e.initialData,n)]},initialData:e})])}),yL=mb.deviceType.isTouch(),xL=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),wL=(e,t)=>uR.parts.close(Kh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:Ml([vk.config({})])})),SL=()=>uR.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),kL=(e,t)=>uR.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:Jh(`<p>${Xf(t.translate(e))}</p>`)}]}]}),CL=e=>uR.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),OL=(e,t)=>[uk.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),uk.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],_L=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return uR.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),A.some(!0)),useTabstopAt:e=>!l_(e),firstTabstop:e.firstTabstop,dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:Jh(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:yL?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:Ml([ch.config({}),sh("dialog-events",e.dialogEvents.concat([Jr(Ks(),((e,t)=>{HM.isBlocked(e)||Yp.focusIn(e)})),jr(Er(),((e,t)=>{e.getSystem().broadcastOn([p_],{newFocus:t.event.newFocus})}))])),sh("scroll-lock",[Zr((()=>{ja(St(),s)})),Qr((()=>{$a(St(),s)}))]),...e.extraBehaviours]),eventOrder:{[gr()]:["dialog-events"],[Cr()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[Or()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},TL=e=>Kh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),"data-mce-name":"close"}},buttonBehaviours:Ml([vk.config({}),kx.config(e.tooltips.getConfig({tooltipText:e.translate("Close")}))]),components:[rb("close",{tag:"span",classes:["tox-icon"]},e.icons)],action:e=>{Rr(e,_k)}}),EL=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:Ml([ZN.config({channel:`${d_}-${t}`,initialData:e,renderComponents:e=>[ai(n.translate(e.title))]})])}),AL=()=>({dom:Jh('<div class="tox-dialog__draghandle"></div>')}),ML=(e,t,o)=>((e,t,o)=>{const n=uR.parts.title(EL(e,t,A.none(),o)),s=uR.parts.draghandle(AL()),r=uR.parts.close(TL(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return uk.sketch({dom:Jh('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),DL=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:`${n.getOr(0)}px`,position:"absolute"}},behaviours:t,components:[{dom:Jh('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),BL=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{const n=wi(e().element,".tox-dialog__header").map((e=>Gt(e)));uR.setBusy(e(),((e,s)=>DL(o.message,s,t,n)))},onUnblock:()=>{uR.setIdle(e())}}),IL="tox-dialog--fullscreen",FL="tox-dialog--width-lg",RL="tox-dialog--width-md",NL=e=>{switch(e){case"large":return A.some(FL);case"medium":return A.some(RL);default:return A.none()}},LL=(e,t)=>{const o=ze(t.element.dom);qa(o,IL)||(Xa(o,[FL,RL]),NL(e).each((e=>ja(o,e))))},zL=(e,t)=>{const o=ze(e.element.dom),n=Ka(o),s=G(n,(e=>e===FL||e===RL)).or(NL(t));((e,t)=>{H(t,(t=>{((e,t)=>{const o=Ha(e)?e.dom.classList.toggle(t):((e,t)=>R(Pa(e),t)?Wa(e,t):Ua(e,t))(e,t);Ga(e)})(e,t)}))})(o,[IL,...s.toArray()])},VL=(e,t,o)=>di(_L({...e,firstTabstop:1,lazySink:o.shared.getSink,extraBehaviours:[KO({}),...e.extraBehaviours],onEscape:e=>{Rr(e,_k)},dialogEvents:t,eventOrder:{[mr()]:[ZN.name(),Nl.name()],[Cr()]:["scroll-lock",ZN.name(),"messages","dialog-events","alloy.base.behaviour"],[Or()]:["alloy.base.behaviour","dialog-events","messages",ZN.name(),"scroll-lock"]}})),HL=(e,t={})=>V(e,(e=>"menu"===e.type?(e=>{const o=V(e.items,(e=>{const o=be(t,e.name).getOr(Ms(!1));return{...e,storage:o}}));return{...e,items:o}})(e):e)),PL=e=>j(e,((e,t)=>"menu"===t.type?j(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),UL=(e,t)=>[Yr(Ks(),i_),e(Ok,((e,o,n,s)=>{Pl(bt(s.element)).fold(b,Vl),t.onClose(),o.onClose()})),e(_k,((e,t,o,n)=>{t.onCancel(e),Rr(n,Ok)})),jr(Mk,((e,o)=>t.onUnblock())),jr(Ak,((e,o)=>t.onBlock(o.event)))],WL=(e,t,o)=>{const n=(t,o)=>jr(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{ZN.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...UL(n,t),n(Ek,((e,t)=>t.onSubmit(e))),n(Ck,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(Tk,((e,t,n,s)=>{const r=()=>s.getSystem().isConnected()?Yp.focusIn(s):void 0,a=e=>At(e,"disabled")||Et(e,"aria-disabled").exists((e=>"true"===e)),i=bt(s.element),l=Pl(i);t.onAction(e,{name:n.name,value:n.value}),Pl(i).fold(r,(e=>{a(e)||l.exists((t=>tt(e,t)&&a(t)))?r():o().toOptional().filter((t=>!tt(t.element,e))).each(r)}))})),n(Dk,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Qr((t=>{const o=e();wu.setValue(t,o.getData())}))]},jL=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=P(o,(e=>"start"===e.align)),s=(e,t)=>uk.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:V(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},GL=(e,t,o)=>({dom:Jh('<div class="tox-dialog__footer"></div>'),components:[],behaviours:Ml([ZN.config({channel:`${m_}-${t}`,initialData:e,updateState:(e,t)=>{const n=V(t.buttons,(e=>{const t=Zh(((e,t)=>$T(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return A.some({lookupByName:t=>((e,t,o)=>G(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:jL})])}),$L=(e,t,o)=>uR.parts.footer(GL(e,t,o)),qL=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=Em.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return LO.getField(o,t).orThunk((()=>{const o=e.getFooter().bind((e=>ZN.getState(e).get()));return o.bind((e=>e.lookupByName(t)))}))}return A.none()},YL=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...wu.getValue(n),...ce(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=vn(r,t),i=((e,t)=>{const o=e.getRoot();return ZN.getState(o).get().map((e=>Kn(Xn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();wu.setValue(l,i),le(o,((e,t)=>{ve(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{qL(e,t).each(o?Um.enable:Um.disable)},focus:t=>{qL(e,t).each(ch.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Nr(t,Ak,{message:e})}))},unblock:()=>{n((e=>{Rr(e,Mk)}))},showTab:t=>{n((o=>{const n=e.getBody();ZN.getState(n).get().exists((e=>e.isTabPanel()))&&Em.getCurrent(n).each((e=>{uL.showTab(e,t)}))}))},redial:r=>{n((n=>{const a=e.getId(),i=t(r),l=HL(i.internalDialog.buttons,o);n.getSystem().broadcastOn([`${c_}-${a}`],i),n.getSystem().broadcastOn([`${d_}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${u_}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${m_}-${a}`],{...i.internalDialog,buttons:l}),s.setData(i.initialData)}))},close:()=>{n((e=>{Rr(e,Ok)}))},toggleFullscreen:e.toggleFullscreen};return s},XL=(e,t,o,n=!1,s)=>{const r=da("dialog"),a=da("dialog-label"),i=da("dialog-content"),l=e.internalDialog,c=Ms(l.size),d=NL(c.get()).toArray(),u=Zh(((e,t,o,n)=>uk.sketch({dom:Jh('<div class="tox-dialog__header"></div>'),components:[EL(e,t,A.some(o),n),AL(),TL(n)],containerBehaviours:Ml([KF.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>Si(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:l.title,draggable:!0},r,a,o.shared.providers)),m=Zh(((e,t,o,n,s)=>vL(e,t,A.some(o),n,s))({body:l.body,initialData:l.initialData},r,i,o,n)),g=HL(l.buttons),p=PL(g),h=Ce(0!==g.length,Zh(((e,t,o)=>GL(e,t,o))({buttons:g},r,o))),f=WL((()=>v),{onBlock:e=>{HM.block(b,((t,n)=>{const s=u.getOpt(b).map((e=>Gt(e.element)));return DL(e.message,n,o.shared.providers,s)}))},onUnblock:()=>{HM.unblock(b)},onClose:()=>t.closeWindow()},o.shared.getSink),b=di({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline",...d],attributes:{role:"dialog","aria-labelledby":a}},eventOrder:{[mr()]:[ZN.name(),Nl.name()],[gr()]:["execute-on-form"],[Cr()]:["reflecting","execute-on-form"]},behaviours:Ml([Yp.config({mode:"cyclic",onEscape:e=>(Rr(e,Ok),A.some(!0)),useTabstopAt:e=>!l_(e)&&("button"!==We(e)||"disabled"!==Tt(e,"disabled")),firstTabstop:1}),ZN.config({channel:`${c_}-${r}`,updateState:(e,t)=>(c.set(t.internalDialog.size),LL(t.internalDialog.size,e),s(),A.some(t)),initialData:e}),ch.config({}),sh("execute-on-form",f.concat([Jr(Ks(),((e,t)=>{Yp.focusIn(e)})),jr(Er(),((e,t)=>{e.getSystem().broadcastOn([p_],{newFocus:t.event.newFocus})}))])),HM.config({getRoot:()=>A.some(b)}),nh.config({}),KO({})]),components:[u.asSpec(),m.asSpec(),...h.map((e=>e.asSpec())).toArray()]}),v=YL({getId:x(r),getRoot:x(b),getFooter:()=>h.map((e=>e.get(b))),getBody:()=>m.get(b),getFormWrapper:()=>{const e=m.get(b);return Em.getCurrent(e).getOr(e)},toggleFullscreen:()=>{zL(b,c.get())}},t.redial,p);return{dialog:b,instanceApi:v}};var KL=tinymce.util.Tools.resolve("tinymce.util.URI");const JL=["insertContent","setContent","execCommand","close","block","unblock"],ZL=e=>a(e)&&-1!==JL.indexOf(e.mceAction),QL=(e,t,o,n)=>{const s=da("dialog"),i=ML(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[r_(A.none(),{dom:{tag:"iframe",attributes:{src:e.url}},behaviours:Ml([vk.config({}),ch.config({})])})]}],behaviours:Ml([Yp.config({mode:"acyclic",useTabstopAt:C(l_)})])};return uR.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?A.none():A.some($L({buttons:e},s,n)))),u=((e,t)=>{const o=(e,t)=>jr(e,((e,o)=>{n(e,((n,s)=>{t(x,n,o.event,e)}))})),n=(e,t)=>{ZN.getState(e).get().each((o=>{t(o,e)}))};return[...UL(o,t),o(Tk,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})(0,BL((()=>y),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new KL(e.url,{base_uri:new KL(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=rc(),v=[ZN.config({channel:`${c_}-${s}`,updateState:(e,t)=>A.some(t),initialData:e}),sh("messages",[Zr((()=>{const t=lc(ze(window),"message",(t=>{if(h.isSameOrigin(new KL(t.raw.origin))){const n=t.raw.data;ZL(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,x,n):(e=>!ZL(e)&&a(e)&&ve(e,"mceAction"))(n)&&e.onMessage(x,n)}}));b.set(t)})),Qr(b.clear)]),Nl.config({channels:{[g_]:{onReceive:(e,t)=>{wi(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],y=VL({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},u,n),x=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Nr(t,Ak,{message:e})}))},unblock:()=>{t((e=>{Rr(e,Mk)}))},close:()=>{t((e=>{Rr(e,Ok)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([g_],e)}))}}})(y);return{dialog:y,instanceApi:x}},ez=(e,t)=>Kn(Xn("data",t,e)),tz=e=>tk(e,".tox-alert-dialog")||tk(e,".tox-confirm-dialog"),oz=(e,t,o)=>t&&o?[]:[iM.config({contextual:{lazyContext:()=>A.some(Qo(ze(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"],lazyViewport:t=>QS(e,t.element).map((e=>({bounds:ek(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Kt(e.element).top})}))).getOrThunk((()=>({bounds:on(),optScrollEnv:A.none()})))})],nz=e=>{const t=e.editor,o=gv(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{uR.hide(l),n()},r=Zh($T({name:"close-alert",text:"OK",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"cancel",e)),a=SL(),i=wL(s,t.providers),l=di(_L({lazySink:()=>t.getSink(),header:xL(a,i),body:kL(o,t.providers),footer:A.some(CL(OL([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[jr(_k,s)],eventOrder:{}}));uR.show(l);const c=r.get(l);ch.focus(c)}}})(e.backstages.dialog),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{uR.hide(c),n(e)},r=Zh($T({name:"yes",text:"Yes",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"submit",e)),a=$T({name:"no",text:"No",primary:!1,buttonType:A.some("secondary"),align:"end",enabled:!0,icon:A.none()},"cancel",e),i=SL(),l=wL((()=>s(!1)),t.providers),c=di(_L({lazySink:()=>t.getSink(),header:xL(i,l),body:kL(o,t.providers),footer:A.some(CL(OL([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[jr(_k,(()=>s(!1))),jr(Ek,(()=>s(!0)))],eventOrder:{}}));uR.show(c);const d=r.get(c);ch.focus(d)}}})(e.backstages.dialog),r=(t,o)=>YN.open(((t,n,s)=>{const r=n,a=((e,t,o)=>{const n=da("dialog"),s=e.internalDialog,r=ML(s.title,n,o),a=Ms(s.size),i=NL(a.get()).toArray(),l=((e,t,o)=>{const n=vL(e,t,A.none(),o,!1);return uR.parts.body(n)})({body:s.body,initialData:s.initialData},n,o),c=HL(s.buttons),d=PL(c),u=Ce(0!==c.length,$L({buttons:c},n,o)),m=WL((()=>f),BL((()=>p),o.shared.providers,t),o.shared.getSink),g={id:n,header:r,body:l,footer:u,extraClasses:i,extraBehaviours:[ZN.config({channel:`${c_}-${n}`,updateState:(e,t)=>(a.set(t.internalDialog.size),LL(t.internalDialog.size,e),A.some(t)),initialData:e})],extraStyles:{}},p=VL(g,m,o),h={getId:x(n),getRoot:x(p),getBody:()=>uR.getBody(p),getFooter:()=>uR.getFooter(p),getFormWrapper:()=>{const e=uR.getBody(p);return Em.getCurrent(e).getOr(e)},toggleFullscreen:()=>{zL(p,a.get())}},f=YL(h,t.redial,d);return{dialog:p,instanceApi:f}})({dataValidator:s,initialData:r,internalDialog:t},{redial:YN.redial,closeWindow:()=>{uR.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return uR.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),t),a=(n,s,r,a)=>YN.open(((n,i,l)=>{const c=ez(i,l),d=ac(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{Yh.reposition(e),o&&u||iM.refresh(e)})),g=XL({dataValidator:l,initialData:c,internalDialog:n},{redial:YN.redial,closeWindow:()=>{d.on(Yh.hide),t.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},e.backstages.popup,a.ariaAttrs,m),p=di(Yh.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:a.persistent?{event:"doNotDismissYet"}:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:Ml([sh("window-manager-inline-events",[jr(_r(),((e,t)=>{Rr(g.dialog,_k)}))]),...oz(t,o,u)]),isExtraPart:(e,t)=>tz(t)}));return d.set(p),Yh.showWithinBounds(p,ui(g.dialog),{anchor:s},(()=>{const e=t.inline?St():ze(t.getContainer()),o=Qo(e);return A.some(o)})),o&&u||(iM.refresh(p),t.on("ResizeEditor",m)),g.instanceApi.setData(c),Yp.focusIn(g.dialog),g.instanceApi}),n),i=(o,n,s,r)=>YN.open(((o,a,i)=>{const l=ez(a,i),c=ac(),d=e.backstages.popup.shared.header.isPositionedAtTop(),u=()=>c.on((e=>{Yh.reposition(e),iM.refresh(e)})),m=XL({dataValidator:i,initialData:l,internalDialog:o},{redial:YN.redial,closeWindow:()=>{c.on(Yh.hide),t.off("ResizeEditor ScrollWindow ElementScroll",u),c.clear(),s(m.instanceApi)}},e.backstages.popup,r.ariaAttrs,u),g=di(Yh.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:r.persistent?{event:"doNotDismissYet"}:{},...d?{}:{fireRepositionEventInstead:{}},inlineBehaviours:Ml([sh("window-manager-inline-events",[jr(_r(),((e,t)=>{Rr(m.dialog,_k)}))]),iM.config({contextual:{lazyContext:()=>A.some(Qo(ze(t.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top","bottom"],lazyViewport:e=>QS(t,e.element).map((e=>({bounds:ek(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Kt(e.element).top})}))).getOrThunk((()=>({bounds:on(),optScrollEnv:A.none()})))})]),isExtraPart:(e,t)=>tz(t)}));return c.set(g),Yh.showWithinBounds(g,ui(m.dialog),{anchor:n},(()=>e.backstages.popup.shared.getSink().toOptional().bind((e=>{const o=QS(t,e.element).map((e=>ek(e))).getOr(on()),n=Qo(ze(t.getContentAreaContainer())),s=tn(n,o);return A.some(Zo(s.x,s.y,s.width,s.height-15))})))),iM.refresh(g),t.on("ResizeEditor ScrollWindow ElementScroll ResizeWindow",u),m.instanceApi.setData(l),Yp.focusIn(m.dialog),m.instanceApi}),o);return{open:(t,o,n)=>{if(!u(o)){if("toolbar"===o.inline)return a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o);if("bottom"===o.inline)return i(t,e.backstages.popup.shared.anchors.inlineBottomDialog(),n,o);if("cursor"===o.inline)return a(t,e.backstages.popup.shared.anchors.cursor(),n,o)}return r(t,n)},openUrl:(o,n)=>((o,n)=>YN.openUrl((o=>{const s=QL(o,{closeWindow:()=>{uR.hide(s.dialog),n(s.instanceApi)}},t,e.backstages.dialog);return uR.show(s.dialog),s.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{s.open(e,t)}}};nn.add("silver",(e=>{(e=>{hb(e),(e=>{const t=e.options.register,o=e=>f(e,r)?{value:zw(e),valid:!0}:{valid:!1,message:"Must be an array of strings."},n=e=>h(e)&&e>0?{value:e,valid:!0}:{valid:!1,message:"Must be a positive number."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:n,default:Uw(e)}),t("color_cols_foreground",{processor:n,default:Ww(e,Nw)}),t("color_cols_background",{processor:n,default:Ww(e,Lw)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:Hw}),t("color_default_background",{processor:"string",default:Hw})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:jI(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);let t=()=>on();const{dialogs:o,popups:n,renderUI:s}=iR(e,{getPopupSinkBounds:()=>t()});XS(e,n.backstage.shared);const a=nz({editor:e,backstages:{popup:n.backstage,dialog:o.backstage}});return{renderUI:()=>{const o=s();return QS(e,n.getMothership().element).each((e=>{t=()=>ek(e)})),o},getWindowManagerImpl:x(a),getNotificationManagerImpl:()=>((e,t,o)=>{const n=t.backstage.shared,s=()=>{const t=Qo(ze(e.getContentAreaContainer())),o=on(),n=ol(o.x,t.x,t.right),s=ol(o.y,t.y,t.bottom),r=Math.max(t.right,o.right),a=Math.max(t.bottom,o.bottom);return A.some(Zo(n,s,r-n,a-s))};return{open:(t,r)=>{const a=()=>{r(),Yh.hide(l)},i=di(ib.sketch({text:t.text,level:R(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,onAction:a,iconProvider:n.providers.icons,translationProvider:n.providers.translate})),l=di(Yh.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:n.getSink,fireDismissalEventInstead:{},...n.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}}}));o.add(l),h(t.timeout)&&t.timeout>0&&Xh.setEditorTimeout(e,(()=>{a()}),t.timeout);const c={close:a,reposition:()=>{const t=ui(i),o={maxHeightFunction:fc()},r=e.notificationManager.getNotifications();if(r[0]===c){const e={...n.anchors.banner(),overrides:o};Yh.showWithinBounds(l,t,{anchor:e},s)}else F(r,c).each((e=>{const n=r[e-1].getEl(),a={type:"node",root:St(),node:A.some(ze(n)),overrides:o,layouts:{onRtl:()=>[fl],onLtr:()=>[fl]}};Yh.showWithinBounds(l,t,{anchor:a},s)}))},text:e=>{ib.updateText(i,e)},settings:t,getEl:()=>i.element.dom,progressBar:{value:e=>{ib.updateProgress(i,e)}}};return c},close:e=>{e.close()},getArgs:e=>e.settings}})(e,{backstage:n.backstage},n.getMothership())}}))}();