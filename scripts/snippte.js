const readline = require('readline');
const path = require('path');
const fs = require('fs');

const filepath = path.join(__dirname, '../src/style/global/var.less');
const input = fs.createReadStream(filepath);
const rl = readline.createInterface({
  input,
});
let result = '{';
rl.on('line', async (line) => {
  const desc = /\/\/.*/.exec(line) ? /\/\/(.*)/.exec(line)[1] : 'empty';
  const prefix = /(@[^:]*)\s*:/.exec(line) ? /(@[^:]*)\s*:/.exec(line)[1].replace(' ', '') : '';
  if (!prefix) return;
  result += `
    "${prefix}": {
      "prefix": "${prefix}",
      "body": [
        "${prefix}"
      ],
      "description": "${desc}"
    },
  `;
});

rl.on('close', (line) => {
  fs.writeFile(`${__dirname}/../.vscode/style.code-snippets`, `${result}}`, (err) => {
    if (err) {
      return console.error(err);
    }
    console.log('snippte 已经生成！');
  });
});
