{
  // -----------统一配置--------------
  // 原则：不允许有和 prettier 格式化冲突的配置；一般为会对文件进行重写的插件和配置
  // 是否在保存时格式化文件, 是，保证 prettier 起作用
  // 默认使用prettier格式化支持的文件
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.formatOnPaste": false,
  "editor.formatOnType": false,
  "editor.codeActionsOnSave": {
    "source.fixAll": "never"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // 默认文件编码 utf-8
  "files.encoding": "utf8",
  // 关闭 editor 内置样式校验，stylelint 插件建议
  "css.validate": false,
  "less.validate": false,
  "scss.validate": false,
  // 关闭 JavaScript 验证
  "javascript.validate.enable": false,
  // eslint 校验的语言列表
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "html",
    {
      "language": "vue",
      // 额外配置自动修复
      "autoFix": true
    },
    {
      "language": "typescript",
      // 额外配置自动修复
      "autoFix": true
    },
    {
      "language": "typescriptreact",
      // 额外配置自动修复
      "autoFix": true
    }
  ],
  // vetur 禁用格式化和校验
  "vetur.format.enable": false,
  "vetur.validation.script": false,
  "vetur.validation.style": false,
  "vetur.validation.template": false,
  // 开启 stylelint 校验
  "stylelint.enable": true
  // ---------统一配置结束----------------
}
