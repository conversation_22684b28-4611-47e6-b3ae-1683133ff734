# 丁香通管理后台 & 用户中心 v2

## 功能介绍

丁香通用户中心

[测试环境地址](https://www.biomart.net/usercenter/v2/index.htm)
[正式环境地址](https://www.biomart.cn/usercenter/v2/index.htm)

## 技术栈

- [React](https://zh-hans.reactjs.org/)
- [Redux](https://redux.js.org)
- [React Router](https://reacttraining.com)
- [Ant Design](https://ant.design)
- [编辑器 TinyMCE](https://www.tiny.cloud/docs/tinymce/6/react-cloud/)

## 代码目录

```
├── charles_config // 抓包代理配置文件
│   └── rewrite_config.xml
├── config-overrides.js
├── proxy.js
├── src
│   ├── App.js // 主组件
│   ├── actions // api接口
│   ├── api // api接口
│   ├── assets // 静态资源
│   ├── components // 公共组件
│   ├── configs // 公共常量
│   ├── containers // 业务组件
│   ├── index.tsx // 主入口
│   ├── layouts // 布局组件
│   ├── router // 页面路由
│   ├── router.js // 页面路由
│   ├── store // 后端接口及数据仓库
│   ├── style // 公共样式
│   ├── types // ts申明
│   └── utils // 公共方法
├── tsconfig.json
├── tsconfig.paths.json
└── webpack.config-overrides.js
```

## 本地开发

1. 安装依赖：npm install
2. 启动服务：npm run start
3. 导入代理配置，开启 Charles（如下步骤）
4. 访问测试环境地址即可本地调试开发

按照本地开发的步骤，打包本地资源并启动本地静态资源服务器之后，利用 Charles 代理抓包调试。参考操作：

1. 下载安装 Charles
2. 选择菜单栏 `Tools => Rewrite`
3. 直接导入 `charles_config/rewrite_config.xml`

## 部署发布

### 测试环境发布

1. 当前开发分支合并 feat-example 到远程 dynamic 分支
2. 在[jarvis](https://jarvis.dxy.net/tasks?appId=47)丁香通-商家后台 V2 下查看对应发布进度及日志
3. 代码发布成功后，在[丁香通管理后台](https://www.biomart.net/admin/guessoneguess.do)测试环境点击【刷新时间戳】

### 生产环境发布

1. 当前开发分支 feat-example 合并到远程 master 分支
2. 可以在[jarvis](https://jarvis.dxy.net/tasks?appId=47)丁香通-商家后台 V2 下查看对应发布进度及日志
3. 代码发布成功后，在[丁香通管理后台](https://www.biomart.cn/admin/guessoneguess.do)生产环境点击【刷新时间戳】

## 其他说明

### 分支规则

[开发分支命名约定规则](https://wiki.dxy.net/pages/viewpage.action?pageId=227123194)

### 组件

#### 1.组件编写步骤

##### 1) 搜索组件库（我们项目中的和使用到的第三方的组件库），看是否有实现

##### 2) 判断组件的属性：

- 通用基础组件：和业务无关，可以放在其他项目当中使用，无副作用 存放路径：src/components/common
- 通用业务组件：业务相关组件，具有较强通用性 存放路径：src/components/special
- 局部组件： 局部业务组件，遵循就近原则 存放路径：{调用组件地址}/components

##### 3）组件当前只使用 1 次，遵循就近原则，存放在离使用方较近的位置，参考【局部业务组件】。大于 1 次使用才允许抽离到"src/components"目录

##### 4) 组内其他成员 Code Review（须全部通过），通用局部组件无须 Review

#### 2.组件文档

待做

### 样式

#### 1. 样式结构

- 基础全局样式 路径：src/style/base
- 全局工具样式（方法，变量等） 路径：src/style/global
- antd 组件覆盖样式 路径：src/style/antdComponent
- 组件样式 路径：当前组件路径/style.less

#### 2. 样式规范

##### 1）规范来源：[丁香通通用规范](https://lanhuapp.com/web/#/item/project/board?pid=026f9d26-bc58-4af6-b90c-58ca761d1115) [商家后台结构规范](https://lanhuapp.com/web/#/item/project/board?pid=075878f0-04a3-4328-92d8-46b7ec7ca1c4)

##### 2）样式编写注意事项

- 务必使用 less 变量编写（参考:src/style/global/var.less）
- 目录 src/style/global 下方法和变量都是全局的，使用时不需要引入文件
- snippet 提示依赖 src/style/global/var.less 文件
- 样式编写过程中如发现设计稿的内容和规范不一致，需要通知设计同学修改设计稿，或者修改样式规范，同步到全局工具样式文件内

### 视图

#### 1. 结构

##### 1）存放目录：src/containers

##### 2）模块化：同模块下的文件应该合并到一个文件目录

例如：

```
-containers
 -activity
 -activityDetail
 -activityEdit

改为

-containers
 -activity
  -index
  -edit
  -detail
```

### 注意事项

2019-11-21

- 目前新版商家后台与旧版商家后台并存
- 为保证旧版商家后台的公共区域与新版保持一致，旧版后台引用了 biomart_cms_layout 项目，该项目为 biomart_cms_v2 复制拆分的 Header 与 SideBar 部分
- 如果侧边栏、头部有修改（比如新增一个导航），要记得同步到 biomart_cms_layout 项目
- 用户个人中心、消息中心，暂时不做调整。侧边栏、头部也不同步。（产品计划将个人中心、消息中心做成前台页面效果，所以暂时不动）
