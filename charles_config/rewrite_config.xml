<?xml version='1.0' encoding='UTF-8' ?>
<?charles serialisation-version='2.0' ?>
<rewriteSet-array>
  <rewriteSet>
    <active>true</active>
    <name>biomart_cms_v2</name>
    <hosts>
      <locationPatterns/>
    </hosts>
    <rules>
      <rewriteRule>
        <active>true</active>
        <ruleType>6</ruleType>
        <matchValue>https?\:\/\/assets\.dxycdn\.com\/gitrepo\/(biomart_cms_v2|biomart_cms_v2_dynamic|biomart_cms_v2_develop|biomart_cms_v2_preview)\/dist\/static\/</matchValue>
        <matchHeaderRegex>false</matchHeaderRegex>
        <matchValueRegex>true</matchValueRegex>
        <matchRequest>false</matchRequest>
        <matchResponse>false</matchResponse>
        <newValue>http://localhost:3200/static/</newValue>
        <newHeaderRegex>false</newHeaderRegex>
        <newValueRegex>false</newValueRegex>
        <matchWholeValue>false</matchWholeValue>
        <caseSensitive>false</caseSensitive>
        <replaceType>1</replaceType>
      </rewriteRule>
      <rewriteRule>
        <active>true</active>
        <ruleType>6</ruleType>
        <matchValue>https?\:\/\/a1\.dxycdn\.com\/gitrepo\/(biomart_cms_v2|biomart_cms_v2_dynamic|biomart_cms_v2_develop|biomart_cms_v2_preview)\/dist\/static\/</matchValue>
        <matchHeaderRegex>false</matchHeaderRegex>
        <matchValueRegex>true</matchValueRegex>
        <matchRequest>false</matchRequest>
        <matchResponse>false</matchResponse>
        <newValue>http://localhost:3200/static/</newValue>
        <newHeaderRegex>false</newHeaderRegex>
        <newValueRegex>false</newValueRegex>
        <matchWholeValue>false</matchWholeValue>
        <caseSensitive>false</caseSensitive>
        <replaceType>1</replaceType>
      </rewriteRule>
      <rewriteRule>
        <active>true</active>
        <ruleType>6</ruleType>
        <matchValue>http?\:\/\/localhost:3200\/static\/</matchValue>
        <matchHeaderRegex>false</matchHeaderRegex>
        <matchValueRegex>true</matchValueRegex>
        <matchRequest>false</matchRequest>
        <matchResponse>false</matchResponse>
        <newValue>http://***************:3200/static/</newValue>
        <newHeaderRegex>false</newHeaderRegex>
        <newValueRegex>false</newValueRegex>
        <matchWholeValue>false</matchWholeValue>
        <caseSensitive>false</caseSensitive>
        <replaceType>1</replaceType>
      </rewriteRule>
    </rules>
  </rewriteSet>
</rewriteSet-array>