import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Radio, 
  Select, 
  Upload, 
  Icon, 
  message, 
  Table,
  Progress,
  Modal
} from 'antd';
import UploadAttachment from 'src/components/common/baseUploadAttachment';
import CategoryTreeSelect from 'src/components/product-category/category-tree-select';

const { Option } = Select;

const BatchUploadForm = ({ form }) => {
  const { getFieldDecorator, validateFields, getFieldValue, setFieldsValue } = form;
  
  // 状态管理
  const [submitting, setSubmitting] = useState(false);
  const [customCategories, setCustomCategories] = useState([]);
  const [uploadTasks, setUploadTasks] = useState([]);
  const [titlePicFileList, setTitlePicFileList] = useState([]);

  // 获取自定义分类
  const getCustomCategories = async () => {
    const userName = getFieldValue('owner');
    if (!userName || userName.trim().length === 0) {
      message.error('请输入信息所有人用户名！');
      return;
    }
    
    try {
      // 这里应该调用实际的API
      // const response = await adminMemberApi.getCustomCategories({ userName });
      // setCustomCategories(response.results || []);
      message.success('获取自定义分类成功');
    } catch (error) {
      message.error('获取自定义分类失败');
    }
  };

  // 处理Excel文件上传
  const handleExcelUpload = (fileInfo) => {
    console.log('Excel文件上传:', fileInfo);
  };

  // 处理标题图片上传
  const beforePicUpload = (file) => {
    const isLt20M = file.size / 1024 / 1024 < 20;
    if (!isLt20M) {
      message.error('上传文件大小不能超过 20MB!');
    }
    return isLt20M;
  };

  const handlePicChange = (info) => {
    let newFileList = [...info.fileList];
    newFileList = newFileList.slice(-1);
    
    newFileList = newFileList.map(file => {
      if (file.response) {
        if (file.response.success) {
          message.success('图片上传成功');
        } else {
          message.error(file.response.message || '图片上传失败');
          return null;
        }
      }
      return file;
    }).filter(file => file !== null);

    setTitlePicFileList(newFileList);
  };

  // 表单提交
  const handleSubmit = (e) => {
    e.preventDefault();
    validateFields((err, values) => {
      if (err) {
        return;
      }
      
      setSubmitting(true);
      console.log('提交数据:', values);
      
      // 这里应该调用实际的上传API
      setTimeout(() => {
        message.success('上传任务已提交');
        setSubmitting(false);
      }, 2000);
    });
  };

  return (
    <div style={{ padding: '20px', backgroundColor: '#f5f5f5' }}>
      {/* 页面标题 */}
      <div style={{ 
        backgroundColor: '#fff', 
        padding: '10px 20px', 
        marginBottom: '20px',
        borderLeft: '4px solid #1890ff'
      }}>
        <span style={{ fontSize: '16px', fontWeight: 'bold' }}>信息批量导入</span>
        <span style={{ float: 'right', color: '#666' }}>
          您当前的位置：产品信息管理 > 信息批量导入
        </span>
      </div>

      {/* 主表单 */}
      <Card title="产品信息批量上传" style={{ marginBottom: '20px' }}>
        <Form onSubmit={handleSubmit} layout="horizontal" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          
          {/* Excel文件上传 */}
          <Form.Item label="*上传excel批量发布表单：" required>
            <UploadAttachment
              maxSize={20}
              limit={1}
              accept={['.xls', '.xlsx']}
              onChange={handleExcelUpload}
              tips="注意:表单容量不超过20M，目前只支持xls, xlsx文件，不支持多sheet。"
            />
          </Form.Item>

          {/* 信息分类 */}
          <Form.Item label="*上传信息分类：" required>
            {getFieldDecorator('category', {
              rules: [{ required: true, message: '请选择信息分类' }]
            })(
              <CategoryTreeSelect 
                placeholder="请选择信息分类"
                style={{ width: '100%' }}
              />
            )}
            <div style={{ marginTop: '8px', color: '#666' }}>
              您选择的类别(ctrl多选)：<span id="categorySelected"></span>
            </div>
          </Form.Item>

          {/* 信息所有人用户名 */}
          <Form.Item label="*信息所有人用户名：" required>
            {getFieldDecorator('owner', {
              rules: [
                { required: true, message: '请输入信息所有人用户名' },
                { max: 50, message: '用户名长度不能超过50个字符' }
              ]
            })(
              <Input placeholder="请输入信息所有人用户名" style={{ width: '300px' }} />
            )}
          </Form.Item>

          {/* 货币种类 */}
          <Form.Item label="*货币种类：" required>
            {getFieldDecorator('unit', {
              rules: [{ required: true, message: '请选择货币种类' }]
            })(
              <Radio.Group>
                <Radio value="$">美元</Radio>
                <Radio value="￥">人民币</Radio>
              </Radio.Group>
            )}
          </Form.Item>

          {/* 自定义分类 */}
          <Form.Item label="自定义分类：">
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              {getFieldDecorator('customCateId')(
                <Select placeholder="选择自定义分类" style={{ width: '200px' }}>
                  <Option value="">选择自定义分类</Option>
                  {customCategories.map(item => (
                    <Option key={item.id} value={item.id}>{item.value}</Option>
                  ))}
                </Select>
              )}
              <Button onClick={getCustomCategories}>点击获取自定义分类</Button>
            </div>
          </Form.Item>

          {/* 品牌商标 */}
          <Form.Item label="品牌商标：">
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              {getFieldDecorator('trademark')(
                <Input placeholder="请输入品牌商标" style={{ width: '300px' }} />
              )}
              <Button>选择品牌</Button>
            </div>
            <div style={{ marginTop: '8px', color: '#666', fontSize: '12px' }}>
              （标准名称，便于用户检索。若填写将覆盖产品信息里的品牌商标内容[重要]）
            </div>
          </Form.Item>

          {/* 确定表单默认图片 */}
          <Form.Item label="确定表单默认图片">
            <div>
              <div style={{ marginBottom: '10px' }}>标题图片：</div>
              <Upload
                name="excel_pic"
                listType="picture-card"
                fileList={titlePicFileList}
                beforeUpload={beforePicUpload}
                onChange={handlePicChange}
                onRemove={() => setTitlePicFileList([])}
                showUploadList={{ showPreviewIcon: false }}
              >
                {titlePicFileList.length === 0 && (
                  <>
                    <Icon type="plus" />
                    <div className="ant-upload-text">上传</div>
                  </>
                )}
              </Upload>
            </div>
          </Form.Item>

          {/* 处理模式 */}
          <Form.Item label="处理模式">
            {getFieldDecorator('process', {
              initialValue: '0'
            })(
              <Radio.Group>
                <Radio value="0">常规模式</Radio>
                <Radio value="1">忽略详细描述(更新时生效)</Radio>
              </Radio.Group>
            )}
          </Form.Item>

          {/* 操作类型和提交按钮 */}
          <Form.Item wrapperCol={{ offset: 6, span: 18 }}>
            <div style={{ marginBottom: '16px' }}>
              {getFieldDecorator('type', {
                rules: [{ required: true, message: '请选择操作类型' }]
              })(
                <Radio.Group>
                  <Radio value="0">单纯新增产品</Radio>
                  <Radio value="1">单纯更新产品</Radio>
                  <Radio value="2">新增产品，遇存在数据时更新产品</Radio>
                  <Radio value="3">单纯新增产品，兼容处理多行形式的多规格多价格</Radio>
                </Radio.Group>
              )}
            </div>
            <div style={{ color: 'red', marginBottom: '16px', fontSize: '12px' }}>
              （使用"SPU（货号+品牌）"来判断是否是同一条数据）
            </div>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={submitting}
              size="large"
            >
              {submitting ? '上传中...' : '上传'}
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Form.create()(BatchUploadForm);
