/* 由 stylus 重构为标准 CSS，适用于 React 项目 */

body {
  -webkit-print-color-adjust: exact;
  background-color: #eee;
  font-family: 'PingFang SC', sans-serif;
}

.shop-traffic-chart {
  height: 5rem;
}

.report-container {
  font-family: 'PingFang SC', sans-serif;
  min-height: 100vh;
}

.report-container .page-wrap {
  width: 19.2rem;
  height: 10.8rem;
  background-color: #fff;
  margin: 0 auto;
}

.report-container .page-wrap.pageWrap-shop-traffic {
  height: 12rem;
}

.report-container .page-wrap:not(:nth-last-child(2)) {
  margin-bottom: 0.16rem;
}

.report-container .page {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 10.8rem;
  padding: 0.8rem;
}

.report-container .page.page-shop-traffic {
  height: 11.5rem;
}

.report-container .page-source {
  position: absolute;
  top: 0.88rem;
  right: 0.8rem;
  font-size: 0.24rem;
  color: #999;
}

.report-container .page-source>img {
  width: 0.32rem;
  margin-right: 0.08rem;
  vertical-align: middle;
}

.report-container .page-index-wrap {
  position: absolute;
  left: 0.8rem;
  top: 0;
  display: flex;
}

.report-container .page-index-wrap .page-index {
  width: 0.64rem;
  height: 1.4rem;
  font-size: 0.36rem;
  line-height: 0.43rem;
  color: #fff;
  padding-top: 0.89rem;
  background-color: #3296FA;
  text-align: center;
  box-sizing: border-box;
}

.report-container .page-index-wrap .page-index-title {
  color: #3296FA;
  font-size: 0.36rem;
  letter-spacing: 0.02rem;
  line-height: 0.5rem;
  font-weight: 500;
  padding: 0.8rem 0 0 0.32rem;
  box-sizing: border-box;
}

.report-container .custom-table-wrap {
  width: 11.66rem;
  margin: 1.4rem auto 0 auto;
  font-size: 0.16rem;
  border: 0.01rem solid #ddd;
  table-layout: fixed;
}

.report-container .custom-table-wrap thead th {
  background-color: #3296FA;
  color: #fff;
  font-size: 0.16rem;
  height: 0.4rem;
  line-height: 0.4rem;
  text-align: left;
  padding: 0 0.16rem;
  font-weight: normal;
}

.report-container .custom-table-wrap thead th:not(:last-child) {
  border-right: 1px solid #fff;
}

.report-container .custom-table-wrap tbody tr:nth-child(odd) {
  background-color: #f5f4f9;
}

.report-container .custom-table-wrap tbody tr td {
  color: #666;
  font-size: 0.16rem;
  line-height: 0.4rem;
  height: 0.4rem;
  padding: 0 0.16rem;
}

.report-container .custom-table-wrap .is-center {
  text-align: center;
}

.report-container .custom-table-wrap .ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.report-container .custom-table-wrap .placeholder-text {
  visibility: hidden;
  line-height: 0.4rem;
  font-size: 0.16rem;
}

.report-container .custom-table-wrap .rank-wrap {
  position: relative;
  height: 0.22rem;
}

.report-container .custom-table-wrap .rank-wrap .rank {
  position: absolute;
  left: 50%;
  top: -0.04rem;
  background-image: url('https://img1.dxycdn.com/2020/0212/921/3396241687096862688-2.png');
  background-repeat: no-repeat;
  width: 0.24rem;
  height: 0.3rem;
  background-size: 0.72rem;
  transform: translateX(-50%);
}

.report-container .custom-table-wrap .rank-wrap .rank-1 {
  background-position: 0 0;
}

.report-container .custom-table-wrap .rank-wrap .rank-2 {
  background-position: -0.24rem 0;
}

.report-container .custom-table-wrap .rank-wrap .rank-3 {
  background-position: -0.48rem 0;
}

.report-container .page-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.report-container .page-cover {
  background-image: url('https://img1.dxycdn.com/2020/0211/280/3396051525272200488-2.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  text-align: center;
  padding-top: 3.77rem;
  color: #fff;
  box-sizing: border-box;
}

.report-container .page-cover .agency-name,
.report-container .page-cover .report-title {
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.26rem;
}

.report-container .page-cover .report-date-range {
  display: inline-block;
  margin-top: 0.32rem;
  font-size: 0.36rem;
  font-weight: 400;
  line-height: 0.5rem;
  padding: 0 0.4rem;
  border: 1px solid rgba(255, 255, 255, 1);
  box-sizing: border-box;
}

.report-container .page-cover .report-create-date {
  margin-top: 2.76rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.24rem;
  font-weight: 400;
}

.report-container .page-directory {
  background: linear-gradient(to right, rgba(245, 244, 249, 1), rgba(245, 244, 249, 1) 71.4%, white 71.4%, white 100%);
}

.report-container .page-directory .description {
  position: absolute;
  left: 0.8rem;
  top: 2.28rem;
  width: 10.14rem;
}

.report-container .page-directory .description .block:not(:last-child) {
  margin-bottom: 1rem;
}

.report-container .page-directory .description .block-title {
  font-size: 0.36rem;
  line-height: 0.5rem;
  letter-spacing: 0.02rem;
  color: #333;
  font-weight: 600;
  margin-bottom: 0.29rem;
}

.report-container .page-directory .description .block-title::before {
  content: '';
  display: block;
  width: 0.24rem;
  height: 0.16rem;
  background-color: #3296FA;
  margin-bottom: 0.16rem;
}

.report-container .page-directory .description .block-content {
  font-size: 0.28rem;
  line-height: 0.6rem;
  color: #666;
  font-weight: 400;
}

.report-container .page-directory .directory {
  position: absolute;
  left: 13.06rem;
  top: 3.06rem;
}

.report-container .page-directory .directory .section {
  display: flex;
}

.report-container .page-directory .directory .section:not(:last-child) {
  margin-bottom: 0.8rem;
}

.report-container .page-directory .directory .section-index {
  width: 1.2rem;
  height: 1.2rem;
  background-color: #3296FA;
  font-size: 0.7rem;
  line-height: 1.2rem;
  text-align: center;
  color: #fff;
}

.report-container .page-directory .directory .section-title {
  position: relative;
  z-index: 1;
  color: #3296FA;
  margin: 0.03rem 0 0 0.48rem;
  font-weight: 500;
  line-height: 0.78rem;
  font-size: 0.56rem;
}

.report-container .page-directory .directory .section-subtitle {
  position: relative;
  z-index: 0;
  margin: -0.39rem 0 0 0.48rem;
  font-size: 0.56rem;
  font-weight: 500;
  color: rgba(232, 232, 232, 1);
  line-height: 0.78rem;
}

.report-container .page-section .section-content-wrap {
  position: absolute;
  top: 5.61rem;
  left: 0.8rem;
}

.report-container .page-section .section-content-wrap .section-content {
  position: relative;
  width: 17.6rem;
  height: 4.39rem;
  background-color: rgba(50, 150, 250, 0.1);
}

.report-container .page-section .section-content-wrap .section-content::after {
  content: '';
  display: block;
  width: 6.68rem;
  height: 0.04rem;
  background-color: #ddd;
  position: absolute;
  left: 0.64rem;
  bottom: 0.64rem;
}

.report-container .page-section .section-content-wrap .section-content .section-title {
  color: #3296FA;
  font-size: 0.56rem;
  line-height: 0.78rem;
  font-weight: 500;
  padding: 0.64rem 0 0 0.64rem;
  box-sizing: border-box;
}

.report-container .page-section .section-content-wrap .section-content .section-subtitle {
  color: #999;
  font-size: 0.26rem;
  line-height: 0.37rem;
  font-weight: 600;
  padding: 0.16rem 0 0 0.64rem;
  box-sizing: border-box;
}

.report-container .page-section .section-content-wrap .section-index {
  position: absolute;
  right: 0.87rem;
  top: -1.84rem;
  background-image: url('https://img1.dxycdn.com/2020/0211/377/3396057149531834825-2.png');
  background-repeat: no-repeat;
  width: 2.81rem;
  height: 2.81rem;
  background-size: 8.43rem 2.81rem;
}

.report-container .page-section .section-content-wrap .section-index.index-1 {
  background-position: 0 0;
}

.report-container .page-section .section-content-wrap .section-index.index-2 {
  background-position: -2.81rem 0;
}

.report-container .page-section .section-content-wrap .section-index.index-3 {
  background-position: -5.62rem 0;
}

.report-container .page-data-overview .page-main {
  margin-top: 2.32rem;
}

.report-container .page-data-overview .data-overview-wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 8rem;
}

.report-container .page-data-overview .data-item {
  width: 50%;
  display: flex;
  margin-bottom: 0.48rem;
}

.report-container .page-data-overview .data-item .item-icon-wrap {
  font-size: 0;
  display: flex;
  align-items: center;
}

.report-container .page-data-overview .data-item .item-icon-wrap img {
  width: 0.48rem;
}

.report-container .page-data-overview .data-item .item-content {
  flex: 1;
  margin-left: 0.22rem;
}

.report-container .page-data-overview .data-item .item-content .content {
  color: #3296FA;
  font-size: 0.4rem;
  font-weight: 500;
  line-height: 0.56rem;
}

.report-container .page-data-overview .data-item .item-content .label {
  color: #999;
  line-height: 0.37rem;
  font-size: 0.26rem;
}

.report-container .page-data-overview .shop-preview {
  position: absolute;
  left: 8.86rem;
  top: 3.27rem;
  width: 9.54rem;
  height: 5.49rem;
  background-image: url('https://img1.dxycdn.com/2020/0211/698/3396060398674936458-2.png');
  background-repeat: no-repeat;
  background-size: 100%;
}

.report-container .page-data-overview .shop-preview .shop-image-wrap {
  position: relative;
  margin: 0.32rem 0 0 1.12rem;
  width: 7.27rem;
  height: 4.55rem;
  overflow: hidden;
}

.report-container .page-data-overview .shop-preview .shop-image-wrap img {
  max-width: 100%;
  max-height: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.report-container .page-shop-traffic .chart-snapshot-wrap,
.report-container .page-shop-traffic .shop-traffic-chart {
  width: 10rem;
  margin: 1.2rem auto 0 auto;
}

.report-container .page-shop-traffic .chart-snapshot-wrap {
  display: none;
}

.report-container .page-shop-traffic .chart-snapshot-wrap img {
  width: 100%;
}

.report-container .page-shop-traffic .shop-traffic-legend {
  position: absolute;
  top: 1.2rem;
  left: 50%;
}

.report-container .page-shop-traffic .shop-traffic-legend p span {
  display: inline-block;
  width: 18px;
  height: 2px;
  margin: 0 5px 3px 0;
}

.report-container .page-shop-traffic .shop-traffic-legend p:first-child span {
  background-color: #f00;
}

.report-container .page-shop-traffic .shop-traffic-legend p:last-child span {
  background-color: #3296FA;
}

.report-container .page-shop-traffic-source .chart-snapshot-wrap,
.report-container .page-shop-traffic-source .shop-traffic-source-chart {
  width: 6rem;
  height: 5rem;
}

.report-container .page-shop-traffic-source .chart-snapshot-wrap {
  display: none;
}

.report-container .page-shop-traffic-source .chart-snapshot-wrap img {
  width: 100%;
}

.report-container .page-shop-inquiry .chart-snapshot-wrap,
.report-container .page-shop-inquiry .shop-inquiry-chart {
  width: 6rem;
  height: 5rem;
}

.report-container .page-shop-inquiry .chart-snapshot-wrap {
  display: none;
}

.report-container .page-shop-inquiry .chart-snapshot-wrap img {
  width: 100%;
}

.report-container .page-shop-traffic .data-summary,
.report-container .page-activity .data-summary,
.report-container .page-product-inquiry .data-summary,
.report-container .page-product-score .data-summary,
.report-container .page-article .data-summary {
  background-color: #FAFAFA;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  height: 3.65rem;
  padding: 0.56rem 1rem;
  box-sizing: border-box;
}

.report-container .page-shop-traffic .data-summary .block,
.report-container .page-activity .data-summary .block,
.report-container .page-product-inquiry .data-summary .block,
.report-container .page-product-score .data-summary .block,
.report-container .page-article .data-summary .block {
  margin-bottom: 0.48rem;
  display: flex;
  align-items: center;
  height: 0.99rem;
}

.report-container .page-shop-traffic .data-summary .block .label,
.report-container .page-activity .data-summary .block .label,
.report-container .page-product-inquiry .data-summary .block .label,
.report-container .page-product-score .data-summary .block .label,
.report-container .page-article .data-summary .block .label {
  width: 0.8rem;
  height: 0.56rem;
  color: #fff;
  font-size: 0.24rem;
  line-height: 0.56rem;
  text-align: center;
}

.report-container .page-shop-traffic .data-summary .block .content,
.report-container .page-activity .data-summary .block .content,
.report-container .page-product-inquiry .data-summary .block .content,
.report-container .page-product-score .data-summary .block .content,
.report-container .page-article .data-summary .block .content {
  margin-left: 0.32rem;
  color: #666;
  font-size: 0.24rem;
  line-height: 0.33rem;
  max-height: 0.99rem;
  overflow: hidden;
  flex: 1;
  word-break: break-all;
}

.report-container .page-shop-traffic .data-summary .block.description .label,
.report-container .page-activity .data-summary .block.description .label,
.report-container .page-product-inquiry .data-summary .block.description .label,
.report-container .page-product-score .data-summary .block.description .label,
.report-container .page-article .data-summary .block.description .label {
  background-color: #3296FA;
}

.report-container .page-shop-traffic .data-summary .block.suggestion .label,
.report-container .page-activity .data-summary .block.suggestion .label,
.report-container .page-product-inquiry .data-summary .block.suggestion .label,
.report-container .page-product-score .data-summary .block.suggestion .label,
.report-container .page-article .data-summary .block.suggestion .label {
  background-color: #FFAF27;
}

.report-container .page-shop-traffic-source .page-main,
.report-container .page-shop-inquiry .page-main {
  display: flex;
  margin-top: 1.5rem;
}

.report-container .page-shop-traffic-source .data-summary,
.report-container .page-shop-inquiry .data-summary {
  flex: 1;
  margin-left: -0.8rem;
  padding: 0.58rem 0.8rem;
  background-color: #fafafa;
  box-sizing: border-box;
}

.report-container .page-shop-traffic-source .data-summary .block:not(:last-child),
.report-container .page-shop-inquiry .data-summary .block:not(:last-child) {
  margin-bottom: 0.5rem;
}

.report-container .page-shop-traffic-source .data-summary .block .label,
.report-container .page-shop-inquiry .data-summary .block .label {
  width: 0.8rem;
  height: 0.56rem;
  color: #fff;
  font-size: 0.24rem;
  line-height: 0.56rem;
  text-align: center;
}

.report-container .page-shop-traffic-source .data-summary .block .content,
.report-container .page-shop-inquiry .data-summary .block .content {
  margin-top: 0.32rem;
  color: #666;
  font-size: 0.24rem;
  line-height: 0.33rem;
  height: 1.65rem;
  word-break: break-all;
}

.report-container .page-shop-traffic-source .data-summary .block.description .label,
.report-container .page-shop-inquiry .data-summary .block.description .label {
  background-color: #3296FA;
}

.report-container .page-shop-traffic-source .data-summary .block.suggestion .label,
.report-container .page-shop-inquiry .data-summary .block.suggestion .label {
  background-color: #FFAF27;
}

.report-container .page-product-inquiry .table-wrap {
  display: flex;
}

.report-container .page-product-inquiry .table-wrap .el-table {
  flex: none;
}

.report-container .page-product-inquiry .table-wrap .custom-table-wrap {
  width: 8.1rem;
}

.report-container .page-product-inquiry .table-wrap .custom-table-wrap:not(:last-child) {
  margin-right: 1rem;
}

.report-container .page-product-score .product-average-score-wrap {
  margin: 1.64rem auto 0 auto;
  position: relative;
  width: 3.6rem;
  height: 3.65rem;
  z-index: 1;
}

.report-container .page-product-score .product-average-score-wrap::after {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  content: '';
  display: block;
  width: 4.4rem;
  height: 4.4rem;
  border-radius: 50%;
  background-color: #EAF4FE;
  z-index: -1;
}

.report-container .page-product-score .product-average-score-wrap::before {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  content: '';
  display: block;
  width: 3.65rem;
  height: 3.65rem;
  border-radius: 50%;
  background: linear-gradient(180deg, rgba(125, 179, 250, 1) 0%, rgba(50, 150, 250, 1) 100%);
  z-index: 0;
}

.report-container .page-product-score .product-average-score-wrap .product-average-score {
  position: relative;
  width: 100%;
  text-align: center;
  color: #fff;
  font-weight: 500;
  line-height: 3.65rem;
  font-size: 1rem;
}

.report-container .page-product-score .product-average-score-wrap .product-average-score span {
  font-size: 1.6rem;
}

.report-container .page-data-summary .page-main>.title-wrap,
.report-container .page-suggestion .page-main>.title-wrap {
  position: absolute;
  top: 2.28rem;
  right: 0.8rem;
  width: 4.99rem;
  height: 2.02rem;
  background-color: rgba(50, 150, 250, 0.1);
}

.report-container .page-data-summary .page-main>.title-wrap .title,
.report-container .page-suggestion .page-main>.title-wrap .title {
  padding: 0 0.04rem;
  text-align: right;
  color: #3296FA;
  font-size: 0.3rem;
  line-height: 0.42rem;
  margin-top: -0.48rem;
  letter-spacing: 0.05rem;
  box-sizing: border-box;
}

.report-container .page-data-summary .data-summary,
.report-container .page-suggestion .data-summary {
  margin-top: 4.06rem;
}

.report-container .page-data-summary .data-summary .block,
.report-container .page-suggestion .data-summary .block {
  display: flex;
  margin-top: 0.56rem;
  height: 1.61rem;
}

.report-container .page-data-summary .data-summary .block .icon-wrap,
.report-container .page-suggestion .data-summary .block .icon-wrap {
  width: 0.82rem;
  margin-right: 0.38rem;
}

.report-container .page-data-summary .data-summary .block .icon-wrap img,
.report-container .page-suggestion .data-summary .block .icon-wrap img {
  width: 100%;
}

.report-container .page-data-summary .data-summary .block .content,
.report-container .page-suggestion .data-summary .block .content {
  flex: 1;
}

.report-container .page-data-summary .data-summary .block .content .title,
.report-container .page-suggestion .data-summary .block .content .title {
  font-size: 0.3rem;
  font-weight: 500;
  color: rgba(51, 51, 51, 1);
  line-height: 0.42rem;
}

.report-container .page-data-summary .data-summary .block .content .description,
.report-container .page-suggestion .data-summary .block .content .description {
  height: 1.11rem;
  overflow: hidden;
  margin-top: 0.08rem;
  color: #666;
  font-size: 0.26rem;
  line-height: 0.37rem;
}

.report-container .page-suggestion .data-summary {
  margin-top: 3.8rem;
}

.report-container .page-back-cover {
  background-image: url('https://img1.dxycdn.com/2020/0211/280/3396051525272200488-2.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  text-align: center;
  padding-top: 4.17rem;
  color: #fff;
  box-sizing: border-box;
}

.report-container .page-back-cover .logo {
  width: 3rem;
}

.report-container .page-back-cover .title {
  font-size: 0.88rem;
  font-weight: 500;
  line-height: 1.23rem;
  margin-top: 0.4rem;
}

@media print {
  body {
    background: #fff;
    height: auto !important;
    overflow: visible !important;
  }

  html {
    font-size: 44pt !important;
    height: auto !important;
    overflow: visible !important;
  }

  .noprint {
    display: none;
  }

  /* 确保报告容器能够显示所有内容 */
  .report-container {
    height: auto !important;
    overflow: visible !important;
  }

  .report-container .page-wrap {
    width: 840pt;
    height: 594pt;
    -webkit-column-break-after: always;
    break-after: always;
    min-width: auto;
    max-width: auto;
    display: flex;
    align-items: center;
    page-break-inside: avoid;
    break-inside: avoid;
    -webkit-column-break-inside: avoid;
  }

  .report-container .page-wrap:not(:last-child) {
    margin-bottom: 0;
  }

  .report-container .page {
    width: 100%;
    height: 472pt;
    overflow: hidden;
  }

  .report-container .page.page-shop-traffic {
    height: 11.5rem;
  }

  .report-container .page-shop-traffic .chart-snapshot-wrap {
    display: block;
    width: 15rem !important;
    height: 5.5rem !important;
    margin: 1.2rem auto 0 auto !important;
  }

  .report-container .page-shop-traffic .chart-snapshot-wrap img {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    display: block;
    margin: 0 auto;
    box-sizing: border-box;
  }

  .report-container .page-shop-traffic .shop-traffic-chart {
    display: none;
  }

  .report-container .page-shop-traffic-source .chart-snapshot-wrap {
    display: block;
  }

  .report-container .page-shop-traffic-source .shop-traffic-source-chart {
    display: none;
  }

  .report-container .page-shop-inquiry .chart-snapshot-wrap {
    display: block;
  }

  .report-container .page-shop-inquiry .shop-inquiry-chart {
    display: none;
  }

  .page-shop-inquiry .page-main {
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-start !important;
    justify-content: flex-start !important;
    width: 100% !important;
    box-sizing: border-box;
  }

  .page-shop-inquiry .data-summary {
    width: 50% !important;
    max-width: 50% !important;
    margin: 0 0 0 -0.8rem !important;
    padding: 0.58rem 0.8rem !important;
    box-sizing: border-box;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    min-height: 100% !important;
    background-color: #fafafa !important;
  }

  .page-shop-inquiry .chart-snapshot-wrap {
    width: 50% !important;
    max-width: 50% !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    height: 100% !important;
  }

  .page-shop-inquiry .chart-snapshot-wrap img {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    display: block;
    margin: 0 auto;
    box-sizing: border-box;
  }

  .page-shop-traffic-source .page-main {
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-start !important;
    justify-content: flex-start !important;
    width: 100% !important;
    box-sizing: border-box;
  }

  .page-shop-traffic-source .data-summary {
    width: 50% !important;
    max-width: 50% !important;
    margin: 0 0 0 -0.8rem !important;
    padding: 0.58rem 0.8rem !important;
    box-sizing: border-box;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    min-height: 100% !important;
    background-color: #fafafa !important;
  }

  .page-shop-traffic-source .chart-snapshot-wrap {
    width: 50% !important;
    max-width: 50% !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    height: 100% !important;
  }

  .page-shop-traffic-source .chart-snapshot-wrap img {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    display: block;
    margin: 0 auto;
    box-sizing: border-box;
  }
}

@page {
  size: A4 landscape;
  margin: 0;
}