{"baselist复杂版": {"prefix": "baselist", "body": ["import React, { useState } from 'react'", "import BaseList from './index'", "import api from 'src/api'", "import { Input,Button } from 'antd'", "import ContentBlock from 'src/components/common/contentBlock'", "function BaseListExample() {", "  // 额外的筛选参数", "  const [filters,setFilters] = useState({ type:'' })", "  const baselistRef = React.createRef()", "  const request = {", "    api:api.product.getProductList,", "    // 返回结果处理，使其符合规定的列表结构。非必填", "    resDecoration(res){", "      const { items=[], page_index: pageNo=1, items_per_page: pageSize=20, total_items: totalCount=0 }  = res", "      return {", "        results:{", "          items,", "          pageBean:{", "            pageNo,", "            pageSize,", "            totalCount", "          }", "        }", "      }", "    },", "    // 请求前的请求参数处理，试用一些额外需要传递的参数。非必填", "    requestDecoration(params){", "      return { ...params,action:'list' }", "    }", "  }", "  // 表格列表配置", "  const columns = [", "    {", "      title:'A',", "      dataIndex:'cate_name',", "      key:'cate_name'", "    },", "    {", "      title: 'B',", "      dataIndex: 'name',", "      key: 'name'", "    },", "    {", "      title:'操作',", "      dataIndex: 'id',", "      key:'id',", "      render: () =>{", "        return <><Button onClick={baselistRef.current.fetchData} type=\"link\">删除</Button><Button type=\"link\">删除</Button></>", "      }", "    }", "  ]", "  // 操作栏配置", "  const control = {", "    search:{", "      placeholder:'请输入',", "      prop:'search'", "    },", "    buttonList:[", "      {", "        label:'新增',", "        // eslint-disable-next-line @typescript-eslint/no-empty-function", "        onClick:()=>{},", "        type:'primary'", "      },", "      {", "        value: '其他',", "        // eslint-disable-next-line @typescript-eslint/no-empty-function", "        onClick: () => { },", "        type: 'default'", "      }", "    ],", "    // 额外的筛选项，操作样式", "    slot:<Input value={filters.type} onChange={(e)=>{setFilters({ type:e.target.value })}}></Input>", "  }", "  const onChangeFilters = (value)=>{", "    // eslint-disable-next-line @typescript-eslint/no-unused-vars", "    const { search,...filters } = value", "    setFilters(filters)", "  }", "  const tableProps = {", "    columns,", "    rowKey:\"id\"", "  }", "  return (", "    <div>", "      <BaseList", "        title=\"我的商铺\"", "        ref={baselistRef}", "        request={request}", "        control={control}", "        tableProps={tableProps}", "        onChangeFilters={onChangeFilters}", "        filters={filters}", "      >", "      </BaseList>", "    </div>", "  )", "}", "", "export default BaseListExample"], "description": "baselist复杂版"}, "baselist简单版": {"prefix": "baselist", "body": ["import React from 'react'", "import BaseList from 'src/components/common/baseList'", "import api from 'src/api'", "import { Button } from 'antd'", "function BaseListExample() {", "  const request = {", "    api: api.marketing.getSpecialManage,", "    resDecoration(res) {", "      const { items = [], page_index: pageNo = 1, items_per_page: pageSize = 20, total_items: totalCount = 0 } = res", "      return {", "        results: {", "          items,", "          pageBean: {", "            pageNo,", "            pageSize,", "            totalCount", "          }", "        }", "      }", "    },", "    requestDecoration(params) {", "      return {", "        items_per_page: params.pageSize,", "        page_index: params.pageNo,", "        total_pages: params.pageNo,", "        action: 'List'", "      }", "    }", "  }", "  const columns = [", "    {", "      title: '标题',", "      dataIndex: 'title',", "      key: 'title',", "      render: (text, row) => {", "        return <a href={row.specialUrl} className=\"text-btn\">{text}</a>", "      }", "", "    },", "    {", "      title: '创建时间',", "      dataIndex: 'createTime',", "      key: 'createTime'", "    },", "    {", "      title: '商品数',", "      dataIndex: 'productCount',", "      key: 'productCount'", "    },", "    {", "      title: '操作栏',", "      key: 'specialUrl',", "      render: (text, row) => {", "        return <>", "          <Button type=\"link\">二维码</Button>", "          <Button type=\"link\">编辑</Button>", "          <Button type=\"link\">删除</Button>", "          <Button type=\"link\">产品管理</Button>", "        </>", "      }", "    }", "  ]", "  const control = {", "    buttonList:[", "      {", "        label:'新建专题',", "        onClick(){", "          return", "        }", "      }", "    ]", "  }", "  const tableProps = {", "    columns,", "    rowKey: \"id\"", "  }", "  return (", "    <div id=\"specialManageList\">", "      <BaseList", "        title=\"我的商铺\"", "        control={control}", "        request={request}", "        tableProps={tableProps}", "      >", "      </BaseList>", "    </div>", "  )", "}", "", "export default BaseListExample"], "description": "baselist简单版"}}