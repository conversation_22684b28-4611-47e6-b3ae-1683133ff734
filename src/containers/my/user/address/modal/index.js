import { Modal, Form } from 'antd';
import React, { useState } from 'react';
import './index.less';
import api from 'src/api';
import DxyCascading from '@dxy/cascading-comp-react';

const EditAddressMoal = (props) => {
  const {
    data = {},
    visible,
    onSubmit,
    onCancel,
    form: { getFieldDecorator, validateFields, setFieldsValue },
  } = props;

  const [addressPathName, setAddressPathName] = useState('');
  const [addressRef, setAddressRef] = useState(null);

  const [nameError, setNameError] = useState(false);
  const [locationError, setLocationError] = useState(false);
  const [addressError, setAddressError] = useState(false);
  const [phoneError, setPhoneError] = useState(false);

  const addressAfterInit = () => {
    if (addressRef && data?.location) {
      const result = addressRef.state.cascading.searchData(data.location.toString());
      const { origin, pathName } = result || {};
      if (origin) {
        addressRef.state.cascading.setCurrentSelected(origin);
        setAddressPathName(pathName[0] && pathName[0].join('-'));
      }
    }
  };

  const handleSubmit = () => {
    setNameError(false);
    setLocationError(false);
    setAddressError(false);
    setPhoneError(false);
    validateFields((errors, values) => {
      if (errors) {
        if (errors) {
          Object.values(errors).forEach(({ errors: fieldErrors }) => {
            if (fieldErrors && fieldErrors.length > 0) {
              const fieldName = fieldErrors[0].field;

              if (fieldName === 'name') {
                setNameError(true);
              }

              if (fieldName === 'location') {
                setLocationError(true);
              }

              if (fieldName === 'address') {
                setAddressError(true);
              }

              if (fieldName === 'phone') {
                setPhoneError(true);
              }
            }
          });
          return;
        }
      }

      api.user
        .editUserAddress({
          data: {
            ...values,
            op: data?.id ? 'put' : undefined,
            id: data?.id,
          },
        })
        .then(() => {
          onSubmit();
        });
    });
  };

  const addressConfirm = (value) => {
    const { id, pathName } = value;
    setAddressPathName(pathName[0] && pathName[0].join('-'));
    setFieldsValue({ location: id[0] });
  };

  return (
    <Modal title="编辑收货地址" visible={visible} onCancel={onCancel} onOk={handleSubmit}>
      <div className="editAddressForm">
        <div className="row">
          <div className="half">
            <div className="formBolck">
              <span className="form-label">* 姓名</span>
              {getFieldDecorator('name', {
                initialValue: data?.name || '',
                rules: [{ required: true, message: '联系人不能为空' }],
              })(<input type="text" name="name" />)}
            </div>
            <div className={nameError ? 'label-error' : 'label-error-hiden'}>联系人不能为空</div>
          </div>
          <div className="half">
            <div className="formBolck">
              <span className="form-phone">* 电话</span>
              {getFieldDecorator('phone', {
                initialValue: data?.mobile_phone || '',
                rules: [{ required: true, message: '电话号码不能为空' }],
              })(<input type="text" name="phone" />)}
            </div>
            <div className={phoneError ? 'label-error' : 'label-error-hiden'}>电话号码不能为空</div>
          </div>
        </div>

        <div className="row">
          <div className="full">
            <div className="formBolck">
              <span className="form-label">* 地区</span>

              {getFieldDecorator('location', {
                initialValue: data?.location,
                rules: [{ required: true, message: '地区不能为空' }],
              })(<input type="hidden" name="location" />)}

              <DxyCascading
                ref={(ref) => {
                  setAddressRef(ref);
                }}
                version={'mtw2hhcey5uxsrqbgm3ciyyvk6g7pf3a'}
                type="location"
                listId="location-list"
                afterInit={addressAfterInit}
                dataUrl={
                  'https://a1.dxycdn.com/core/widgets/cascading-list-v2/data/build/location.json'
                }
                allDataUrl={
                  'https://a1.dxycdn.com/core/widgets/cascading-list-v2/data/build/location_all.json'
                }
                valueFormat={({ pathName }) => {
                  return pathName[0] && pathName[0].join('-');
                }}
                maxLevel={3}
                customContainer={({ onClick }) => {
                  return (
                    <div
                      onClick={onClick}
                      style={{
                        width: '100%',
                        height: '100%',
                      }}
                    >
                      {addressPathName}
                    </div>
                  );
                }}
                confirm={addressConfirm}
              />
            </div>
            <div className={locationError ? 'label-error' : 'label-error-hiden'}>地区不能为空</div>
          </div>
        </div>

        <div className="row">
          <div className="full">
            <div className="formBolck">
              <span className="form-label">* 详细地址</span>
              {getFieldDecorator('address', {
                initialValue: data?.address || '',
                rules: [{ required: true, message: '详细地址不能为空' }],
              })(<input type="text" name="address" />)}
            </div>
            <div className={addressError ? 'label-error' : 'label-error-hiden'}>
              详细地址不能为空
            </div>
          </div>
        </div>
        <div className="row">
          <div className="full formBolck">
            <span className="form-label"> 邮箱</span>
            {getFieldDecorator('email', {
              initialValue: data?.email || '',
            })(<input type="text" name="email" />)}
          </div>
        </div>
        <div className="row">
          <div className="full formBolck">
            <span className="form-label"> 单位/学校</span>
            {getFieldDecorator('company', {
              initialValue: data?.company || '',
            })(<input type="text" name="company" />)}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default Form.create()(EditAddressMoal);
