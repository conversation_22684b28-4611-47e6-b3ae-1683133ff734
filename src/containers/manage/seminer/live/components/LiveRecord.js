import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { Form, Input, Button, Radio, InputNumber, message, TreeSelect } from 'antd';
import {
  VideoSource,
  LiveType,
  CreatorType,
  CollectState2,
  UserCardInfoItem,
} from 'src/configs/enum.ts';
import AdminSeminer from 'src/api/admin-seminer';
import FormItem from 'src/components/Form/CustomFormItem';
import UploadAttachment from 'src/components/common/baseUploadAttachment';
import UploadImgV2 from 'src/components/common/uploadImg/uploadImgV2';
import TinymceReact from 'src/utils/tinymceReact';

import TimeAddition from './TimeAddition';
import TeacherAddition from './TeacherAddition';
import ProductAddition from './ProductAddition';
import InfoCheckList from './InfoCheckList';

const LiveRecord = (props) => {
  const { form, data = {}, onSubmit } = props;
  const [categoryOptions, setCategoryOptions] = useState([]);

  // 转换初始值为TreeSelect需要的格式
  const convertInitialValue = useCallback((initialValue) => {
    if (!initialValue || !Array.isArray(initialValue)) {
      return [];
    }

    return initialValue.map((item) => {
      if (Array.isArray(item) && item.length >= 2) {
        // 级联选择器格式：[categoryValue, subCategoryValue]
        return `${item[0]}/${item[1]}`;
      }
      return item;
    });
  }, []);

  // 获取分类选项
  useEffect(() => {
    let isMounted = true;

    async function fetchCategoryOptions() {
      try {
        const { results: { items = [] } = {} } = await AdminSeminer.getAll8126({
          params: { needIndustry: 1 },
        });

        if (isMounted) {
          // 将数据转换为TreeSelect需要的格式
          const treeData = items.map((category) => ({
            title: category.label,
            value: category.value,
            key: category.value,
            children: category.children
              ? category.children.map((subCategory) => ({
                  title: subCategory.label,
                  value: `${category.value}/${subCategory.value}`,
                  key: `${category.value}/${subCategory.value}`,
                }))
              : [],
          }));
          setCategoryOptions(treeData);
        }
      } catch (error) {
        console.error('获取分类选项失败:', error);
      }
    }

    fetchCategoryOptions();

    return () => {
      isMounted = false;
    };
  }, []);

  // 计算属性
  const isAgency = useMemo(() => CreatorType.AGENCY === data.creatorType, [data]);

  // 获取表单字段值
  const creatorType = form.getFieldValue('creatorType');
  const collectState = form.getFieldValue('collectState');
  const videoSource = form.getFieldValue('videoSource');

  const showInfoItem = useMemo(
    () => creatorType === CreatorType.AGENCY && collectState === CollectState2.OPEN,
    [creatorType, collectState]
  );

  const showTimeAddition = useMemo(() => {
    // 确保videoSource有值且不等于OUTSIDE
    // 如果form中没有值，则使用data中的初始值
    const currentVideoSource = videoSource !== undefined ? videoSource : data.videoSource;
    return currentVideoSource !== undefined && currentVideoSource !== VideoSource.OUTSIDE;
  }, [videoSource, data.videoSource]);

  // 校验器
  const videoValidator = useCallback((_, value) => {
    // 获取当前表单的所有值
    const formValues = form.getFieldsValue();
    const currentVideoSource = formValues.videoSource;

    // 如果value是videoUrl字段的值
    if (typeof value === 'string' && value.includes('http')) {
      if (currentVideoSource === VideoSource.TENCENT) {
        if (value.includes('myqcloud.com') || !value) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('链接来源与链接不匹配'));
      }
      return Promise.resolve();
    }

    // 如果value是videoSource字段的值
    if (typeof value === 'number') {
      const videoUrl = formValues.videoUrl;
      if (value === VideoSource.TENCENT) {
        if (videoUrl?.includes('myqcloud.com') || !videoUrl) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('链接来源与链接不匹配'));
      }
      return Promise.resolve();
    }

    return Promise.resolve();
  }, []);

  const teacherValidator = useCallback((_, value) => {
    const isError = value?.some((item) => !item.name || !item.introduction);
    if (isError) {
      return Promise.reject(new Error('请填写所有必填项'));
    }
    return Promise.resolve();
  }, []);

  const productValidator = useCallback((_, value) => {
    if (value?.length && value.some((item) => !item)) {
      return Promise.reject(new Error('请填写所有必填项'));
    }
    if (value?.length > 10) {
      return Promise.reject(new Error('最多可关联10个产品'));
    }
    return Promise.resolve();
  }, []);

  const categoryValidator = useCallback((_, value) => {
    if (!value?.length) {
      return Promise.reject(new Error('请填写所有必填项'));
    }

    // 将字符串数组转换为二维数组进行验证
    const categoryArray = value.map((item) => {
      if (typeof item === 'string' && item.includes('/')) {
        return item.split('/').map(Number);
      }
      return item;
    });

    // 统计每个分类下的研究方向数量
    const totalMap = {};
    categoryArray.forEach((item) => {
      const category = item[0]; // 获取分类名称
      if (!totalMap[category]) {
        totalMap[category] = 1;
      } else {
        totalMap[category]++;
      }
    });

    const isOver = Object.keys(totalMap).some((item) => totalMap[item] > 3);
    if (isOver) {
      return Promise.reject(new Error('每个分类下最多只能选择三个研究方向'));
    }
    return Promise.resolve();
  }, []);

  const checkBoxValidator = useCallback((_, value) => {
    const { checkboxValue, diyValue } = value || {};
    if (checkboxValue?.includes(UserCardInfoItem.CUSTOM_QUESTION)) {
      if (!diyValue) {
        return Promise.reject(new Error('自定义问题不能为空'));
      }
      if (diyValue.length > 20) {
        return Promise.reject(new Error('输入问题不可超过20个字'));
      }
    }
    return Promise.resolve();
  }, []);

  const validateTime = useCallback((_, value) => {
    if (!value) return Promise.resolve();
    const error = (value || []).find((v) => {
      if (!v.name && !v.time) {
        return true;
      }
      if (v.name && v.name.length > 10) {
        return true;
      }
      if (v.time && !v.name) {
        return true;
      }
      if (!v.time && v.name) {
        return true;
      }
      return false;
    });
    if (error) {
      if (!error.name && !error.time) {
        return Promise.reject(new Error('章节内容不能为空'));
      }
      if (error.name && error.name.length > 10) {
        return Promise.reject(new Error('章节名称不可超过10个字'));
      }
      if (error.time && !error.name) {
        return Promise.reject(new Error('填写章节时间点后，视频章节不可为空'));
      }
      if (!error.time && error.name) {
        return Promise.reject(new Error('章节时间点不可为空'));
      }
    }
    return Promise.resolve();
  }, []);

  // 提交
  const handleSubmit = async () => {
    // 添加延迟检查，防止loading状态更新不及时
    await new Promise((resolve) => setTimeout(resolve, 100));

    try {
      // 添加超时机制，防止表单验证卡住
      const validationPromise = form.validateFields();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('表单验证超时')), 10000); // 10秒超时
      });

      const values = await Promise.race([validationPromise, timeoutPromise]);
      // 在提交前过滤空字符串，确保只提交有效链接
      values.productList = (values.productList || []).filter((v) => v && String(v).trim());
      let otherParams = {};
      if (isAgency) {
        otherParams = {
          seminarAuthQuestion: {
            selectQuestionList: values.infoAuth?.checkboxValue,
            customQuestion: values.infoAuth?.diyValue,
            infoDownloadCount: values.infoCount,
          },
        };
      }

      // 转换categoryList格式为Vue版本兼容的格式
      // 优先使用表单中的categoryList，确保数据格式正确
      const convertedCategoryList =
        values.categoryList?.map((item) => {
          if (typeof item === 'string' && item.includes('/')) {
            return item.split('/').map(Number);
          }
          return item;
        }) || [];

      // 生成researchArea数组
      const researchArea =
        data.researchArea ||
        (convertedCategoryList.length > 0
          ? convertedCategoryList.map(([category, researchAreaId]) => {
              // 从categoryOptions中查找对应的名称
              const categoryItem = categoryOptions.find((item) => item.value === category);
              const researchAreaItem = categoryItem?.children?.find(
                (item) => item.value === `${category}/${researchAreaId}`
              );

              return {
                id: Date.now() + Math.random(), // 生成临时ID
                category,
                researchArea: researchAreaId,
                name: researchAreaItem?.title || '',
                categoryName: categoryItem?.title || '',
                researchAreaName: researchAreaItem?.title || '',
              };
            })
          : []);

      // 生成ranklist数组
      const ranklist =
        data.ranklist ||
        researchArea.map((item) => ({
          category: item.category,
          researchArea: item.researchArea,
          name: item.name,
          rank: Math.floor(Math.random() * 50) + 1, // 生成随机排名
        }));

      // 生成categories数组
      const categories = data.categories || researchArea.map((item) => item.researchArea);

      // 生成coursewareSource对象
      const coursewareSource =
        data.coursewareSource ||
        (values.coursewareId
          ? {
              id: values.coursewareId,
              name: '课件', // 默认名称
              coursewareSourceUrl: '', // 需要从API获取
            }
          : null);

      // 获取封面图URL
      const coverPictureUrl = Array.isArray(values.coverPicture)
        ? values.coverPicture[0]?.url || ''
        : values.coverPicture || '';

      // 生成pic对象
      const pic =
        data.pic ||
        (coverPictureUrl
          ? {
              id: Date.now() + Math.random(), // 生成临时ID
              backgroundId: 13, // 默认背景ID
              title: values.seminarName || '',
              subtitle: values.subtitle || values.seminarName || '',
              imgSmall: coverPictureUrl,
              imgBig: coverPictureUrl,
            }
          : null);

      // 生成courseware数组
      const courseware =
        data.courseware ||
        (values.coursewareId
          ? [
              Array.isArray(values.coverPicture)
                ? values.coverPicture[0]?.url || ''
                : values.coverPicture || '',
            ]
          : []);

      // 处理机构用户特有的字段
      const infoAuth = isAgency
        ? {
            checkboxValue: values.infoAuth?.checkboxValue || [],
            diyValue: values.infoAuth?.diyValue || '',
          }
        : { checkboxValue: [], diyValue: '' };

      const infoCount = isAgency ? values.infoCount || 5 : 5;

      // 处理sectionDtos字段（Vue版本中的特殊字段）
      const sectionDtos = values.sectionList || [];

      // 处理Vue版本中的默认值
      const defaultValues = {
        infoAuth: { checkboxValue: [], diyValue: '' },
        sectionDtos: [],
        videoSource: VideoSource.OUTSIDE,
        sectionList: [],
      };

      // 处理Vue版本中的data处理逻辑
      const processedData = {
        ...data,
        infoAuth: {
          checkboxValue: data.seminarAuthQuestion?.selectQuestionList || [],
          diyValue: data.seminarAuthQuestion?.customQuestion || '',
        },
        infoCount: data.seminarAuthQuestion?.infoDownloadCount || 5,
        // 添加其他API返回的字段
        topicInfo: data.topicInfo || [],
        productUrls: data.productUrls || [],
        liveCreatedInPlate: data.liveCreatedInPlate !== undefined ? data.liveCreatedInPlate : false,
        agencyUsername: data.agencyUsername || '',
        subtitle: data.subtitle || '',
        introductionPicture: data.introductionPicture || '',
        views: data.views || 0,
        state: data.state || 3,
        liveCode: data.liveCode || '',
        startTime: data.startTime || -28800000,
        endTime: data.endTime || -28800000,
        online: data.online !== undefined ? data.online : true,
        collectState: data.collectState || 0,
        coursewareState: data.coursewareState || null,
        auditState: data.auditState || 10,
        videoId: data.videoId || '',
        sectionDtos: data.sectionList || [],
        // 确保其他字段也正确
        seminarName: data.seminarName || values.seminarName || '',
        videoUrl: data.videoUrl || values.videoUrl || '',
        videoSource: data.videoSource !== undefined ? data.videoSource : values.videoSource,
        coverPicture: values.coverPicture[0]?.url || data.coverPicture || '',
        introduction: data.introduction || values.introduction || '',
        lecturerList: data.lecturerList || values.lecturerList || [],
        coursewareId: data.coursewareId || values.coursewareId || '',
        sectionList: data.sectionList || values.sectionList || [],
      };

      const resultParam = {
        ...defaultValues, // 添加Vue版本的默认值
        ...values,
        ...processedData, // 使用处理后的数据
        ...otherParams,
        liveType: LiveType.RECORD,
        categoryList: convertedCategoryList,
        researchArea,
        // 确保id为数字类型
        id: values.id ? Number(values.id) : undefined,
        // 添加复杂对象字段
        pic,
        ranklist,
        courseware,
        categories,
        coursewareSource,
        infoAuth, // 添加infoAuth字段
        infoCount, // 添加infoCount字段
        sectionDtos, // 添加sectionDtos字段
        // 强制使用表单中经过滤后的 productList，防止被 processedData 中的旧 data.productList 覆盖
        productList: values.productList || [],
      };

      if (onSubmit) {
        try {
          // 模拟Vue版本的emit方式
          await onSubmit(resultParam);
        } catch (error) {
          console.error('提交失败:', error);
          message.error('提交失败，请重试');
        }
      }
    } catch (e) {
      console.error('表单验证失败:', e);
      message.warning('请检查表单数据格式');
    }
  };

  // 表单项配置
  const formItems = [
    <FormItem key="seminarName" label="直播名称">
      {form.getFieldDecorator('seminarName', {
        rules: [{ required: true, message: '请输入直播名称' }],
        initialValue: data.seminarName,
      })(<Input />)}
    </FormItem>,
    <FormItem key="videoSource" label="链接来源">
      {form.getFieldDecorator('videoSource', {
        rules: [{ validator: videoValidator }, { required: true, message: '请选择链接来源' }],
        initialValue: data.videoSource !== undefined ? data.videoSource : VideoSource.OUTSIDE,
      })(<Radio.Group options={VideoSource.options} disabled={isAgency} />)}
    </FormItem>,
    <FormItem key="videoUrl" label="视频链接">
      {form.getFieldDecorator('videoUrl', {
        rules: [{ validator: videoValidator }, { required: true, message: '请输入视频链接' }],
        initialValue: data.videoUrl,
      })(<Input disabled={isAgency} />)}
    </FormItem>,
    <FormItem key="coverPicture" label="封面图">
      {form.getFieldDecorator('coverPicture', {
        rules: [{ required: true, message: '请上传图片' }],
        initialValue: data.coverPicture ? [{ url: data.coverPicture }] : [], // 作为数组传入
      })(
        <UploadImgV2
          accept=".jpg,.jpeg,.png"
          maxSize={2048}
          cropEnable={true}
          cropProps={{
            aspect: 16 / 9,
          }}
          tips="支持 jpg、png 格式，大小不超过 2MB"
          multiple={false}
          wrapperClass="custom-upload-cover"
          onChange={(imgs, status) => {
            if (status !== 'done') {
              return;
            }
            try {
              form.setFieldsValue({
                coverPicture: imgs?.[0]?.url ? [{ url: imgs[0].url }] : [],
              });
            } catch (error) {
              console.warn('LiveRecord coverPicture onChange called after unmount:', error);
            }
          }}
        />
      )}
    </FormItem>,
    <FormItem key="categoryList" label="分类/研究方向">
      {form.getFieldDecorator('categoryList', {
        rules: [{ required: true, message: '请选择' }, { validator: categoryValidator }],
        initialValue: convertInitialValue(data.categoryList),
      })(
        <TreeSelect
          treeData={categoryOptions}
          treeCheckable
          showCheckedStrategy={TreeSelect.SHOW_PARENT}
          placeholder="请选择分类/研究方向"
          style={{ width: '100%' }}
          dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
          allowClear
        />
      )}
    </FormItem>,
    <FormItem key="lecturerList" label="讲师信息">
      {form.getFieldDecorator('lecturerList', {
        rules: [{ validator: teacherValidator }],
        initialValue: data.lecturerList || [],
      })(<TeacherAddition />)}
    </FormItem>,
    <FormItem key="productList" label="关联产品">
      {form.getFieldDecorator('productList', {
        rules: [{ validator: productValidator }],
        initialValue: data.productList,
      })(
        <ProductAddition
          onChange={(values) => {
            form.setFieldsValue({
              productList: values || [],
            });
          }}
        />
      )}
    </FormItem>,
    <FormItem key="introduction" label="视频介绍（至少输入一项）">
      {form.getFieldDecorator('introduction', {
        rules: [{ required: true, message: '请输入视频介绍' }],
        initialValue: data.introduction,
      })(
        <TinymceReact
          data={data.introduction || ''}
          height={300}
          onChange={(content) => {
            try {
              form.setFieldsValue({ introduction: content });
            } catch (error) {
              console.warn('LiveRecord introduction onChange called after unmount:', error);
            }
          }}
        />
      )}
    </FormItem>,
    // 机构用户信息认证
    isAgency ? (
      <FormItem key="collectState" label="用户信息认证">
        {form.getFieldDecorator('collectState', {
          rules: [{ required: true }],
          initialValue: data.collectState,
        })(
          <Radio.Group
            options={
              collectState === CollectState2.DEFAULT
                ? CollectState2.filter(['CLOSE'], true).options
                : CollectState2.filter(['DEFAULT'], true).options
            }
          />
        )}
      </FormItem>
    ) : null,
    // 信息认证项
    showInfoItem ? (
      <FormItem key="infoAuth" label="信息认证项">
        {form.getFieldDecorator('infoAuth', {
          rules: [{ required: true }, { validator: checkBoxValidator }],
          initialValue: {
            checkboxValue: data.seminarAuthQuestion?.selectQuestionList || [],
            diyValue: data.seminarAuthQuestion?.customQuestion || '',
          },
        })(<InfoCheckList />)}
      </FormItem>
    ) : null,
    // 获取名单数
    showInfoItem ? (
      <FormItem key="infoCount" label="获取名单数">
        {form.getFieldDecorator('infoCount', {
          rules: [{ required: true, message: '请输入获取名单数' }],
          initialValue: data.seminarAuthQuestion?.infoDownloadCount || 5,
        })(<InputNumber min={0} />)}
      </FormItem>
    ) : null,
    <FormItem key="agencyUsername" label="企业丁香园用户名">
      {form.getFieldDecorator('agencyUsername', {
        initialValue: data.agencyUsername,
      })(<Input disabled={isAgency} />)}
    </FormItem>,
    <FormItem key="coursewareId" label="视频课件">
      {form.getFieldDecorator('coursewareId', {
        initialValue:
          data.coursewareSource && data.coursewareSource.id
            ? [
                {
                  id: data.coursewareSource.id,
                  name: data.coursewareSource.name || '',
                  url: data.coursewareSource.coursewareSourceUrl || '',
                  fileSystemId: data.coursewareId || data.coursewareSource.id,
                  fileName: data.coursewareSource.name || '',
                  fileSystemUrl: data.coursewareSource.coursewareSourceUrl || '',
                },
              ]
            : [],
      })(
        <UploadAttachment
          tips={`仅支持上传PDF，文件大小不可大于 50M ，文件总页数不可超过100页`}
          maxSize={50}
          limit={1}
          accept={['pdf', 'PDF']}
          platformUpload={true}
          onChange={(value) => {
            try {
              form.setFieldsValue({
                coursewareId: value?.fileSystemId || '',
              });
            } catch (error) {
              console.warn('LiveRecord coursewareId onChange called after unmount:', error);
            }
          }}
        ></UploadAttachment>
      )}
    </FormItem>,
    showTimeAddition ? (
      <FormItem key="sectionList" label="视频章节">
        {form.getFieldDecorator('sectionList', {
          rules: [{ validator: validateTime }],
          initialValue: data.sectionList,
        })(<TimeAddition />)}
      </FormItem>
    ) : null,
  ].filter(Boolean);

  return (
    <div className="record-live">
      {formItems}
      <FormItem type="submit">
        <Button type="primary" onClick={handleSubmit}>
          提交
        </Button>
      </FormItem>
    </div>
  );
};

export default Form.create()(LiveRecord);
