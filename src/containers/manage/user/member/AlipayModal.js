import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Spin, message } from 'antd';
import AdminMember from 'src/api/admin-member';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
const AlipayModal = ({ form, userId, showAlipayModal, setShowAlipayModal }) => {
  const [defaultValue, setDefaultValue] = useState({});
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = form;
  const getCustomerAccount = async () => {
    setLoading(true);
    const response = await AdminMember.getCustomerAccount({
      params: {
        id: userId,
      },
    });
    if (response) {
      setDefaultValue(response);
    }
    setLoading(false);
  };
  useEffect(() => {
    if (userId) {
      getCustomerAccount();
    }
  }, [userId]);
  if (loading) {
    return <Spin />;
  }

  const setCustomerAccount = async (values) => {
    const api = defaultValue.id
      ? AdminMember.changeCustomerAccount
      : AdminMember.addCustomerAccount;

    const response = await api({
      data: {
        id: defaultValue?.id,
        agencyId: defaultValue?.agencyId || userId,
        ...values,
      },
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    if (response) {
      message.success(response);
    } else {
      message.error('系统异常');
    }
    setShowAlipayModal(false);
  };

  const onSubmit = (e) => {
    e.preventDefault();
    form.validateFields(async (err, values) => {
      if (!err) {
        setCustomerAccount(values);
      }
    });
  };
  return (
    <Modal
      title="支付宝账号"
      width={500}
      visible={showAlipayModal}
      onOk={onSubmit}
      onCancel={() => {
        setShowAlipayModal(false);
      }}
      destroyOnClose
    >
      <Form {...formItemLayout} form={form}>
        <Form.Item label="账户">
          {getFieldDecorator('aliaccount', {
            rules: [
              {
                required: true,
                message: '请输入支付宝账号',
              },
            ],
            initialValue: defaultValue.alipayAccount,
          })(<Input placeholder="请输入支付宝账号" />)}
        </Form.Item>
        <Form.Item label="账户名称">
          {getFieldDecorator('aliaccountName', {
            rules: [
              {
                required: true,
                message: '请输入支付宝账户名称',
              },
            ],
            initialValue: defaultValue.alipayAccountName,
          })(<Input placeholder="请输入支付宝账户名称" />)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create()(AlipayModal);
