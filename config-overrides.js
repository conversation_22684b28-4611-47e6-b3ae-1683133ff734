const path = require('path');
const { override, overrideDevServer } = require('customize-cra');
const configFactory = require('./webpack.config-overrides');
const proxyConf = require('./proxy');

const devServerConfig = () => (config) => {
  return {
    ...config,
    proxy: {
      '/japi': proxyConf,
      '/api': proxyConf,
      '/product': proxyConf,
      '/i': proxyConf,
      '/usercenter': proxyConf,
      '/mymsg': proxyConf,
      '/admin': proxyConf,
      '/ajaxaction': proxyConf,
    },
  };
};

const addEntry = () => (config, env) => {
  // 获取发布系统打包环境
  const PUBLISH_ENV = process.env.PUBLISH_ENV;
  const publicPathConfig = {
    prod: 'https://a1.dxycdn.com/gitrepo/biomart_admin_v3/dist/',
    dynamic: 'https://a1.dxycdn.com/gitrepo/biomart_admin_v3_dynamic/dist/',
    preview: 'https://a1.dxycdn.com/gitrepo/biomart_admin_v3_preview/dist/',
    dev: `http://localhost:${process.env.PORT}/`,
  };
  config = configFactory(env, publicPathConfig);

  // 配置打包输出的目标文件夹
  if (env === 'development') {
    config.output.publicPath = publicPathConfig[PUBLISH_ENV];
    // eslint-disable-next-line no-console
    console.log('env is development, skip build path change...');
  } else {
    const paths = require('react-scripts/config/paths');
    paths.appBuild = path.join(path.dirname(paths.appBuild), 'dist');
    config.output.path = path.join(path.dirname(config.output.path), 'dist');

    // 配置publicPath
    const publicPath = publicPathConfig[PUBLISH_ENV] || publicPathConfig.prod;
    paths.appPath = publicPath;
    config.output.publicPath = publicPath;
  }

  return config;
};

module.exports = {
  webpack: override(addEntry()),
  devServer: overrideDevServer(devServerConfig()),
};
