{"name": "biomart_cms_v2", "version": "0.1.36", "private": true, "scripts": {"start": "yarn snippet && cross-env PORT=3200  PUBLISH_ENV=dev react-app-rewired start", "build": "react-app-rewired build", "test": "jest", "dxybuild:test": "cross-env PUBLISH_ENV=test npm run build", "dxybuild:prod": "cross-env PUBLISH_ENV=prod npm run build", "dxybuild:preview": "cross-env PUBLISH_ENV=preview npm run build", "dxybuild:dynamic": "cross-env PUBLISH_ENV=dynamic npm run build", "eslint": "eslint ./src --ext .js,.ts,.jsx", "api": "auto create", "snippet": "node ./scripts/snippte.js", "dust-build:dynamic": "cross-env PUBLISH_ENV=dynamic npm run build", "dust-build:develop": "cross-env PUBLISH_ENV=test npm run build", "dust-build:master": "cross-env PUBLISH_ENV=prod npm run build", "dust-build:preview": "cross-env PUBLISH_ENV=preview npm run build", "release": "standard-version"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@ant-design/icons": "^5.5.2", "@babel/polyfill": "^7.12.1", "@dxy-toh/biomart_components": "^0.1.22", "@dxy/cascading-comp-react": "^1.2.17", "@dxy/cascading-list-v3": "^3.1.19", "@dxy/dxy-analytics": "^0.5.21", "@dxy/jarvis-cli": "^0.0.30", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@sentry/browser": "^5.30.0", "@tinymce/tinymce-react": "^4.3.2", "@types/jest": "^24.0.19", "@types/node": "^12.20.55", "@types/react": "^16.3.5", "@types/react-dom": "^16.9.2", "@types/react-redux": "^7.1.25", "@types/react-router": "^4.0.23", "@types/react-router-dom": "^5.1.0", "antd": "^3.26.20", "axios": "^0.19.0", "base64url": "^3.0.1", "blueimp-canvas-to-blob": "^3.29.0", "ckeditor4-react": "^1.4.2", "classnames": "^2.3.2", "connected-react-router": "^6.9.3", "core-js": "^3.30.2", "dayjs": "^1.11.7", "detect-browser": "^4.8.0", "echarts": "^4.9.0", "fabric": "^3.6.6", "history": "^4.10.1", "immutability-helper": "^3.0.2", "jquery": "^3.7.1", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.29.4", "prop-types": "^15.8.1", "qs": "^6.11.2", "ramda": "^0.30.1", "react": "^16.3.1", "react-color": "^2.19.3", "react-cropper": "^2.3.3", "react-dom": "^16.3.1", "react-image-crop": "^8.6.12", "react-loadable": "^5.5.0", "react-redux": "^7.2.9", "react-router": "5.3.4", "react-router-dom": "^5.1.2", "react-scripts": "3.2.0", "redux": "^4.2.1", "redux-logger": "^3.0.6", "redux-thunk": "^2.4.2", "regenerator-runtime": "^0.14.1", "set-cookie-parser": "^2.6.0", "standard-version": "^8.0.2", "vod-js-sdk-v6": "^1.6.1", "xss": "^1.0.14"}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-class-static-block": "^7.21.0", "@dxy-toh/eslint-config-biomart": "^1.0.39", "@dxy/eslint-config-dxy": "^3.0.6", "@dxy/stylelint-config-dxy": "^0.0.2", "awesome-typescript-loader": "^5.2.1", "babel": "^6.23.0", "babel-loader": "8.0.6", "babel-plugin-import": "^1.13.6", "babel-plugin-named-asset-import": "^0.3.8", "babel-plugin-transform-class-properties": "^6.24.1", "babel-preset-stage-0": "^6.24.1", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^5.1.2", "cross-env": "^6.0.3", "customize-cra": "^0.8.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-jest": "^28.9.0", "eslint-plugin-prettier": "3.1.4", "husky": "^4.3.8", "less": "^3.13.1", "less-loader": "^5.0.0", "lint-staged": "^10.5.4", "prettier": "2.0.1", "react-app-rewired": "^2.2.1", "react-router-config": "^5.1.1", "react-use": "^13.27.1", "renamer": "^1.1.4", "style-resources-loader": "^1.5.0", "stylelint": "10.0.0", "tsconfig-paths-webpack-plugin": "^3.5.2", "typescript": "^5.7.2", "write-file-webpack-plugin": "^4.5.1"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,ts}": ["eslint --fix", "git add"]}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin master"}, "changelogHeader": "# 丁香通 新版 商家后台 项目 Changelog\n"}}