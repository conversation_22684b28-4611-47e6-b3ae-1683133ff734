module.exports = {
  env: {
    browser: true,
    es6: true,
    node: true,
  },
  extends: ['@dxy/eslint-config-dxy/base', '@dxy-toh/biomart/react', '@dxy-toh/biomart/typescript'],
  rules: {
    'comma-dangle': ['error', 'only-multiline'],
    'no-setter-return': 0,
    'no-console': ['error', { allow: ['warn', 'error', 'info'] }],
    'no-confusing-arrow': 0,
    'getter-return': 2, // 强制 getter 函数中出现 return 语句
    'no-compare-neg-zero': 2, // 禁止与 -0 进行比较
    'no-return-await': 0,
    'consistent-return': 0,
    camelcase: 0,
    'object-curly-spacing': ['error', 'always'],
    '@typescript-eslint/no-empty-function': ['error', { allow: ['arrowFunctions'] }],
  },
  globals: {
    document: true,
    window: true,
    describe: true,
    it: true,
    expect: true,
  },

  // globals: {
  //   // ... 其他测试可能使用的全局变量
  // },
};
