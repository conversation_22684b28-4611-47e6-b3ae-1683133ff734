# 丁香通 新版 商家后台 项目 Changelog

### [0.1.36](https://gitlab.dxy.net/f2e/biomart_cms_v2/compare/v0.1.35...v0.1.36) (2024-04-11)


### Features

* cas逻辑修改 ([8eff435](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/8eff435b67e750c0f6fc716135223b1250810090))
* readme更新 ([4b6560b](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/4b6560bb384c45b697bb4abe3f717b837af10f25))
* readme更新 ([a67d17d](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/a67d17d3f85b9f5cb67ad6953ecdd1274d9eb20d))
* v1.25.0 ([5aab0eb](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/5aab0eb29ad2cc0000f9e33bb612f889ac3156a9))
* v1.26.0 p2 ([0be14d6](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/0be14d6fde9b66dfa3e7b28672b9b14bd3e67f50))
* v1.26产品图片必传校验 ([e37fbc8](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/e37fbc8b538f57d7f0fac18cb4eee728b71859af))
* 企业动态，新增新闻头图字段 ([f810dee](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f810deec98763e668852970a00bf6ea879453c43))
* 全屏遮挡 ([8398c33](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/8398c333fe9683d00c9b19acf299a5f9707f5d23))
* 去掉提示文案 ([9180774](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/91807744b5e425f4d6ef5830d15cc0884d803e37))
* 商家端关闭助力形式 ([0f63bc0](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/0f63bc06d839fdba21cc57b558005ff0247234a9))
* 文案修改 ([cddbc47](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/cddbc47ce2be063f9142f45979be16ad203c054a))
* 文案调整 ([e108890](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/e108890044d7fa087a1eb70936471c77db1714bf))
* 新编辑器引入 ([ee83488](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/ee83488914a8c17e121ed795ce8def9e843c4d1a))
* 新闻编辑功能 ([9baa782](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/9baa7826214642736dd74ddd6b78c0fd13de3660))
* 自定义上传 ([e80218b](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/e80218b32ed01bb35b2731891f52422006888f29))
* 资讯，页面开发 ([a938491](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/a938491002cad7ff79944fb53a906023be57e168))
* 隐藏快捷设置入口 ([8c8de86](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/8c8de86442f8a0a0e7e94f54d23f9e3f7d1d2bb5))


### Bug Fixes

* bug修复 ([f0bb075](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f0bb075ce2b1cdf2c08de3f0e68a978bf5c62f8f))
* ckeditor加载数据问题 ([0dba2f2](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/0dba2f279d72eafc681c33b8d5ca5f7c9550363d))
* mac 和 windows10系统，显示新版编辑器 ([f145de3](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f145de3371c75c9e06b60b580b99ad1ccccc8b57))
* 不同分类发布后状态区分 ([c110354](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/c110354ddb2ae75e7ab0aed45ecdf64733de6277))
* 产品列表，排序修改 ([87e3503](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/87e3503917f6ae8e0984d930f4cbd726ede91fcd))
* 会员权益文案修改 ([d407952](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/d40795263b230320995584a0b7e183097ca526ab))
* 关闭编辑器自动转换链接 ([17420db](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/17420dbd5c89022f4abdecf952d3c02be55d97b9))
* 判断windows11系统，显示旧版编辑器 ([996914f](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/996914f7d4d30c11b488cc85e8414e92bb34fc69))
* 加载字段可编辑 ([bed43a8](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/bed43a8397220a8a28ca203b98a557fc43327876))
* 历史产品编辑逻辑修改，计分逻辑修改 ([9a5dd1c](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/9a5dd1cb270ce37ebad58b35aabddc70300edc6f))
* 去掉打印调试语句 ([72900d2](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/72900d26aacd0feb9713285cd7f9f76150c522d7))
* 去掉旧编辑器 ([31c0e56](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/31c0e5659c72e9de336df91b267c4cf1bd6ba2ed))
* 去除 ([e406ae3](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/e406ae3ce22f1075f35724cbb30c09c28423a222))
* 变量判断 ([f63d5fd](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f63d5fd1e2f438f5a1c2b0d6db2bc0c65ae55e62))
* 域名校验 ([ee58e01](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/ee58e0173acf3ecfcb4a243e312371ba1b96b366))
* 外链匹配，每次只匹配一个 ([19b7062](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/19b7062146d7dcafa6d585f2ea7db1d7da7c1fa4))
* 提交时自动把图片转成内部url ([1724438](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/1724438f3ec38d9d0c9489ae55a0fc3cb4e7ba11))
* 提交时自动把图片转成内部url ([29e28be](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/29e28be45fb014cd446e7c91ed93bb44f01672d6))
* 敏感词字段匹配 ([e859b9c](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/e859b9c5dddff73db9859ea58140bab041fd733a))
* 敏感词字段匹配 ([4b87b6b](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/4b87b6b04b4a85c2744e56adc745bd2bd1948158))
* 敏感词校验 ([c13b20c](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/c13b20c06a0ac88ed47ae0258c955ac198242793))
* 敏感词校验提示 ([0c2223a](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/0c2223acc088dac206fe2ed47f1c62b243ccd0e9))
* 文案修改 ([b149bdd](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/b149bdde97c743ac4abb6a2431c4304b6d62843b))
* 新编辑器，上传联调 ([12c14f8](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/12c14f83a07902b0c1a72c7dcb23eacef223a0b0))
* 新编辑器对应修改算分逻辑 ([8e56078](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/8e560787b045374ad26854348d49c94e1503c7c1))
* 方法名错误 ([161b7a4](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/161b7a4804a81f3ac60ff00afe51ecdb7bdbeeaa))
* 测试新编辑器 ([01be3e1](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/01be3e11f61dae108dd89ba3264153fcdcae9137))
* 测试新编辑器，图片上传 ([7c456aa](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/7c456aa9b80c6fa21d1080fa1aa1673896f684ed))
* 编辑器，外链校验 ([d123e56](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/d123e565fbe96463f688c9d086621e453a24e571))
* 编辑器，调整 ([f39ac8b](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f39ac8bfbff94951c1e56cc253cf6f2380b76a94))
* 编辑器外链校验 ([3332e8a](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/3332e8aa226aa496ae920c6f55337c7f33285257))
* 编辑器外链校验调整 ([8872efe](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/8872efeac73606693295b146c9545e0ed93b9cf2))
* 编辑器换行和回车，事件修改 ([1bdae54](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/1bdae5410b2267c657084fd4cbad5c711ee98351))
* 资讯，翻页修改 ([f9e3479](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f9e347979b5a25dd9be2bc2fe6819ac8d31b225d))

### [0.1.35](https://gitlab.dxy.net/f2e/biomart_cms_v2/compare/v0.1.34...v0.1.35) (2023-10-25)


### Features

* ga ([cde54f9](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/cde54f9a2cc5237e9189f67fcb6928a0e61ffb29))
* ga ([43516df](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/43516df5f01e4996bda51bf7560d66ca69b7d3c1))
* node版本指定10 ([6109576](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/6109576746b141a3ca68196ea72c8815fa09d1e5))
* review优化 ([b143d8f](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/b143d8f3d9c85a09f19ea57f0c6e757f245cb80a))
* v1.11.0 ([9c4ece4](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/9c4ece463cd4943fb36c801104306362dd5d0a9e))
* v1.21.0版本P1功能 ([f1e2679](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f1e2679d709b91b401139e454c238020baa66fb8))
* v1.8.0 ([fd08548](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/fd08548a75742afe6b3d43ff7e88bfc7e6140f18))
* 保留原来的,并新增 ([b963f4e](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/b963f4e52d49634d451315a4e929bcd59433be8d))
* 信息完整度评分的计算修改加日志 ([20f4edd](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/20f4edde438110166b648ae2386aab0bacbfad0d))
* 信息完整度评分的计算修改加日志 ([02122c6](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/02122c6755cabadfb9bba76e6666f2e6cc010159))
* 信息完整度评分的计算修改加日志 ([fa81b9a](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/fa81b9a1b3651886e00109fdf508a5cbb5fde1f7))
* 修改产品信息完整度 ([3504b12](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/3504b12cbf6c811bdf6f199f593d45f4c2e7b0fc))
* 修改产品信息完整的评分 ([70b3008](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/70b300800afdb0439fcf463689ed288fed3a0031))
* 修改价格区间加分判断 ([7daded7](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/7daded74377aa615aa80c98fae125ad1cd67b798))
* 切换状态,重新请求数据 ([23d7275](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/23d7275d4daeda9705b13253599a766ba8e9a46b))
* 列表回显 ([a456c1a](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/a456c1a56e4887df4ec90c4b689003a459c9d1d6))
* 列表回显调整 ([c9bed38](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/c9bed38d077ee72dbe269cf4d9b8e943dd81defb))
* 加点注释 ([7554875](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/7554875387bb26c36b0039bc1594a974a4d4aced))
* 动态插入 ([b03a990](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/b03a990dee311212afdfce7582c1d19f97c62e63))
* 商家管理后台，试用报告露出用户 ([f7e56bf](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f7e56bf4d64223c2c3085c272f9e71f0ac3b1b03))
* 字段调整 ([f49994e](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f49994e98fd9f99d3ac643dbb0c0ca7541aee2aa))
* 打包 ([f0a7895](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f0a7895458fcfec68fb2ff233af9c4ddec5d8abc))
* 打包依赖新增 ([de555c6](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/de555c65abeb927c3f66498b3b717312ae5612f4))
* 指定node版本14 ([4449832](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/44498329a492c21842e1fe80cf693ca72dd4341b))
* 指定类型 ([673ad87](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/673ad872c72cda43fb007c9eae8e6ba9dd47c8c6))
* 排序修改 ([d62ff8d](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/d62ff8d8321b788eebcddb535433368cea2ab779))
* 接口整体联调 ([d64d3db](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/d64d3db3b2bed7e628066fab8e7ca621dbe3492d))
* 接口联调 ([b0dc6cc](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/b0dc6ccf8a55488980782494c3106d38d9821541))
* 搜索空白页 ([f8a4e18](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f8a4e18514f8cf257bb753cbb6d36e84b732ae04))
* 改变引入方式 ([4f806f3](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/4f806f3e471d1a450af8b689eb79c91938731139))
* 数据结构兼容 ([6679074](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/66790746a658878fc0aac8cc76c5858909a709e5))
* 文案修改 ([93193bc](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/93193bc06cc7055064b88186898e74749de541ad))
* 文案调整 ([cf42df8](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/cf42df8ee3445a60ba6a58d8b5e07ddceaff3948))
* 新增已过期状态 ([7c693fd](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/7c693fdff6cfd2e1b5fe1fbddc709e27151ce6b9))
* 更新ga ([9530cdc](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/9530cdc7685aa2eea6d7252fcd7356c0b66207a1))
* 校验调整 ([761f35c](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/761f35cf78f53e0d639e34ca9a18da4722973f04))
* 格式化 ([523dc1a](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/523dc1a968c429ec2441442d8f5d12ee01ae87ff))
* 添加提示文案 ([6be7103](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/6be7103f2b4947f489e7797efe54c2217b58ebd4))
* 表单校验定制 ([675b816](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/675b816d1af4d4915af5441bb5566defe4327d96))
* 规则调整 ([52737da](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/52737dad2f2315bf67311a4cdfd54487c1af5086))


### Bug Fixes

*  sentry 迁移 ([7debe39](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/7debe39c081fb880f54ec8268d623ba32bfeee40))
* （即商机为 0）的情况下，原本的购买页面修改为弹窗 ([5fa848c](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/5fa848c80caa3ef3378115e824f437d24c4a6b6b))
* 【丁香通GA4统计代码替换】 ([a422942](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/a422942905b0f5017052fbfd94869e16d78f523c))
* 【丁香通GA4统计代码替换】 ([99ddf70](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/99ddf70449f676894363db2c348559708fcd19c0))
* 【商家管理后台】补充智能商机购买页面替换逻辑 ([2fb9d75](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/2fb9d757f4077d6c3bfb7c2c8dd209ac21cfe754))
* antd v3 版本，调用api修改 ([e0ca292](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/e0ca292ec92b39bdcdf20308a5946aa4c1f0cabe))
* bug 修改 ([8e32c34](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/8e32c34e288654d58207a0d3f9e9e6d18896567c))
* bug 修改 ([a082a28](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/a082a28b71020401bc77bd464062395c35e103e2))
* bug修复 ([e7269dc](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/e7269dc2610827df7f2f87c6d9a37216ca7f1a2b))
* ckedit instancename ([fcf65fd](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/fcf65fdaaf7af6ba07ad55fb5e7947e9dedb0860))
* ckeditor instancename ([2d1d3f7](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/2d1d3f7c61e04d05de1fcb4d8151f5f144983e2e))
* hover 和点击交互共存 ([7d7775c](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/7d7775c9cc896e7a522a05a457726567ebbc55a1))
* hover 和点击交互共存 ([3d88fb8](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/3d88fb858250cb8d970660273054b40ee4c3800c))
* hover 和点击交互共存 ([9e777e1](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/9e777e10566b8438390be1ac5e3072bc7e878b81))
* hover 和点击交互共存 ([998c547](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/998c547262c618ba3a9a33222204c8fa9f97a802))
* hover 和点击交互共存 ([ecaed6f](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/ecaed6fa38d20fa44311d16213df066f0ab7d48a))
* 丁香学社【专题】新接口替换 ([014d2b8](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/014d2b8890fdca40d4199faed90caf3ebc9580fd))
* 丁香学社【专题】新接口替换 ([0a44099](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/0a44099154b9cad58fa1592864d7a5d17f4a9dc7))
* 丁香学社新接口替换 ([bf52e57](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/bf52e57b797eeb3821fad4c2f97a4595d5d8d294))
* 丁香学社新接口替换 ([6e5deb6](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/6e5deb69d2e1b84c2e3c7863a02a29df77d7c5d3))
* 丁香学社新接口替换 ([b068b9b](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/b068b9bbe001c090309debb42c991685400d3964))
* 丁香学社新接口替换，详情联调 ([ffce350](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/ffce3507f0d7bd6c4807502e9c5879255a69a586))
* 上传限制修改，历史bug ([2b31d3b](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/2b31d3b9aba6ef8810d387cd49bbd4d045c17a12))
* 产品提的优化 ([35590d4](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/35590d4393ddb91d8bcf29306b1452f77c9c047d))
* 会员按钮逻辑修改 ([43a49fd](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/43a49fd79ef6a507ef5a4ad22ced22efd21d8174))
* 修改询价回复的左侧样式 ([c77fda2](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/c77fda2cb9fdab8c9d83db86e42657bf54948435))
* 公开设置弹窗，新增取消按钮 ([f196f14](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f196f14749a96d6ea72b1adc5cb9e359157c0a79))
* 关闭eslint校验 ([2e57d7b](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/2e57d7b458c85ee66bb86cf06da8114965e690fc))
* 删掉旧的ga代码引用 ([da22d8c](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/da22d8cb28408b7a248374716e01c811108d4289))
* 删除.eslintignore配置 ([2ea1912](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/2ea19124471a76aee8cf37d46287cc6e1ba0a1f5))
* 删除加提示 ([71c1c9e](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/71c1c9e94c850ff59737de00947ad5f1370e08ae))
* 删除延迟提示 ([d1a5280](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/d1a5280c5eee53559be24a723ded4b1806e36ea3))
* 判断下来源页 ([017dd42](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/017dd4206b9c2d44dd1c70299b0255d4fd7ecfe1))
* 历史代码有bug，碰巧contacted 为 true，businessService为false就符合了 ([c93fcfd](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/c93fcfd4f6dd178e942c8d1c8cf997905587470d))
* 去掉“取消”按钮 ([1657bc5](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/1657bc59b5c1ff69907ea38f996ab9b8a64b5719))
* 去掉todo ([80fc98a](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/80fc98af88025a4de88ff2d04015c5d84f67561b))
* 发布时间写死 ([46863ea](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/46863eae1c2ddf78c0e5479c5faacb2641a9f81d))
* 后台「保留会员」的求购联系次数改为 20 条。 ([1bcf063](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/1bcf06374f44e80357e7fc265e4ab2c388a993c0))
* 商家信息接口 /api/v2/agency/agency/info；商家vip申请记录接口 /api/v2/agency/memberShip/applyInfo；商家vip到期提醒接口  /api/v2/agency/memberShip/alert 全部替换成新接口 ([e4f58a5](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/e4f58a511f23f975562bde257fffd5fa9f7b6772))
* 商家信息接口 /api/v2/agency/agency/info；商家vip申请记录接口 /api/v2/agency/memberShip/applyInfo；商家vip到期提醒接口  /api/v2/agency/memberShip/alert 全部替换成新接口 ([d708d1f](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/d708d1f8c43cbf66a7512488979d2b47349040f8))
* 商家端会员权益修改 ([fa67f3b](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/fa67f3b04537c275593449c8380294d86f520dc5))
* 学社新详情页 url 规则修改 ([af70454](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/af70454013e9e251e7d89bc744bfaccd2db2a6a5))
* 学社新详情页 url 规则修改 ([9e591a5](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/9e591a51d37d1e0a8c5ea5d772a77c3bac9d392f))
* 学社新详情页 url 规则修改 ([5b9d80e](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/5b9d80e184d681968ea2d40997501eefa4ded32b))
* 弹窗改成 hover 效果 ([d7b1823](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/d7b18236614bf57baee7206db5cf5e04d110db17))
* 打包环境升级 ([a7ed8c7](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/a7ed8c7f1ccb57b75c31ea383edcb5dc7d6cee62))
* 报名提交校验 ([2a9eb17](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/2a9eb17efdc7020f896bc37c0f719ea33d49b70a))
* 排序渲染 ([db9f22a](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/db9f22a763ffc48e7906eeac381b6513ad72714d))
* 接口格式修改 ([62d4739](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/62d47395e78a54b5f645f6825658a7eff9a4ea25))
* 接口格式修改 ([39717f6](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/39717f6dc311c9381847ad2c93aaf7229d6ada9d))
* 接口格式修改 ([a292083](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/a29208317266b0817122bcde4c58a07af5c45179))
* 接口返回兼容 ([a7bda11](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/a7bda112386b06dfa97e49d78ec0e09deb83deea))
* 接口返回兼容 ([7930e71](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/7930e71d81b136f5c719a61c877e2effc24b529e))
* 撤回全局商家信息接口修改 ([506717d](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/506717dbf5406dbeb08aa9a6f3dfc6880d3c1f2a))
* 新增会员等级 ([fbf2fc4](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/fbf2fc461327d0cf0ec37105112fd2570dd17a7d))
* 新增会员等级，会员续费逻辑调整 ([26ef6bc](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/26ef6bc77a7987d669362076dfb5b0e64b8d8dea))
* 新增会员等级，最高会员不允许升级了 ([882e242](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/882e2428588ccc023a9bb39574e7e762f64be797))
* 新增行bug ([f38065b](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f38065b8420b60c534b721e33d6eb59dec782c9c))
* 新用户信息接口静默提示 ([124f8f5](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/124f8f54f8eed4ce3c30e85d67f78fba47a719f8))
* 智能推荐交互修改，跳询价改为弹窗提示 ([12bb75b](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/12bb75bf8a2cf7b429e5f6f9938f4a0c4aa14e80))
* 智能推荐交互修改，跳询价改为弹窗提示 ([7f8cfee](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/7f8cfeeee00ab5e482f392d70ac0d0d9addb118c))
* 智能推荐交互修改，跳询价改为弹窗提示 ([7c5dbd6](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/7c5dbd60e7fdfe77b62568af7921633d3d47251a))
* 替换cdn路径 ([f901742](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f90174282b25a84152d4563d9330214bb2906d5c))
* 替换公告文案 ([2350d0d](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/2350d0d84afc77d654630d364e582903cc30c3d7))
* 替换新安全接口 ([a352017](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/a3520174ccfdbadb759a67ec51620508af6d8ab2))
* 样式修复 ([7bf03fd](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/7bf03fda1c7e84e33c664359f15d51095fead95f))
* 正在升级中的话，不展示升级按钮了 ([98beb24](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/98beb243d68ae0c580849d9f1df5ccfcfdd41bb1))
* 添加单件和批量产品发布限制 ([5d9f878](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/5d9f8786921125ec17f2658586bc48def6c07f44))
* 登录登出联调 ([c8b2f34](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/c8b2f3417ceca161aeef8311eb724fa95747023f))
* 登录登出联调 ([bc7abaf](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/bc7abaf70d2d5b6cf027a184c0bb62154ac66e55))
* 登录联调 ([69abe88](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/69abe8860c38e57a06e8d797296eb3814a5447f3))
* 登录联调 ([868903a](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/868903ad4ca16e778886cf58dfbc80321a85f0fa))
* 登录联调 ([4c3a40b](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/4c3a40b0dacbb3a54a18ec3b3e41aaca7d308743))
* 登录联调 ([b32165a](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/b32165a7d9c5b6643de6ae14711bf988c68e20de))
* 解决上传图片报错 ([3ad84de](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/3ad84de6485638011f5dc9bd7e9e95cd44671120))
* 解决上传图片报错 ([f531141](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/f53114178ff54431884776d6b3736e621c28e577))
* 讲师信息字段修改 ([5241d64](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/5241d64fca58df8fc7980e6854c6cff60bf6087b))
* 设置公开，接口联调 ([db0058f](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/db0058fdbc0dab8ebaf07b5178414fb0ee40c1a4))
* 设置公开，接口联调 ([e778d00](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/e778d008ea95c2d6a0fcc6790da6c751579008fe))
* 设置公开，接口联调 ([d0e271a](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/d0e271aa549ace496c4282106cae25fca86a4515))
* 试用报告查看页，添加公开报告的入口 ([61fb9e3](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/61fb9e35c3da78b5e60604e728ff08d9dc2c1ebe))
* 试用报告查看页，添加公开报告的入口 ([66b7629](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/66b76291ea30cab03b51103c572db4833742210b))
* 试用报告查看页，添加公开报告的入口 ([05f41a4](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/05f41a4a5b015506b2105092b07e198e86a49819))
* 退出登录联调 ([15830c9](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/15830c9f63574b042086d7fda4c2adb07a35589c))
* 退出登录联调 ([5f1e6b4](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/5f1e6b4599c59d9b7068c7543289a943614c1edf))
* 配置代理 ([c3b8b73](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/c3b8b73e9815636298c54608e8a55d8c95a0fc8c))
* 配置代理 ([b9c35eb](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/b9c35eb6575d104b2b23017c0fa5b3bb8873e3c1))
* 重名产品链接修复 ([e8ea8ef](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/e8ea8ef6f3e5d281a7c64b76a0010158d48eb3ce))
* 重复校验，loading状态需要重置掉 ([eaae8ca](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/eaae8caf9f5100a004498046cf7ebcda25d80303))
* 重新声明一个 window._gaq 变量，覆盖掉旧的 ga 打点变量 ([40f327d](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/40f327d3419d4018cad1721e6f2bc08596ad1f3e))

### [0.1.34](https://gitlab.dxy.net/f2e/biomart_cms_v2/compare/v0.1.33...v0.1.34) (2022-05-09)


### Features

* 隐藏banner ([99823bf](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/99823bf5f7910c9b75d35db5a4ac455117e60c6c))


### Bug Fixes

* 导出联系人 ([4a6889c](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/4a6889cbd19a4d94cfe68431965e0c766249ebfd))
* 导出联系方式 ([1eefa2d](https://gitlab.dxy.net/f2e/biomart_cms_v2/commit/1eefa2d54660b66756b0f4a54ca68f7b51bf38fe))

### [0.1.33](https://gitlab.dxy.net///compare/v0.1.32...v0.1.33) (2021-08-05)


### Bug Fixes

* 删除直播入口 ([9aa1fdf](https://gitlab.dxy.net///commit/9aa1fdf5d8d2d65269f45c17b777e4ef56287d16))

### [0.1.32](https://gitlab.dxy.net///compare/v0.1.31...v0.1.32) (2021-04-25)


### Bug Fixes

* 修改联系号码和联系人、删除「促销产品推广服务」 ([d94098c](https://gitlab.dxy.net///commit/d94098c37a586c455d1e3bcd27b6c4fb65ab9416))

### [0.1.31](https://gitlab.dxy.net///compare/v0.1.30...v0.1.31) (2021-02-24)


### Features

* 加入批量咨询tag ([7bded49](https://gitlab.dxy.net///commit/7bded49e0e500c8dc7cd22c0ded79ca9172d757b))


### Bug Fixes

* 批量咨询tag与消息对齐 ([b555fb4](https://gitlab.dxy.net///commit/b555fb41fb4d46cf51610c66c7ec65ff94956c96))
* 高级设置文案修改 ([42a4f24](https://gitlab.dxy.net///commit/42a4f241b0ed99b7666e19a2c1b523d6e596a091))

### [0.1.30](https://gitlab.dxy.net///compare/v0.1.29...v0.1.30) (2021-01-28)


### Bug Fixes

* 上传文件多次上传显示问题 ([752085d](https://gitlab.dxy.net///commit/752085d54a4d4c3781b591306174773b8524b9cd))
* 修复上传文件返回格式为数组的情况 ([94dbd32](https://gitlab.dxy.net///commit/94dbd3294a156b71a942a668f6c63a56e2863f9e))
* 多个上传文件索引修改 ([8132935](https://gitlab.dxy.net///commit/813293592c735899e5a09677192b8f4efc8e6e6a))

### [0.1.29](https://gitlab.dxy.net///compare/v0.1.28...v0.1.29) (2021-01-25)


### Features

* 录播管理列表修改，创建和编辑新增视频课件及视频章节 ([46c7ec3](https://gitlab.dxy.net///commit/46c7ec372843f3865e34a645ca2607366bf5368b))


### Bug Fixes

* 修复50条视频章节验证卡顿的问题 ([fa37dbd](https://gitlab.dxy.net///commit/fa37dbd61e006be0fca45be0c23ea9da11920c19))
* 修复在beforeUpload中使用async的问题 ([59082e5](https://gitlab.dxy.net///commit/59082e5afdc802f2866c2c6c02b443fc991cfbf3))
* 修改上传课件限制文件大小，与后端联调（后期改回） ([9f29025](https://gitlab.dxy.net///commit/9f2902580031a0c06f7db3c3ba4d7c904ab7c534))
* 修改视频课件上传方式 ([b9dc3d3](https://gitlab.dxy.net///commit/b9dc3d353524d15ba4d2d9612cfc751e24c2d8f4))
* 去掉无用的注释 ([47861d3](https://gitlab.dxy.net///commit/47861d315a10a8d0792e5442afbecc6049cc1f6b))
* 封装AdditionList组件，修改可上传视频数为200 ([326dec7](https://gitlab.dxy.net///commit/326dec74c1247ea55ad7f949c3b337d41ff5519e))
* 将Upload组件右侧的tips改为absolute并放到Upload外层 ([293ce9f](https://gitlab.dxy.net///commit/293ce9fe081ddaff14f934aeeaa54ec970257380))
* 录播管理上传课件组件优化，新增上传中不可保存 ([ca0a8f6](https://gitlab.dxy.net///commit/ca0a8f67574d1e5bae407825d2014a5539913668))
* 文案修改 ([bf3468e](https://gitlab.dxy.net///commit/bf3468ea27bf338ed31a6b846ee7c8b057c4083a))
* 文案修改，去掉一些视频章节的判断 ([9204e15](https://gitlab.dxy.net///commit/9204e15938e8173c12bcf82e63564482d0b94266))
* 视频课件转码失败，Upload组件可点击范围修改。 ([c9b746a](https://gitlab.dxy.net///commit/c9b746a4466b3142f17d2926e70333413f16f7b0))
* 视频章节新增删除交互修改，上传课件回显原文件名 ([9ec9e24](https://gitlab.dxy.net///commit/9ec9e24e624fbe3aba6987b0d20c0d423d7b7bc7))

### [0.1.28](https://gitlab.dxy.net///compare/v0.1.27...v0.1.28) (2021-01-08)

### [0.1.27](https://gitlab.dxy.net///compare/v0.1.26...v0.1.27) (2021-01-05)


### Features

* 商家后台求购管理 ([ea5ac51](https://gitlab.dxy.net///commit/ea5ac51fbc09a9fbbb6629c92002bab8b427e18a))

### [0.1.26](https://gitlab.dxy.net///compare/v0.1.25...v0.1.26) (2020-12-16)

### [0.1.25](https://gitlab.dxy.net///compare/v0.1.24...v0.1.25) (2020-12-16)


### Bug Fixes

* 视频选择title显示 ([a4dbaf4](https://gitlab.dxy.net///commit/a4dbaf46d412265ca8f90ab3441d8e70dc4eb18a))

### [0.1.24](https://gitlab.dxy.net///compare/v0.1.23...v0.1.24) (2020-12-08)


### Bug Fixes

* 产品详情限制大于20字 ([308227a](https://gitlab.dxy.net///commit/308227a14cd0b3613e0750e5af38d8e665394f9e))
* 修改专题描述文案 ([81f84f1](https://gitlab.dxy.net///commit/81f84f1f21c351bfafe10a5eff1dff9d7456775c))
* 字数统计错误获取 ([ae844db](https://gitlab.dxy.net///commit/ae844dbc804417e5e07ba8e887584da00714d226))
* 字数限制不包括富文本 ([b3d3cb9](https://gitlab.dxy.net///commit/b3d3cb93f86daadfb8f171a56b393d48aadfb48f))

### [0.1.23](https://gitlab.dxy.net///compare/v0.1.22...v0.1.23) (2020-11-20)

### [0.1.22](https://gitlab.dxy.net///compare/v0.1.21...v0.1.22) (2020-11-20)

### [0.1.21](https://gitlab.dxy.net///compare/v0.1.20...v0.1.21) (2020-11-18)


### Bug Fixes

* eslint error ([183bd78](https://gitlab.dxy.net///commit/183bd78c1ae714f88ffb6bafb0948da54e317745))
* 修改接口参数 ([3284a27](https://gitlab.dxy.net///commit/3284a271a71f40317dd9a1288bb83c25d89f3e0e))
* 冒烟修改 ([e25ef39](https://gitlab.dxy.net///commit/e25ef39b3620d54c37747c9e32f99cb373d19eb6))
* 取消函数报错修改 ([ca44e20](https://gitlab.dxy.net///commit/ca44e208a1df75d93f31b337e7208f420d59d813))
* 处理多次提交 ([8833d0f](https://gitlab.dxy.net///commit/8833d0f135be2c2f0860b05450a3eeb1697575ef))
* 接口参数异常处理 ([84c6c1b](https://gitlab.dxy.net///commit/84c6c1b8b49cd5baf81ea87045263e8ed4814e47))
* 视频申请数处理 ([c712a56](https://gitlab.dxy.net///commit/c712a56230fb73e31820cdc375a4887b76fa22ee))
* 联调处理 ([5ab63fa](https://gitlab.dxy.net///commit/5ab63fa05ca2cf793d7df3f04f26453168c1649b))

### [0.1.20](https://gitlab.dxy.net///compare/v0.1.19...v0.1.20) (2020-11-10)


### Bug Fixes

* 用户信息认证文案修改 ([50c9f8c](https://gitlab.dxy.net///commit/50c9f8cbb6f7b94cf0934067deb7aafc42ad1a9b))

### [0.1.19](https://gitlab.dxy.net///compare/v0.1.18...v0.1.19) (2020-11-04)


### Bug Fixes

* 修改错别字 ([79ca8e6](https://gitlab.dxy.net///commit/79ca8e6781709b52df242789b81cd1b2e81cc6cf))

### [0.1.18](https://gitlab.dxy.net///compare/v0.1.17...v0.1.18) (2020-10-27)


### Bug Fixes

* 店内咨询询价对话处理 ([aeed319](https://gitlab.dxy.net///commit/aeed31973d5874a1e0683c34458f15755b41dc71))

### [0.1.17](https://gitlab.dxy.net///compare/v0.1.16...v0.1.17) (2020-10-26)


### Features

* 导出 ([ff9efe2](https://gitlab.dxy.net///commit/ff9efe2364f6ca6b10a0fabbe384a4d6ce60d814))
* 导出 ([1b1be7e](https://gitlab.dxy.net///commit/1b1be7e1276cfa765b8dbd8103401a581683305d))
* 导出 ([3c11610](https://gitlab.dxy.net///commit/3c1161042c961ba43f5bd7706a34bde449ec73ba))
* 导出 ([a0e4fdf](https://gitlab.dxy.net///commit/a0e4fdf45844b7a9785bc76f2fa72813fbd7a826))
* 导出联系人 ([95e698d](https://gitlab.dxy.net///commit/95e698d1648d90201bc448a56215f74d949c099d))
* 导出联系人 ([05586e8](https://gitlab.dxy.net///commit/05586e8686ae9ba2e6b417f5be4987017b1f1eb6))
* 导出联系对象 ([943841a](https://gitlab.dxy.net///commit/943841a4ed15259c89c9f1f3281141b93ad0b2c5))
* 导出联系对象 ([b825d66](https://gitlab.dxy.net///commit/b825d66598f4dfc6e92170fb1d1eb1eb5d542d02))
* 导出联系对象 ([fe6f686](https://gitlab.dxy.net///commit/fe6f6869b6487e6635fbf22122573aec5a7c73c4))
* 导出联系对象 ([2aa46e0](https://gitlab.dxy.net///commit/2aa46e06d43531e17f608f6cacb7e5f7ddcc182e))
* 导出联系对象 ([79edc60](https://gitlab.dxy.net///commit/79edc60f007ff22217cd1d3f8deca541c8e2e13c))
* 导出联系对象 ([71ddc04](https://gitlab.dxy.net///commit/71ddc04fe90d1d1deddfd2ceaeef2e5e0a33ac56))
* 导出联系对象 ([2804f50](https://gitlab.dxy.net///commit/2804f50f67c8001293e28e4f107cc96e6e148083))
* 导出联系对象 ([2387999](https://gitlab.dxy.net///commit/23879995dad1f9b0200fd6b674cdb77cf5628711))


### Bug Fixes

* 信息认证文案修改，radio阻止check样式“ ([60516e7](https://gitlab.dxy.net///commit/60516e74f96e4175c447e12ddaa10ffba07de2cb))
* 列表重复请求问题 ([1d42468](https://gitlab.dxy.net///commit/1d42468c736a576c7057ed98568edf143d161dd6))
* 审核通过未开启自定义，点击开启自定义，弹出确认框 ([f2c1f7b](https://gitlab.dxy.net///commit/f2c1f7bd9e14a927557ae0a689ba813e5a6cf39f))
* 导出后刷新列表 ([e250be3](https://gitlab.dxy.net///commit/e250be32e07b35c311ad6aeb4badaf3e277017de))
* 已上线且已通过列表才显示链接 ([f668645](https://gitlab.dxy.net///commit/f668645a52b9486baae7427151fdf2968fa668a4))
* 录播视频上传添加用户信息认证表单 ([057401d](https://gitlab.dxy.net///commit/057401de9fe2559b5fea0086c39c0031528ad1e1))
* 抗体分类克隆性产品属性表单修改 ([25f85b4](https://gitlab.dxy.net///commit/25f85b4ee1221416a6834816ad5d1b6331096d34))
* 点击这周也关闭弹窗 ([01995c8](https://gitlab.dxy.net///commit/01995c847c7d85ec99f55bbf75f53a73efc74d15))
* 点击这周也关闭弹窗 ([bfbbbd8](https://gitlab.dxy.net///commit/bfbbbd81f493967d35435d59817d3e2968f5c0f1))
* 用户信息认证项完善 ([7d0d95c](https://gitlab.dxy.net///commit/7d0d95c4538d53dd6dc0388fcfad3dd052cce077))
* 编辑页展示提示规则 ([0995fe3](https://gitlab.dxy.net///commit/0995fe3ecc7f6451c8111cbde6bba22ba5ae718c))
* 联调问题修改 ([220a8fd](https://gitlab.dxy.net///commit/220a8fd946182a053d31badc2af9c514f241c21f))
* 表单完善 ([5a58022](https://gitlab.dxy.net///commit/5a5802259f7c7e330e7427b5baed95d5b49165f6))
* 表单完善 ([c896bfd](https://gitlab.dxy.net///commit/c896bfddaeabceadda260e71788230bc15bda480))
* 认证状态开启过就显示人数 ([421d61e](https://gitlab.dxy.net///commit/421d61ee4aab608e4558942e9ff83ea2c72eedeb))
* 认证状态开启过就显示人数 ([ecfa3a7](https://gitlab.dxy.net///commit/ecfa3a79d3b8b1aa8a145495e5425963d3003466))
* 默认值传递 ([dc22a72](https://gitlab.dxy.net///commit/dc22a729909a2529a0b799c0c8cd2c8005365a15))

### [0.1.16](https://gitlab.dxy.net///compare/v0.1.15...v0.1.16) (2020-10-15)

### [0.1.15](https://gitlab.dxy.net///compare/v0.1.14...v0.1.15) (2020-10-15)


### Bug Fixes

* 文案修改 ([45382aa](https://gitlab.dxy.net///commit/45382aa30af4b52b39f1e3cd867ea0b55b4ab48e))
* 文案微调 ([c18043f](https://gitlab.dxy.net///commit/c18043fd97059f284c1c968c15061b5c4ff6df9a))
* 询价设置逻辑调整 ([89d9500](https://gitlab.dxy.net///commit/89d9500daff39f298f2a848eddd0e5fc2ddca794))
* 默认选中基础设置，引导语添加默认文案 ([ea5d6f4](https://gitlab.dxy.net///commit/ea5d6f481a76a94534a2dfcc6b5b01e4a47b692f))

### [0.1.14](https://gitlab.dxy.net///compare/v0.1.13...v0.1.14) (2020-10-13)


### Bug Fixes

* 询价详情名片样式修改；首页图表参数没传导致图表报错问题处理 ([0ac9f9f](https://gitlab.dxy.net///commit/0ac9f9fbd7f043a2ba313f6dc7a8a390d2e4ca58))

### [0.1.13](https://gitlab.dxy.net///compare/v0.1.12...v0.1.13) (2020-10-12)


### Bug Fixes

* *号处理 ([6ad85d0](https://gitlab.dxy.net///commit/6ad85d00d075f65ef84aaca3a5e5cbca69a97578))
* 修改文案 ([5f5f68b](https://gitlab.dxy.net///commit/5f5f68b4d099b005034438cdf1c09ed2c681018a))
* 样式微调 ([4ab791f](https://gitlab.dxy.net///commit/4ab791f444a2acabf3d24b4f0d2dde21f3b3f1e6))
* 添加详细介绍页面地址 ([334313b](https://gitlab.dxy.net///commit/334313bd95fb97a6151c02e4a7b060e154daa235))
* 设计走查调整 ([ec4ea87](https://gitlab.dxy.net///commit/ec4ea876f56e90492fde3c942f10642c1f03a8cf))

### [0.1.12](https://gitlab.dxy.net///compare/v0.1.11...v0.1.12) (2020-09-10)


### Bug Fixes

* 修改丁香学社分类 ([3b1c25c](https://gitlab.dxy.net///commit/3b1c25ca3b2614fe824ccac27f65fa7dcf70f623))

### [0.1.11](https://gitlab.dxy.net///compare/v0.1.10...v0.1.11) (2020-09-01)

### [0.1.10](https://gitlab.dxy.net///compare/v0.1.9...v0.1.10) (2020-08-19)


### Features

* 版权声明 ([d86c3d1](https://gitlab.dxy.net///commit/d86c3d17856b41d5a37b31608355d23b42b4a0c2))
* 联调 ([ff636e4](https://gitlab.dxy.net///commit/ff636e4d2da9b50bd83123b1c4c257df8ad4c9ac))
* 联调 ([1911f0d](https://gitlab.dxy.net///commit/1911f0d62f1a914e2c603cdd49234d037547f33c))
* 文案调整 ([caff3a8](https://gitlab.dxy.net///commit/caff3a83131f3ed12f061f073999593de48f294a))
* 新增信息订阅页面 ([0b8a654](https://gitlab.dxy.net///commit/0b8a654b953e720d220bf46275e11f6f38b2092f))
* 直播&录播列表 ([0d9196c](https://gitlab.dxy.net///commit/0d9196c63f1756d46a1b8fd267dacd5730933d07))
* 直播&录播列表 ([fc8fc30](https://gitlab.dxy.net///commit/fc8fc30c7f11933ed33316728320994f59dee4b2))
* 直播&录播列表 ([d8ad07f](https://gitlab.dxy.net///commit/d8ad07f6806a6a5d78a23f858c1e1d493889100e))
* 直播列表 ([7181a3a](https://gitlab.dxy.net///commit/7181a3af3e849bdf03dfa838995fb8d4d2d92530))
* 直播列表 ([e8bdd5c](https://gitlab.dxy.net///commit/e8bdd5cb2d6379827570037bfb5c73a9ac78f801))
* 直播列表 ([f506c51](https://gitlab.dxy.net///commit/f506c515cb60e9ba759866c4f87730d45852e0e3))
* 直播列表 ([f730175](https://gitlab.dxy.net///commit/f7301750130609c199ab4882852d353671cb19d9))


### Bug Fixes

* 编辑态detail参数被覆盖 ([6f0c422](https://gitlab.dxy.net///commit/6f0c4225699490bd3b99822bdf1a3bec23caeec1))
* 表单重置 ([b956f62](https://gitlab.dxy.net///commit/b956f62fe258881ec3bf2743abe5e592b8544081))
* 处理大屏下的样式问题 ([1f9af28](https://gitlab.dxy.net///commit/1f9af28e1759e590e35e9c84617a6a7755a26659))
* 处理高亮和数字问题 ([5c52d90](https://gitlab.dxy.net///commit/5c52d908340bac62a4cabc5a705fd910b846acff))
* 促销产品列表标题跳转产品详情 ([2c05b71](https://gitlab.dxy.net///commit/2c05b71713095a20d097e9c6440b59df869731f8))
* 发布到丁香学社弹窗 ([8eb4ba8](https://gitlab.dxy.net///commit/8eb4ba859484d9e7a17c7f9e4277af5c3adf39d8))
* 更新图片链接 ([9c3d86c](https://gitlab.dxy.net///commit/9c3d86cf0cefc5923367e9ac111b89effe656bef))
* 链接 ([19d9274](https://gitlab.dxy.net///commit/19d9274c399d2103401abc5e9cf2c5db0adac851))
* 链接 ([a09166f](https://gitlab.dxy.net///commit/a09166f9e8f783cef0abe999bdd416fdf2925372))
* 列表参数 ([e325293](https://gitlab.dxy.net///commit/e325293b7af33ec3bc5848e4c06a354d464732ee))
* 默认展开丁香学社菜单栏 ([2758053](https://gitlab.dxy.net///commit/2758053463bc839e23fea41c1a951e07e2d9b679))
* 设计走查样式修改 ([03cd2e7](https://gitlab.dxy.net///commit/03cd2e751262cd6d162f494fa21b88970da96716))
* 时间判断 ([c31e36d](https://gitlab.dxy.net///commit/c31e36d15140bc54617469b547c57531b2f2f248))
* 文案&兼容 ([fa40e81](https://gitlab.dxy.net///commit/fa40e81f9f99296bd5f185c3d72ca5c752cfdf38))
* 下拉框样式 ([dc4a154](https://gitlab.dxy.net///commit/dc4a1547dabcc3cb9b90487e405d418fcbd11555))
* 修改判断参数 ([9d57d0c](https://gitlab.dxy.net///commit/9d57d0ccf57e9d2d309e378fc85021cdd0435665))
* 修改商家logo样式 ([99b3b22](https://gitlab.dxy.net///commit/99b3b2256f918886e5b67368ec552d48e7c65f32))
* 修改文案 ([e05767e](https://gitlab.dxy.net///commit/e05767e485782bbc9226621a13712a093c6ae063))
* 样式 ([c55d508](https://gitlab.dxy.net///commit/c55d50867bb99a9b380667b38736997464ce9968))
* 样式问题 ([79ac64f](https://gitlab.dxy.net///commit/79ac64f0cef6f8c6e600c4e34928c731b97a44fe))
* 邀请卡展示内容 ([5f79096](https://gitlab.dxy.net///commit/5f79096d7ca6cd9eb20a1c3ea7f3f5fd105a8ecd))
* 营业执照可删除 ([9e52810](https://gitlab.dxy.net///commit/9e52810eb4de07ceebc23f6a300796dc829cf38b))
* 走查样式 ([625c3c9](https://gitlab.dxy.net///commit/625c3c982b32e3729ec513d5d7206071da493aa6))
* a 标签error处理 ([eecd217](https://gitlab.dxy.net///commit/eecd21709221fe1a5ca24ef87d88593c5d434333))
* bug ([ba11af9](https://gitlab.dxy.net///commit/ba11af9c33df1ed32f2e1cecdbe69de0eec6dca3))
* ie兼容问题 ([9fdf661](https://gitlab.dxy.net///commit/9fdf661a8e2ecd9e4dba5952c61764ef2f76fc68))
* maskClosable false ([fbd0928](https://gitlab.dxy.net///commit/fbd092892f76966857fa3288d6d30785c927b89e))
* reset 机构资料营业执照审核 ([71172f1](https://gitlab.dxy.net///commit/71172f1938814ba7847a38b8e2770a3cc9c32381))

### [0.1.9](https://gitlab.dxy.net///compare/v0.1.8...v0.1.9) (2020-08-03)


### Bug Fixes

* 去除营业执照必填限制 ([45c90de](https://gitlab.dxy.net///commit/45c90de31fb335f847a92b8d2bad76e247322ee7))
* 任何时候都可以上传图片 ([087b84c](https://gitlab.dxy.net///commit/087b84c6760672944cab541c8a24fa886f8cf5a8))

### [0.1.8](https://gitlab.dxy.net///compare/v0.1.7...v0.1.8) (2020-07-28)


### Features

* 新增机构资料页 ([bed67ef](https://gitlab.dxy.net///commit/bed67ef0c64d1fa745311ea96ed1a555a8bd3d43))
* 询价增加小程序类型 ([b56e7c9](https://gitlab.dxy.net///commit/b56e7c9e752ed494e73fef1032f1f7f4ba4d1301))
* 样式调整 ([ffd0a59](https://gitlab.dxy.net///commit/ffd0a59578d8a67e90108f80ae50664d82d6552e))


### Bug Fixes

* 编辑联系人信息地址不能为空 ([d673172](https://gitlab.dxy.net///commit/d6731724a699a208bc58664e28df13f41d1c001f))
* 机构资料网址回显失败 ([3e06244](https://gitlab.dxy.net///commit/3e06244d1e72f1513b25a18ee5ef9f91301b9611))
* 内容营销视频上传 ([a9ad8f4](https://gitlab.dxy.net///commit/a9ad8f4425fd6d16bd76533fefcb405b02c605aa))
* 内容营销视频上传修改大小显示200MB，增加错误提示 ([2117e60](https://gitlab.dxy.net///commit/2117e601b77c0f87680e8be267bbd5ce7ac40b6c))
* 人民币标志不一致导致发布失败 ([e759e10](https://gitlab.dxy.net///commit/e759e108272a0251c880f1e31ca40dedd373b505))
* 日期选择弹窗未跟随页面滚动 ([b1561b0](https://gitlab.dxy.net///commit/b1561b0e16765831a6a76297475f87fa304cb357))
* 修复浏览商铺和折叠按钮位置 ([79b63f6](https://gitlab.dxy.net///commit/79b63f67c794817ae08d0703992c637900fafbb9))
* 修改专属客服文案 ([61b76f0](https://gitlab.dxy.net///commit/61b76f0de60f7b639c48a7d11925a519f095b5f0))
* ie兼容性问题 ([5abf29f](https://gitlab.dxy.net///commit/5abf29f066b9c1e088b53b6d61f4216668d58b55))

### [0.1.7](https://gitlab.dxy.net///compare/v0.1.6...v0.1.7) (2020-07-13)


### Bug Fixes

* 批量修改产品图片失败 ([4a8a667](https://gitlab.dxy.net///commit/4a8a667f1b152b5808186db634251be5609ad12d))

### [0.1.6](https://gitlab.dxy.net///compare/v0.1.5...v0.1.6) (2020-07-07)


### Features

* 图片裁剪支持比例设置 ([341ad61](https://gitlab.dxy.net///commit/341ad614225b9916efa9a1f4e696342d527f2bf7))
* 图片尺寸大小修改为裁剪后大小 ([fff4db0](https://gitlab.dxy.net///commit/fff4db064e645708d7b3e11db3e4d5568abc94db))
* 文案调整 ([78382f6](https://gitlab.dxy.net///commit/78382f69770eb6b503847dcbfab3e5b717c4f0af))
* 新增编辑视频页面 ([2df70c4](https://gitlab.dxy.net///commit/2df70c46e512dd2626051469468332700d5583a4))
* 新增互动营销页面 ([bc69ac8](https://gitlab.dxy.net///commit/bc69ac8929f8ad2fcb5178300ab4abd064043e10))
* 新增视频上传demo给后端测试用 ([6cba8fb](https://gitlab.dxy.net///commit/6cba8fbd2c78694842375bf59933819eaea93c9c))
* 样式调整 ([6a5a25d](https://gitlab.dxy.net///commit/6a5a25d92e93e18972b8d916a5416f62586858f4))


### Bug Fixes

* 处理review和warning ([54bc469](https://gitlab.dxy.net///commit/54bc4690d875bbb47b7e53b65dbdd9452bd9e6df))
* 批量推荐产品至首页的超限错误提示处理 ([6124fc8](https://gitlab.dxy.net///commit/6124fc85d4ef4e2d58cb6d2f84de38dc408958e2))
* 删除无用注释；更新富文本时间戳 ([94d81e5](https://gitlab.dxy.net///commit/94d81e5a05e9d6f239a2197e64c83ec8aef13266))
* 设计走查问题处理 ([34c4bf1](https://gitlab.dxy.net///commit/34c4bf16dcd4439666c0bfc96e3f0ea0d7c328a8))
* 设计走查问题处理 ([340ee19](https://gitlab.dxy.net///commit/340ee192fd2fcf9ce3f23629ae2b691f44bb6604))
* 添加打点 ([fbfbf8e](https://gitlab.dxy.net///commit/fbfbf8e275300986ac30bc2da2461383c880820c))
* 修复视频上传大小拦截错误提示的参数名 ([97c6427](https://gitlab.dxy.net///commit/97c6427934b79b3a65357191ec7e5d88f86cac0b))
* 修复eslint error ([c7e589d](https://gitlab.dxy.net///commit/c7e589d4440c82f735493b9745601676d0e0cfea))
* 修复eslint error ([3a9c007](https://gitlab.dxy.net///commit/3a9c0075773f7a50f9de9a7000031a5134d41753))
* 修改分类选择框逻辑；设计走查问题 ([8a2221c](https://gitlab.dxy.net///commit/8a2221c9b505fa702a857162460888a161e0d06a))
* 修改关联产品高度 ([2bade0d](https://gitlab.dxy.net///commit/2bade0df2429c0d207b092e069fd6db6f60a1f30))
* 修改关联产品逻辑 ([020ee04](https://gitlab.dxy.net///commit/020ee04c8514738aa74305c9ba32d65f97456323))
* 修改上传视频demo获取sign参数 ([4311e5f](https://gitlab.dxy.net///commit/4311e5f850778d80db2055f6ac7991d2bd5d435b))
* 修改上传视频demo获取sign参数 ([ec8278f](https://gitlab.dxy.net///commit/ec8278f66b983da05d649abb8fdcf9dc19e7d4ed))

### [0.1.5](https://gitlab.dxy.net///compare/v0.1.4...v0.1.5) (2020-07-01)


### Bug Fixes

* 图片裁剪后体积增大导致上传失败 ([ef0654e](https://gitlab.dxy.net///commit/ef0654eb3bf506dfdfb85ecaaa77a3ab90b13259))

### [0.1.4](https://gitlab.dxy.net///compare/v0.1.3...v0.1.4) (2020-06-29)


### Bug Fixes

* 编辑产品后产品资料信息丢失 ([1ccbd32](https://gitlab.dxy.net///commit/1ccbd32f8b27a4a1b53c6d1ed562b3d214afae94))
* 订单详情编辑发货信息后发票信息丢失 ([9518a99](https://gitlab.dxy.net///commit/9518a991136f9baf6c3b1676da374e552e204b56))
* 附件得分未计算 ([b6e8b01](https://gitlab.dxy.net///commit/b6e8b01db72dfb3bdd5ecc47c4add80fdbe935f4))
* 批量导入失败信息对应行数错误 ([015a759](https://gitlab.dxy.net///commit/015a75904e04bc75db6e748b0b95fe7c3131bcec))

### [0.1.3](https://gitlab.dxy.net///compare/v0.1.2...v0.1.3) (2020-06-29)


### Features

* 产品列表搜索结果数量调整 ([7aed50c](https://gitlab.dxy.net///commit/7aed50c8291aa588500e1df44617119223d28c3b))
* 更新package-lock.json ([2282217](https://gitlab.dxy.net///commit/22822178642f7058cf8cd3c57ce4ed99140ea678))
* 更新package-lock.json ([c30366d](https://gitlab.dxy.net///commit/c30366daf3d7a7800bdb676ab7c7ce88016dbfff))
* 文案调整 ([ea65996](https://gitlab.dxy.net///commit/ea659962890a16924bc0a16688379db9a3298b2d))
* 新增订单管理页 ([ed3df23](https://gitlab.dxy.net///commit/ed3df23e6ed34783703a78fd35aa8a824dd3a0e9))
* 新增订单详情页 ([b020476](https://gitlab.dxy.net///commit/b020476552ad51c0ac0ca884cd35dccecc9ecc01))


### Bug Fixes

* 产品资料属性初始值错误 ([eb5a773](https://gitlab.dxy.net///commit/eb5a7738b23579cf29651ddac44ee6c0a917c065))
* 面包屑缺失 ([c676096](https://gitlab.dxy.net///commit/c676096c0bf2c25a37612d70905dcba7ccc7bf1f))

### [0.1.2](https://gitlab.dxy.net///compare/v0.1.1...v0.1.2) (2020-06-23)


### Bug Fixes

* 多级路由下智能客服跳转错误 ([6903d28](https://gitlab.dxy.net///commit/6903d28bb20b8971368a38041965693dcbaffbf7))

### [0.1.1](https://gitlab.dxy.net///compare/v11.2.3...v0.1.1) (2020-06-23)


### Features

* 版权声明组件抽离为公共组件 ([8f643fd](https://gitlab.dxy.net///commit/8f643fd2f8115e4e04653d57832c59cc65cf513f))
* 产品发布添加产品图裁剪功能 ([b0490e7](https://gitlab.dxy.net///commit/b0490e75436c33154146b91cd53ba512016b646e))
* 产品发布支持品牌搜索功能 ([367d497](https://gitlab.dxy.net///commit/367d497534791fef441d926f8d47e65c96181adf))
* 产品分数计算规则调整 ([cd122f9](https://gitlab.dxy.net///commit/cd122f9261211a7afa65ed775fa8d8058c9f9a94))
* 产品价格检验规则调整 ([3a1b0ae](https://gitlab.dxy.net///commit/3a1b0aefd49d402776f5b6d2c8ceb0c43b3be1e1))
* 代码格式化 ([6e0982e](https://gitlab.dxy.net///commit/6e0982ed8da0e47b636e98ead54409ce4cf9c705))
* 代码优化 ([c233614](https://gitlab.dxy.net///commit/c233614a16bb59aec173f4b63d1d1eefff11c801))
* 调整发布提示 ([8118f70](https://gitlab.dxy.net///commit/8118f70d83fbcaba827377b90070bd8f83a2187d))
* 调整axios默认错误处理逻辑 ([e0e7e0a](https://gitlab.dxy.net///commit/e0e7e0ae84409daf7e1edb2c60f912715d92e885))
* 更新CKEditor时间戳 ([f400285](https://gitlab.dxy.net///commit/f400285c2d3a15b3402ae14483b3cf3c740e9074))
* 更新less样式变量 ([65c9888](https://gitlab.dxy.net///commit/65c988809e7aee382474e31db9d26e8facfc0e9b))
* 删除测试代码 ([e87c617](https://gitlab.dxy.net///commit/e87c617099706de6bc87b2bc32567dd1bf331c4d))
* 删除console代码 ([5a7b31b](https://gitlab.dxy.net///commit/5a7b31bcca2081b47d2b07e2bd1fdcede253638c))
* 上传图片组件支持裁剪功能 ([3cb3bc7](https://gitlab.dxy.net///commit/3cb3bc70208f63c9153b17e80d8d1d80abb63874))
* 文案调整 ([ee01f5d](https://gitlab.dxy.net///commit/ee01f5dd7f25e4cbe8a993badb9410d379634a52))
* 文案调整 ([8553e2a](https://gitlab.dxy.net///commit/8553e2a9cd211d1a3dba47c000d41f957741b9c3))
* 新增产品图片规范弹窗 ([954f09a](https://gitlab.dxy.net///commit/954f09a5586a9d71b2fd4c3a8ca2f23004c7e8ed))
* 新增穿梭框组件 ([2445709](https://gitlab.dxy.net///commit/2445709902228060d5d5d9e9d814655ebc2fad10))
* 新增单件产品发布页面 ([174f06d](https://gitlab.dxy.net///commit/174f06d55e0b9a2a9c3ab4d5187abe095549c37f))
* 新增丁香通搜索指数弹窗 ([db6ddac](https://gitlab.dxy.net///commit/db6ddac41dfb13726dcd40a030919779d1bc30a1))
* 新增工具方法获取级联路径 ([652b487](https://gitlab.dxy.net///commit/652b487723f90ac78923b2a707f62c3b87c6550f))
* 新增工具方法判断是否为医疗器械 ([040b1dd](https://gitlab.dxy.net///commit/040b1dd75e6831333b59de1729ea277e062c52cc))
* 新增批量产品发布页面 ([a78becf](https://gitlab.dxy.net///commit/a78becf5e01dada0d4cd13eba47503798cb0f9f7))
* 新增品牌选择组件 ([79f5730](https://gitlab.dxy.net///commit/79f5730f948c1ffe539c40aea756320dae170c5e))
* 新增商铺评分 ([a2ffd4f](https://gitlab.dxy.net///commit/a2ffd4f04bedb8dbd8f71b8994b0d54783567e7a))
* 新增商铺评分体系 ([857caa7](https://gitlab.dxy.net///commit/857caa7900d2ec981933706990b72d9387acad3f))
* 新增图片裁剪组件 ([297b7d5](https://gitlab.dxy.net///commit/297b7d59828cda4c58643633c0c479ac44726f85))
* 新增文件上传组件 ([3d4b9a1](https://gitlab.dxy.net///commit/3d4b9a1f839936c224c5ceed3737cd0f74923e86))
* 新增文件下载组件 ([d0f5d01](https://gitlab.dxy.net///commit/d0f5d01dffb100334299136d30163022ac2d224f))
* 修改业务组件路径 ([a637b46](https://gitlab.dxy.net///commit/a637b469b55dbaebe59e45433e871feb240a5cb3))
* 修改contentBlock title类型 ([6a5d72d](https://gitlab.dxy.net///commit/6a5d72d022d2dfc57ad4cc1d25a58592ebc6acab))
* 样式调整 ([6b517ef](https://gitlab.dxy.net///commit/6b517efd5c5052900653d8205157a6a2480ab6bb))
* 样式调整 ([780d0fa](https://gitlab.dxy.net///commit/780d0fab35a43f9e3d725a3a241f29c681920f16))
* 样式调整 ([96129e0](https://gitlab.dxy.net///commit/96129e0c3cd8896338ba527f011c7a38b4c3244b))
* 样式调整 ([8585fdf](https://gitlab.dxy.net///commit/8585fdf441b857cc9d84dddad25a874a396cb459))
* 样式调整 ([8f03dcb](https://gitlab.dxy.net///commit/8f03dcb54719f659c713e871994a7c61bb08975c))
* 样式调整 ([8a275fa](https://gitlab.dxy.net///commit/8a275fa0f178f610f4daffa49275204cd170f87c))
* 优化产品发布提示 ([4892855](https://gitlab.dxy.net///commit/4892855818ef95b95396058ebafda7124167191a))
* 优化价格输入组件 ([65c3edb](https://gitlab.dxy.net///commit/65c3edb775984b19a5ba624629a94ab7d5e6c1e3))
* 增加在线客服入口 ([f1011ab](https://gitlab.dxy.net///commit/f1011ab4bc1e9bffbe9f80353f9146890170129f))
* 重构产品试用组件 ([da35b9d](https://gitlab.dxy.net///commit/da35b9d5a68a084dcf26d6e5bfd34acb529aa574))
* 重写产品促销组件 ([6b907c0](https://gitlab.dxy.net///commit/6b907c090e1c850a63abb602c6dd2fdb7f830e3e))
* axios支持multipart/form-data类型数据 ([474052d](https://gitlab.dxy.net///commit/474052df99731afe7ea7f104f86cf7dda2a90ab4))


### Bug Fixes

* 保存产品后产品发布状态错误 ([8e36798](https://gitlab.dxy.net///commit/8e36798c78e0a54d8a91c15193c39ec4492809d5))
* 变量命名与关键字冲突 ([3e53e2e](https://gitlab.dxy.net///commit/3e53e2ea5d81d2aef7fde51474bf9e864f07e4be))
* 产品促销表单数据丢失 ([ebf1152](https://gitlab.dxy.net///commit/ebf1152e3717877e4b154a967eb25b49bd0d61c0))
* 产品链接校验为空判断 ([9b10cb1](https://gitlab.dxy.net///commit/9b10cb1a80c282cda33fff633f5be0b8c1dcd47f))
* 促销表单校验失败未出现提示 ([4666a4e](https://gitlab.dxy.net///commit/4666a4e0d4909d4b301ae35440e3a81c1559b2e8))
* 点击图片裁剪边缘后无法保存 ([c650850](https://gitlab.dxy.net///commit/c65085029a2b597f0ec4b2b8e9437990b8ad41ad))
* 订购记录列表判断微信支付导致页面出错 ([8b88fc7](https://gitlab.dxy.net///commit/8b88fc796063ab997998c1d0f963feca5441f408))
* 发布文章可重复提交 ([9c0677c](https://gitlab.dxy.net///commit/9c0677cb0d578d317206c53f8b6a6ae92191da26))
* 价格组件未设置最小值 ([6ab3902](https://gitlab.dxy.net///commit/6ab3902f1d5c7a3f2f0c84bdcf7b0dd77a2244d1))
* 可重复提交产品信息 ([92d22e7](https://gitlab.dxy.net///commit/92d22e732681c7c2ff5342652eb71f1362a251f0))
* 历史试用数据，报告可查看 ([019bf21](https://gitlab.dxy.net///commit/019bf212becb52ec25a8d5903bc3e8a8f91c186c))
* 联系商机成功未提示 ([22611fe](https://gitlab.dxy.net///commit/22611fe20c022bac9d55abd56e498877151d698f))
* 批量上传提示无权限后自动跳转 ([7e5a9bf](https://gitlab.dxy.net///commit/7e5a9bf12ee7e294f06c7d3fff3f1826d61d0c67))
* 缺少价格类型导致无法发布产品 ([74f7d55](https://gitlab.dxy.net///commit/74f7d555c2c0380248ad56c7713104cab97ac20f))
* 删除多余问号 ([338c364](https://gitlab.dxy.net///commit/338c3646132570b26bc8bda84d8a4069c331dbe2))
* 上传失败loading状态未重置 ([9380d63](https://gitlab.dxy.net///commit/9380d638ca52b244f3641ed98b2e9ba99f99dc24))
* 升级提示判断条件错误 ([12b3cf9](https://gitlab.dxy.net///commit/12b3cf9d1f0874f1d79a423d28865c78f7fd2306))
* 搜索指数弹窗样式覆盖 ([37f2c57](https://gitlab.dxy.net///commit/37f2c57bfbd20232736a387acb935be4ffdd2bdd))
* 添加在线客服入口链接 ([437d3f5](https://gitlab.dxy.net///commit/437d3f5219934f1dae83e6f5cd47e3fded96826f))
* 文件上传校验失败仍旧可以上传 ([ff3cc26](https://gitlab.dxy.net///commit/ff3cc266605168eeaa50ae1ef56be707ee964c9a))
* 我的商铺未显示高亮 ([2b0d4c9](https://gitlab.dxy.net///commit/2b0d4c942ec3733cf34471b2d5ee5be347478a17))
* 下载模板链接错误 ([d4e7678](https://gitlab.dxy.net///commit/d4e76787d290e6aba5912623f24771dc224cf4b2))
* 修复ua账户配置错误问题；还原用ga.js记录数据 ([0080f81](https://gitlab.dxy.net///commit/0080f81b5c8fe113536d27c5d26ed32e9b302862))
* 修改会员套餐文案 ([57f6d10](https://gitlab.dxy.net///commit/57f6d105b55299d24254cb331f72bd54ef545087))
* 修改会员中心服务套餐 ([8b7097f](https://gitlab.dxy.net///commit/8b7097ff653ab50efbc678e10d8307b66a86789e))
* 询价消息对话xss处理，br标签要渲染为换行 ([03c91f3](https://gitlab.dxy.net///commit/03c91f325321a173c2d86f8e87aa316395cace2f))
* 样式冲突 ([605b2c0](https://gitlab.dxy.net///commit/605b2c0b362839070ce523948f6e40c049cb64f7))
* 在线客服修改为智能客服 ([80fe2e8](https://gitlab.dxy.net///commit/80fe2e81c42c676bbb54d92f6e9dab783d071ed2))
* 智能推荐显示多余提示 ([b073d2d](https://gitlab.dxy.net///commit/b073d2daa5c7f242845d3e5303b224e977fa6168))
* axios统一错误处理导致执行逻辑错误 ([70bada8](https://gitlab.dxy.net///commit/70bada82bd790f240944debb96642f123d5e14f3))
* CKEditor IE兼容性问题 ([5a0c17b](https://gitlab.dxy.net///commit/5a0c17b14cbeb0d04de8a14944c4557695004092))
* CKEditor未更新 ([70dcf68](https://gitlab.dxy.net///commit/70dcf6830be44d000677c69a3f9b16bac7ec06c6))
* css样式覆盖 ([6fc0a0c](https://gitlab.dxy.net///commit/6fc0a0c03b6791995454c8723cc92ee59d431f49))
* IE11裁剪兼容性问题 ([c9e2e5a](https://gitlab.dxy.net///commit/c9e2e5a3bec0e6192e2fcc0dac02b40d105317ad))
* IE兼容性问题 ([6a3a73d](https://gitlab.dxy.net///commit/6a3a73dce7095bcec40b9a1f0b0080b8d64b90d6))
* IE浏览器页面滚动闪烁问题 ([251a739](https://gitlab.dxy.net///commit/251a7391082ba8d94e98b4a0e5f3b655ff135fcd))
