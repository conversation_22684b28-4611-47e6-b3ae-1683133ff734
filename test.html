<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <title>丁香园管理控制台</title>
  <link href="https://assets.dxycdn.com/app/tong/css/admin/style.css?t=1607590596101" rel="stylesheet"
    type="text/css" />
  <link href="https://assets.dxycdn.com/app/tong/css/admin/table.css?t=1607590596101" rel="stylesheet"
    type="text/css" />
  <link rel="shortcut icon" href="https://assets.dxycdn.com/app/biomart_dynamic/img/favicon.ico?t=1607590596101"
    type="image/x-icon" />
  <style type="text/css">
    <!--
    .fixed {
      position: fixed;
      top: 30%;
      left: 40%;
      background: #fff;
      color: #000;
      z-index: 1001;
    }
    -->
    input
    {
    border:
    1px
    solid
    #dddddd;
    background:
    #ffffff;
    border-radius:
    3px;
    }
  </style>
  <!--[if IE]>
    <style type="text/css">
    * html .fixed{position:absolute;left:40%;top:expression(eval(document.body.scrollTop + document.body.offsetHeigh/3));z-index:1001;}
    </style>
    <![endif]-->
  <script src="https://assets.dxycdn.com/core/jquery/1.9.0-min.js"></script>
  <script type='text/javascript' src='https://assets.dxycdn.com/app/tong/js/dwrcore1.js'></script>
  <script src="https://assets.dxycdn.com/app/tong/js/util.js?t=1607590596101"></script>
  <script src="https://assets.dxycdn.com/app/tong/js/info.js?t=1607590596101"></script>
  <script src="https://assets.dxycdn.com/app/tong/js/date.js?t=1607590596101"></script>
  <script src="https://assets.dxycdn.com/app/tong/js/scriptaculous/prototype.js?t=1607590596101" type="text/javascript">
  </script>
  <script src="https://assets.dxycdn.com/app/tong/js/scriptaculous/effects.js?t=1607590596101" type="text/javascript">
  </script>

  <script src="https://assets.dxycdn.com/docs/plugin/dxy_dq_system/dq-1.2.js?t=1607590596101"></script>
  <script type="text/javascript">
    var $ = jQuery;
    var dataPageId = "";
    var setIntervalHandle;

    function intervalFn(pid, cid) {
      if (window.searchLoad) {
        clearInterval(setIntervalHandle)
        window.searchLoad(pid, cid)
      }
    }

    function isFunction(it) {
      return Object.prototype.toString.call(it) === '[object Function]';
    }
    if (initDaPageId && isFunction(initDaPageId)) {
      var initDaPageId_old = initDaPageId;
      initDaPageId = function (pid, cid) {
        initDaPageId_old(pid, cid);
        dataPageId = pid;
        window.dgload && window.dgload(pid, cid);
        if (window.searchLoad) {
          window.searchLoad(pid, cid)
        } else {
          setIntervalHandle = setInterval(function () {
            intervalFn(pid, cid)
          }, 500);
        }
      }
    } else {
      var initDaPageId = function (pid, cid) {
        dataPageId = pid;
        window.dgload && window.dgload(pid, cid);
        if (window.searchLoad) {
          window.searchLoad(pid, cid)
        } else {
          setIntervalHandle = setInterval(function () {
            intervalFn(pid, cid)
          }, 500);
        }
      }
    }

    window.dgload = function (pageId, cid) {
      $(function () {
        var dq = new dqSystem({
          debug: 0,
          // debug: window.location.hostname.indexOf('.net') !== -1 ? 1 : 0,
          // advImgSrcAlias: "src_retina",
          cookieid: cid,
          pageId: pageId // pageId
        });
        dq.init();
        window.onScroll;
      });
    }
  </script>
</head>

<body>
  <div class="container">
    <div class="topLine">&nbsp;</div>
    <div class="top">
      <div>您好,dxy_7rbthguq ! [<a
          href="https://www.biomart.net/logout.do?done=https://www.biomart.net/j_acegi_logout"><b>退出</b></a>]</div>
      <a href="https://www.biomart.net" target="_blank"><img
          src="https://assets.dxycdn.com/app/tong/images/admin/topBk.gif" style="border: medium none ;" /></a>
    </div>
    <div class="mid" id="mid" style="width:99%">
      <div class="left" id="leftMenus">
        <div class="aaa">控制面板</div>
        <ul>
          <!-- menu -->
          <li>
            <div><a href="https://www.biomart.net/admin/guessoneguess.do">管理控制台首页</a></div>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_stats')">运营数据分析</a></div>
            <ul id="menu_stats">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/index.do?action=OnlineUser">在线用户</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/stats/stats.do?action=SalesReport">销售业绩查询</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_adminMessage')">短信管理</a></div>
            <ul id="menu_adminMessage">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/message/manage.do?askType=on">站内所有短信</a></li>

              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/message/manage.do?action=SendMessages">群发短信</a></li>
            </ul>
          </li>

          <li>
            <div><a href="javascript:showControlls('menu_user')">用户管理</a></div>
            <ul id="menu_user">
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/user.do?roles=54">签约供应商</a>
              </li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/user.do?roles=55">VIP 签约供应商</a>
              </li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/user.do?roles=56">B-VIP
                  签约供应商</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/user.do?roles=54,55,56,93,94,95,96">付费机构会员</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/user.do?roles=7&by=i.addTime">免费机构会员</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/user.do?roles=8">普通个人会员</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/user.do?roles=5&dayNoLogin=14">两周未登入的金牌用户</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/user.do">所有会员</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/user/upgrade.do">升级申请</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/user/statTryApply.do?action=List">数据分析和微信托管申请</a>
              </li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/managerSimilar.do">商家相似信息组</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/processChangeName.do">处理商家改名申请</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/agencyLicense.do?action=ListCheckLicense">营业执照&医疗器械审核</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/product/cfdaLicense.do?action=List">医疗器械产品证书审核</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/auth.do">品牌认证审核</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/userManage/storeRating">商铺评分管理</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_service')">客服管理</a></div>
            <ul id="menu_service">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/customerService/onlineImManage/list">在线客服管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/service/service.do?action=ListItem">服务管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/service/service.do?action=ListGroup">服务套餐管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/service/service.do?action=ListInfo">服务单查询</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/service/service.do?action=SearchInfo">服务状态查询</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/customerService/storeReportManage/list">商铺诊断报告管理</a>
              </li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_adminInfo')">信息管理</a></div>
            <ul id="menu_adminInfo">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/info/businessManagement/list?pageSize=20&pageNo=1&businessType=1"
                  target="_blank">商机服务订单</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/info/infoSupply.do">供应信息</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/infoSupply/batchProcess.do">产品信息批量修改</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/promotion.do?title=&action_list=%E6%90%9C%E7%B4%A2">产品促销管理</a>
              </li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/info/infoDemand.do">求购信息</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/info/infoDemandSession.do">求购专场信息</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/dataUpload.do?action=DownloadStandardFile">批量上传表格下载</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/dataUpload.do">产品信息批量上传</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/info/productExport">产品信息导出</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/info/attachmentRank.do">资料下载排行</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/activity.do?action=ListA">活动信息管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/attachment/attachGetPathAction.do">文件上传地址获取</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/draw/lottery.do">抽奖信息管理</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/search/keyword/manager.do">产品热门搜索管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/info/keyword/experiment">实验热门搜索管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/indexpage.do?action=ViewCategory">首页分类弹窗管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/info/home/<USER>">首页运营位管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/checkOrder.do?action=orderList">订单管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/activityForMobileIndex?action=list">移动端首页活动专区</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/product/commentList.htm">评价管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/product/activityList.htm">活动产品搜索设置</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/info/emailRemind">客服销售信息管理（提醒邮件用）</a></li>
            </ul>
          </li>
          <li>
            <div><a href="https://www.biomart.net/admin/v2/index.htm#/webinars/liveManage">丁香学社管理</a></div>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_highQuality')">研选产品管理</a></div>
            <ul id="menu_highQuality">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/selection/merchantReview/list">研选商家审核</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/selection/goodsReview/list">研选产品审核</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/highQuality?action=list">线上产品管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/selection/banner/list" target="_blank">banner管理</a>
              </li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_adminInfo2')">试用-丁当铺管理</a></div>
            <ul id="menu_adminInfo2">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/info/infoTry.do?action=ListTryInfo">试用信息</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/tryDingDangStore/indexEdit">试用首页管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/tryDingDangStore/list">试用报告</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/info/infoTry.do?action=ListRecommendTryInfo">试用推荐管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/DDMarket.do?action=ListMallInfo">丁当商城信息</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/DDMarket.do?action=Notice">丁当铺通知</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/mall/exchengeOrder.do?action=ListMallOrder&supplierType=DMALL&productType=NORMAL">丁当铺订单列表</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/mall/exchengeOrder.do?action=ListMallOrder&supplierType=DUOBAO&desc=false">丁当夺宝列表</a>
              </li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/order.do">订单管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/order/autoGoods.do">自动发货商品管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/subscription.do?action=ListSubscription">订阅管理</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/settlement.do">财务审批</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/mall/promotion.do">促销工具</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/settlement.do?action=Commissions">分成管理</a></li>
            </ul>
          </li>

          <li>
            <div><a href="javascript:showControlls('menu_advertise')">广告管理</a></div>
            <ul id="menu_advertise">
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/advert.do?advertType=52">头部广告位
                  - 产品信息</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/advert.do?advertType=53">头部广告位
                  - 业内资讯</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/advert.do?advertType=54">头部广告位
                  - 实验方法</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/advert.do?advertType=2">分类广告位 -
                  产品信息</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/advert.do?advertType=3">分类广告位 -
                  业内资讯</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/advert.do?advertType=4">分类广告位 -
                  实验方法</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/advert.do?advertType=63">文章关联广告
                  - 业内资讯</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/advert.do?advertType=64">文章关联广告
                  - 实验方法</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/advert.do?advertType=70">商家广告 -
                  头部</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/advert.do?advertType=80">商家广告 -
                  右侧</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/advert/pos.do?action=ListPosition">广告基础位置信息</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/askPrice/assign.do?action=vipList">最新商机-增值服务</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_adkeyword')">关键字管理</a></div>
            <ul id="menu_adkeyword">
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/adkeyword/list.do">竞价列表</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/adkeyword/adkeyword.do?action=Put">投放</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/adkeyword/adkeyword.do?action=QueryKeyword">竞价查询</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/hotkeyword/hotKeyword.do">热门关键字管理</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_article')">文章管理</a></div>
            <ul id="menu_article">
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/experiment.do">实验技术方法－列表</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/experimentSnapshot/index.do?#/protocol_add">实验技术方法－新增</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/experiment.do?action=findExperiment">查询实验方法－(NEW)</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/experiment.do?action=EditExperiment">新增实验方法－(NEW)</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/experiment/category.do?action=ArticleCategory">实验技术方法－分类管理</a>
              </li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/news.do">业内资讯－列表</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/news.do?action=EditArticle">业内资讯－新增</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/news/category.do?action=ArticleCategory">业内资讯－分类管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/batchimport/importNewsArticle.do">业内资讯批量上传</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/batchimport/importExpArticle.do">实验技术方法批量上传</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_dajia')">用户服务中心管理</a></div>
            <ul id="menu_dajia">
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/dajia.do">打假曝光文章－列表</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/dajia.do?action=EditArticle">打假曝光文章－新增</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/dajia/category.do?action=ArticleCategory">打假曝光－分类管理</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_pages');">页面管理</a></div>
            <ul id="menu_pages">
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/pages/page.do">静态页面</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/pages/topic.do">专题页面</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/special.do">运营专题搭建</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_block')">前台推荐管理</a></div>
            <ul id="menu_block">
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/submit.do">推荐论坛管理</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/recbbs.do">首页促销推荐帖</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_mail')">邮件管理</a></div>
            <ul id="menu_mail">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/mail/manage.do?action=UserMail">用户订阅管理</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/mail/manage.do">SMTP邮件管理</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/mail/manage.do?action=Sended">邮件已发送管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/mail/manage.do?action=SendMany">群发邮件</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/mail/manage.do?action=TestSendMany">群发邮件测试</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_seo')">SEO管理</a></div>
            <ul id="menu_seo">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/friendLink/friendLink.do">友情链接列表</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/friendLink/friendLink.do?action=AddFriendLink">新增友情链接</a></li>
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/seo/site/push.do">百度主动推送链接</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/seo/keyword.do?action=List">seo产品词库管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/v2/index.htm#/seo/experiment/keyword">seo实验词库管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/seo/category/recommend.do?action=List">分类列表页推荐词管理</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_comment_complaint')">评论反馈管理</a></div>
            <ul id="menu_comment_complaint">
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/comment/comment.do">评论管理</a>
              </li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/complaint/complaint.do">反馈管理</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_permis')">权限管理</a></div>
            <ul id="menu_permis">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/security/user.do?action=Search">用户权限管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/security/role.do?action=List">角色管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/security/permission.do?action=List">权限管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/security/resource.do?action=List">资源管理</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_type')">分类管理</a></div>
            <ul id="menu_type">
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/infoCategory.do">产品信息分类编辑</a>
              </li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_schedule')">定时任务</a></div>
            <ul id="menu_schedule">
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/schedule.do">手动执行定时任务</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/spider/articleCrawSource.do">抓取源</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/spider/articleCrawSchedule.do">抓取任务</a></li>
            </ul>
          </li>

          <li>
            <div><a href="javascript:showControlls('menu_craw')">丁香通蜘蛛</a></div>
            <ul id="menu_craw">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/spider/articleCrawSource.do">文章抓取</a></li>
            </ul>
          </li>

          <li>
            <div><a href="javascript:showControlls('menu_form')">表单管理</a></div>
            <ul id="menu_form">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/form/formRecord.do?action=FormInfoList">所有表单信息</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/form/formRecord.do?action=AddFormInfo">增加表单信息</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/form/formRecord.do?action=FormRecordList&formName=services_contact">服务报价表单</a>
              </li>
            </ul>
          </li>


          <li>
            <div><a href="javascript:showControlls('menu_agent')">代理商管理</a></div>
            <ul id="menu_agent">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/member.do?action=ListBrand">所有品牌商信息</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/member.do?action=AddBrand">增加品牌商信息</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/member.do?action=ListAgency">所有代理商信息</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/member.do?action=AddAgency">增加代理商信息</a></li>
            </ul>
          </li>

          <li>
            <div><a href="javascript:showControlls('menu_sensitive')">敏感词管理</a></div>
            <ul id="menu_sensitive">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitiveKeywords?action=list">敏感词分级管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitive.do?action=SetSensitive">全站敏感词管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitive.do?action=SetSensitiveInfoSupply">产品信息敏感词管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitive.do?action=SetSensitiveArticle">文章内容敏感词管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitive.do?action=SetSensitiveInfoDemand">求购信息敏感词管理</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitive.do?searchType=1&searchState=0">敏感记录管理—业内资讯</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitive.do?searchType=2&searchState=0">敏感记录管理—实验方法</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitive.do?searchType=3&searchState=0">敏感记录管理—产品信息</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitive.do?searchType=11&searchState=3">一级敏感记录—业内资讯</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitive.do?searchType=12&searchState=3">一级敏感记录—实验方法</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitive.do?searchType=13&searchState=3">一级敏感记录—产品信息</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/sensitive.do?searchType=14&searchState=3">一级敏感记录—产品评论</a></li>
            </ul>
          </li>

          <li>
            <div><a href="javascript:showControlls('menu_others')">其他管理</a></div>
            <ul id="menu_others">
              <li name="menu" class="noUnderLine"><a href="https://www.biomart.net/admin/setting.do">系统参数设置</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/setting.do?action=List">其他参数设置</a></li>
              <li class="noUnderLine"><a href="javascript:" onclick="updateJsVersion();">刷新时间戳</a></li>
            </ul>
          </li>

          <li>
            <div><a href="javascript:showControlls('menu_tenyears')">十周年活动</a></div>
            <ul id="menu_tenyears">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/tenyears/bless.do?action=List">所有祝福列表</a></li>
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/tenyears/bless.do?action=ListForbiddenUser">禁言用户列表</a></li>
            </ul>
          </li>
          <li>
            <div><a href="javascript:showControlls('menu_experiment_snapshot')">丁香实验</a></div>
            <ul id="menu_experiment_snapshot">
              <li name="menu" class="noUnderLine"><a
                  href="https://www.biomart.net/admin/experimentSnapshot/index.do">实验快照管理</a></li>
            </ul>
          </li>
        </ul>

      </div>
      <script>
        superOnload();

        function initSupplyQuery(self) {
          var str_href = self.href;
          var now = new Date();
          var to = now.lastDays(7);
          str_href =
            "https://www.biomart.net/admin/info/infoSupply.do?by=createDate&order=desc,infoStatus asc&infoStatus=1&from_date=" +
            to[0].toLocaleString() + "&to_date=" + now.formatString2();
          window.location = str_href;
        }

        function updateJsVersion() {
          if (confirm("确认更新时间戳吗?")) {
            jQuery.get("https://www.biomart.net/admin/set.do?action=UpdateJsVersion", function (data) {
              if (data && data.message)
                alert(data.message);
            });
          }
        }
      </script>

      <script>
        function setMenu() {
          var list = document.getElementsByName("menu");
          var num = 0;
          for (var i = 0; i < list.length; i++) {
            var li_a = list[i].getElementsByTagName("a");
            if (li_a.length < 1) {
              continue;
            }
            var url = li_a[0].href;
            if (url) {
              url = url.replace("https://www.biomart.net", "");
              if (url.indexOf("?") != -1) {
                url = url.substr(0, url.indexOf("?"));
              }
              if (
                "/admin/message/manage.do;/admin/news/publish.do;/admin/experimentSnapshot/;/admin/batchimport/importExpArticle.do;/admin/settlement.do;/user/attachment.do;/user/info/batchInfo.do;/admin/biomart/order/get;/i/.do;/admin/setting.do;/admin/subscription.do;/usercenter/shopsnewsmanage.do;/admin/operation.do;/user/message/manage.do;/usercenter/index.do;/dwr/call/plaincall/AttachmentUtils.deleteAttachment.dwr;/admin/v2/index.htm#/tryDingDangStore/indexEdit;/admin/mail/manage.do;/usercenter/shopstemplate.do;/admin/advert/detail.do;/usercenter/productsinglerelease.do;/admin/v2/index.htm#/webinars/topicEdit;/admin/biomart/order/stat;/admin/v2/index.htm#/webinars/liveManage;/admin/v2/index.htm#/info/businessManagement/list;/admin/security/user.do;/user/mail.do;/mobile/mall.do;/admin/pharma/supply.do;/usercenter/latestbusinessinfo.do;/admin/info/attachmentRank.do;/admin/pages/page.do;/user/complaint/;/admin/v2/index.htm#/selection/goodsReview/detail;/admin/agencyLicense.do;/admin/info/infoSupply.do;/admin/promotion.do;/admin/recommend/manage.do;/msg/.do;/admin/stats/stats.do;/admin/pharma/resource.do;/user/info/infoSupply.do;/admin/spider/icraw.do;/admin/releaseExperimentSnapshot;/admin/infoUpload.do;/admin/v2/index.htm;/admin/order.do;/user/manager/baseConfig.do;/admin/v2/index.htm#/customerService/onlineImManage/list;/admin/v2/index.htm#/customerService/storeReportManage/edit;/my//.htm;/admin/security/resource.do;/admin/indexpage.do;/user/recommend.do;/user/info/infoTransfer.do;/admin/schedule.do;/admin/seo/site/push.do;/admin/mall/exchengeOrder.do;/admin/v2/index.htm#/userManage/storeRating;/usercenter/trypromotion.do;/admin/draw/lottery.do;/admin/complaint/complaint.do;/admin/agencyTmp.do;/admin/cate/category.do;/admin/v2/index.htm#/tryDingDangStore/list;/user/info/infoInviteBusiness.do;/admin/news.do;/admin/seo/keyword.do;/admin/batchInsert/batchInsert.do;/admin/processChangeName.do;/admin/experiment.do;/admin/spider/ecraw.do;/admin/v2/index.htm#/webinars/ApplyManage;/usercenter/companyinfo.dotype=honor;/admin/experimentFeedback/;/admin/user.do;/admin/userCredit.do;/my/order/.do;/admin/special.do;/dwr/call/plaincall/AttachmentUtils.setTitlePic.dwr;/admin/v2/index.htm#/customerService/storeReportManage/preview;/admin/highQuality;/admin/index.do;/admin/infoCategory.do;/usercenter/interactivemarketing.do;/admin/product/cfdaLicense.do;/admin/v2/index.htm#/info/businessManagement/detail;/admin/cache.do;/admin/sensitiveKeywords;/admin/sensitive.do;/admin/security/permission.do;/usercenter/.do;/usercenter/productcustomcategory.do;/admin/infoSupply/batchProcess.do;/usercenter/upgrade.do;/admin/adkeyword/list.do;/admin/v2/index.htm#/webinars/topicManage;/admin/activityForMobileIndex;/admin/v2/index.htm#/info/productExport;/admin/resource/res.do;/admin/info/infoTransfer.do;/admin/hotkeyword/hotKeyword.do;/admin/v2/index.htm#/selection/merchantReview/detail;/admin/score.do;/admin/dajia/publish.do;/admin/pharma/homepage.do;/admin/user/upgrade.do;/admin/infoSupply.do;/admin/experimentSnapshot/index.do;/admin/japi/try/report/list;/admin/japi/try/report/detail;/admin/v2/index.htm#/seo/experiment/keyword;/admin/recbbs.do;/admin/DDMarket.do;/admin/dajia.do;/admin/v2/index.htm#/customerService/storeReportManage/list;/api/login.css;/my/mytrial.do;/admin/product/activityList.htm;/admin/info/infoTry.do;/usercenter/infosubscription.do;/user/info/infoAgency.do;/admin/set.do;/admin/friendLink/friendLink.do;/admin/info/infoAgency.do;/admin/attachment/attachGetPathAction.do;/message/ask.do;/admin/comment/comment.do;/admin/experiment/category.do;/admin/checkOrder.do;/admin/news/category.do;/admin/pharma/links.do;/usercenter/servicehall.do;/admin/mall/promotion.do;/admin/experimentAnswer/;/admin/dajia/category.do;/admin/v2/index.htm#/info/home/<USER>/admin/v2/index.htm#/webinars/topicConfiguration;/admin/order/autoGoods.do;/user/drugdb.do;/admin/spider/crawLog.do;/usercenter/shopshomepageproductrecommend.do;/admin/v2/index.htm#/selection/goodsReview/list;/payment.do;/user/favorite.do;/admin/v2/index.htm#/info/emailRemind;/login.js1;/admin/experimentMethodSnapshot/;/user/index.do;/admin/info/infoDemandSession.do;/user/info/infoDemand.do;/admin/trademark.do;/admin/v2/index.htm#/webinars/EditLive;/admin/info/infoCooperation.do;/usercenter/shopspicmanage.do;/admin/activity.do;/admin/guessoneguess.do;/admin/askPrice/assign.do;/admin/experimentQuestion/;/admin/experiment/delete.do;/admin/form/formRecord.do;/admin/ddmarket/info.do;/admin/product/commentList.htm;/usercenter/companyinfo.do;/i/i.do;/user/manager/agency_web.do;/admin/info/infoDemand.do;/dwr/call/plaincall/AttachmentUtils.changeDescription.dwr;/admin/auth.do;/admin/pharma/news.do;/usercenter/shopsdocumentationmanage.do;/admin/v2/index.htm#/agency/update;/loginback.do;/user/comment/;/admin/experiment/publish.do;/admin/submit.do;/admin/seo/category/recommend.do;/admin/dajia/delete.do;/admin/task.do;/admin/tenyears/bless.do;/admin/info/infoInviteBusiness.do;/admin/v2/index.htm#/selection/banner/list;/admin/order/data.do;/login.js0;/admin/dataUpload.do;/admin/security/role.do;/draw.do;/admin/search/keyword/manager.do;/admin/pages/topic.do;/admin/hotkeyword/searchKeyword.do;/admin/common/uploadFile;/admin/v2/index.htm#/biomartResearch/order/list;/user/info/infoCooperation.do;/user/manager/article.do;/usercenter/v2/index.htm;/usercenter/prodcutbatchrelease.do;/admin/news/delete.do;/admin/member.do;/admin/v2/index.htm#/webinars/topicRegistrationReview;/dwr/call/plaincall/messageutils.deleteallmessages.dwr;/admin/v2/index.htm#/info/keyword/experiment;/admin/v2/index.htm#/webinars/videoCheck;/admin/v2/index.htm#/webinars/HomeManage;/admin/advert.do;/admin/v2/index.htm#/selection/merchantReview/list;/admin/storeUpload.do;/admin/data/;/my/.do;/admin/user/statTryApply.do;/admin/biomart/order/list;/user/info/infoBase.do;/admin/v2/index.htm#/customerService/storeReportManage/add;/usercenter/.htm;/admin/adkeyword/adkeyword.do;/user/manager/agency_info.do;/user/manager/user.do;/admin/biomart/order/changeState;/admin/product/summarySetting.do;/admin/batchimport/importNewsArticle.do;/admin/security/;/usercenter/productmanage.do;/login.do;/usercenter/shopsinfo.do;/admin/service/service.do;/admin/advert/pos.do;"
                .indexOf(url) != -1) {
                list[i].style.display = "block";
              } else {
                list[i].parentNode.removeChild(list[i]);
                i--;
              }
            }
          }

          var ul_list = document.getElementById("leftMenus").getElementsByTagName("ul");
          for (var k = 0; k < ul_list.length; k++) {
            var li_list = ul_list[k].getElementsByTagName("li");
            if (li_list.length < 1) {
              ul_list[k].parentNode.parentNode.removeChild(ul_list[k].parentNode);
              k--;
            }
          }

        }
        setMenu();
      </script>

      <div class="right" id="rightContent">

        <script src='https://www.biomart.net/dwr/interface/CataUtils.js'></script>
        <script type='text/javascript' src='https://assets.dxycdn.com/app/tong/js/cata.js'></script>
        <div id="complaintDiv" class="complaint"></div>
        <!-- div02 -->
        <div class="div02">
          <div class="addr"><span class="l">试用"试剂002" 申请用户信息 <b class="red">1</b>条信息</span><span class="r">您当前的位置：试用信息管理
              > 所有申请用户信息</span></div>
          <div class="hackbox"></div>
          <div class="yxbt">
            <a href="https://www.biomart.net/admin/info/infoTry.do?action=ListTryInfo">返回试用列表</a>
          </div>

          <form action="?" method="post" name="infolist" id="infolist" enctype="multipart/form-data">




            <div id="checkother" class="ts" style="display:none;margin-bottom:-15px;">您可以选中信息后进行刷新、删除、发布、撤销等操作。</div>
            <input type="hidden" id="check_others" name="check_others" />
            <table cellpadding="1" cellspacing="1" bgcolor="#dddddd" class="tbl" width="100%" id="tbl_info_list">
              <tr>
                <th width="9%">真实姓名</th>
                <th width="9%">用户ID</th>
                <th width="9%">订货总量</th>
                <th width="9%">所属单位</th>
                <th width="9%">邮箱</th>
                <th width="9%">电话</th>
                <th width="9%">所 在 地</th>
                <th width="9%">邮寄地址</th>
                <th width="9%">邮编</th>
                <th width="9%">补充说明</th>
                <th width="9%">申请时间</th>
                <th width="9%">发货状态</th>
              </tr>
              <tr onmouseover="upChange(this);" onmouseout="outChange(this);" onclick="checkInfo(this);">
                <td bgcolor="#ffffff" class="f11" align="center">甘女士</td>
                <td bgcolor="#ffffff" class="f11" align="center">dxy_7f36uxs6</td>
                <td bgcolor="#ffffff" class="f11" align="center">1</td>
                <td bgcolor="#ffffff" class="f11" align="center" title="gqy的商铺">gqy的商铺</td>
                <td bgcolor="#ffffff" class="f11" align="center"><EMAIL></td>
                <td bgcolor="#ffffff" class="f11" align="center"> / 13745678922</td>
                <td bgcolor="#ffffff" class="f11" align="center">国内</td>
                <td bgcolor="#ffffff" class="f11" align="center">上峰电商园</td>
                <td bgcolor="#ffffff" class="f11" align="center"></td>
                <td bgcolor="#ffffff" class="f11" align="center" title="">测试需要啊啊</td>
                <td bgcolor="#ffffff" class="f11" align="center">2020-12-01 14:59</td>
                <td bgcolor="#ffffff" class="f11" width="100" align="center" data-reason='你猜'>已取消 <span
                    class="link">查看理由</span>
                </td>
              </tr>
            </table>
            <style>
              .link {
                cursor: pointer;
                color: #169BD5;
              }

              .hide {
                display: none !important;
              }

              .dialog-reason {
                position: fixed;
                width: 100%;
                height: 100%;
                left: 0;
                top: 0;
              }

              .dialog-reason .dialog-content {
                width: 200px;
                height: 100px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                box-shadow: 0 0 1px 1px rgba(0, 0, 0, .3);
                background-color: #fff;
              }

              .dialog-reason .dialog-content .dialog-header {
                height: 30px;
                line-height: 30px;
                font-size: 14px;
                font-weight: bolder;
                position: relative;
                color: #000;
                padding: 10px;
              }

              .dialog-reason .dialog-content .dialog-header .close-btn {
                position: absolute;
                right: 10px;
                top: 6px;
                font-size: 14px;
                color: #000;
                font-weight: bolder;
                cursor: pointer;
              }

              .dialog-reason .dialog-content .reason-content {
                padding: 10px;
                font-size: 12px;
                color: #666;
              }
            </style>
            <div class="dialog-reason hide">
              <div class="dialog-content">
                <div class="dialog-header">理由<div class="close-btn">X</div>
                </div>
                <div class="reason-content">用户不不选</div>
              </div>
            </div>
            <script>
              $(function () {
                $('#tbl_info_list .link').on('click', function () {
                  var reason = $(this).parent().attr('data-reason')
                  $('.dialog-reason .reason-content').html(reason)
                  $('.dialog-reason').removeClass('hide')
                })
                $('.dialog-reason .close-btn').on('click', function () {
                  $('.dialog-reason').addClass('hide')
                })
              })
            </script>
            <div class="page" id="page">
            </div>
          </form>

          <script src='https://www.biomart.net/dwr/interface/CategoryUtils.js'></script>
          <script src='https://www.biomart.net/dwr/interface/InfoUtils.js'></script>
          <script src="https://assets.dxycdn.com/app/tong/js/info.js?t=1607590596101"></script>
          <script src="https://www.biomart.net/supply/categorycj.do"></script>
          <script type='text/javascript' src='https://assets.dxycdn.com/app/tong/js/scriptaculous/dragdrop.js'></script>
          <script>
            var isPerson = false;
            var select_params = {
              elementId: "zhuzhi",
              limit: 1,
              cateId: "",
              size: 1,
              isMulti: false,
              valueElementId: "category",
              showElementId: "",
              allFirst: true,
              code: ""
            };
            initSelects(select_params);

            function recommend(sel) {
              try {
                var tr = sel.parentNode.parentNode;
                tr.onclick = function () {
                  window.setTimeout(function () {
                    checkInfo(tr);
                  }, 100)
                }
                var checkbox = tr.cells[0].childNodes[0];
                checkbox.checked = true;
              } catch (e) {
                var tr = sel.parentElement.parentElement;
                tr.onclick = function () {
                  window.setTimeout(function () {
                    checkInfo(tr);
                  }, 100)
                }
                var checkbox = tr.cells[0].children[0];
                checkbox.checked = true;
              }
              return true;
            }

            function changeCustomCate(sel) {
              if (sel.value.length == 0) {
                return false;
              }
              if (isSelected("改变自定义类型")) {
                var form = document.getElementById("infolist");
                form.action += "action=ChangeCustomCate";
                form.submit();
              } else {
                sel.options[0].selected = true;
              }
            }

            function checkAllOnePage(check) {
              var check = document.getElementById("selectAll");
              var all = document.getElementsByTagName("input");
              if (check.checked == true) {
                for (var i = 0; i < all.length; i++) {
                  if (all[i].type == "checkbox" && all[i].name == "id") {
                    all[i].checked = true;
                  }
                }
                var trs = document.getElementsByTagName("TR");
                for (i = 0; i < trs.length; i++) {
                  for (j = 0; j < trs[i].cells.length; j++) {
                    if (trs[i].cells[j].className != "unColor") {
                      trs[i].cells[j].style.backgroundColor = onColor;
                    }
                  }
                }
                if (trim(document.getElementById("page").innerHTML).length > 0) {
                  checkOtherAll(1);
                }
              } else {
                for (var i = 0; i < all.length; i++) {
                  if (all[i].type == "checkbox" && all[i].name == "id") {
                    all[i].checked = false;
                  }
                }
                var trs = document.getElementsByTagName("TR");
                for (i = 0; i < trs.length; i++) {
                  for (j = 0; j < trs[i].cells.length; j++) {
                    if (trs[i].cells[j].className != "unColor") {
                      trs[i].cells[j].style.backgroundColor = upColor;
                    }
                  }
                }
                checkOtherAll(3);
              }
            }

            function checkOtherAll(check) {
              var co = document.getElementById("checkother");
              if (check == 1) {
                co.style.display = "block";
                co.innerHTML =
                  '此页中的所有 15 会话都已选中。<span style="text-decoration:underline;cursor:pointer;" onclick="return checkOtherAll(2)">选择 信息</span> 中的所有 1 条记录';
                co.style.backgroundColor = "#ffffcc";
              } else if (check == 2) {
                co.style.display = "block";
                co.innerHTML =
                  '已选中 信息 中的所有1条记录。<span style="text-decoration:underline;cursor:pointer;" onclick="return checkOtherAll(3)">清除选中</span>';
                co.style.backgroundColor = "#ffff80";
                document.getElementById("check_others").value = "all";
              } else if (check == 3) {
                co.style.display = "none";
                var check = document.getElementById("selectAll");
                check.checked = false;
                checkAll(check);
                co.innerHTML = "您可以选中信息后进行刷新、删除、发布、撤销等操作。";
                co.style.backgroundColor = "#ffffcc";
                document.getElementById("check_others").value = "";
              }
            }

            function refreshInfo() {
              var num_infos = 0;
              if ($("check_others").value == "all") {
                num_infos = 1;
              } else {
                num_infos = getSelectedInfoNum();
              }
              var score = 0;
              var num_score = num_infos % 5 == 0 ? parseInt(num_infos / 5) : (parseInt(num_infos / 5) + 1);
              var curr_score = "当前您的帐户余额为：" + score + "点。\n";
              var curr_infos = "您将刷新" + num_infos + "条信息（5条/点）。\n"
              var curr_sum = "需要消费点数为：" + num_score + "点。\n";
              if (num_score > score) {
                if (score == 0) {
                  alert(curr_score + curr_infos + curr_sum + "您的余额不足,无法完成本次操作！请申请充值！");
                } else {
                  alert(curr_score + curr_infos + curr_sum + "您的余额不足,无法完成本次操作！请修改需要刷新的条目数（最多不超过" + score * 10 +
                    "条），或申请充值");
                }
                return false;
              } else {
                var cons = "确定该操作吗 ?";
                if (confirm(curr_score + curr_infos + curr_sum + cons)) {
                  $("infolist").submit();
                  return true;
                } else {
                  return false;
                }
              }
              return false;
            }

            function getSelectedInfoNum() {
              var num = 0;
              var infos = document.getElementsByTagName("input");
              for (i = 0; i < infos.length; i++) {
                if (infos[i].type == "checkbox" && infos[i].checked && infos[i].name == "id") {
                  num++;
                }
              }
              return num;
            }
            var checkother_innerHTML;

            function showRefreshTitle(show) {
              if (show) {
                checkother_innerHTML = g("checkother").innerHTML;
                g("checkother").innerHTML = "刷新信息将使您的信息在展示时处于分类列表靠前的位置。";
              } else {
                g("checkother").innerHTML = checkother_innerHTML;
              }
            }

            function showTitlePic(id, path) {
              var divd = g(id + "_titlePic");
              divd.style.display = "block";
              if (divd.childNodes[0].src.indexOf("loadding.gif") != -1) {
                divd.innerHTML = "";
                var img = document.createElement("IMG");
                if (getOs() == 1) {
                  img.style.display = "none";
                }
                img.onload = function () {
                  autozoom(img, 200, true);
                  if (getOs() == 1) {
                    img.style.display = "inline";
                  }
                }
                divd.appendChild(img);
                img.src = path;
                img.onerror = function () {
                  img.src = img.src.replace("_medium.", ".");
                  autozoom(img, 200, true);
                }
                autozoom(img, 200, true);
              }
            }

            function checkRecommendInfos() {
              try {
                var table = document.getElementsByTagName("table")[0];
                var ipts = table.getElementsByTagName("input");
                var rnum = 0;
                for (var i = 0; i < ipts.length; i++) {
                  var ipt = ipts[i];
                  if (ipt.checked && ipt.id != "selectAll") {
                    rnum++;
                    if (ipt.parentNode.parentNode.innerHTML.indexOf("已发布") == -1) {
                      alert("您选中的信息中有未发布的信息，未发布的信息不能被推荐。");
                      return false;
                    }
                  }
                }
              } catch (e) {
                return false;
              }
              return true;
            }
          </script>
          <script>
            var changePriceIds = [];

            function setAlipay(sel) {
              var table = g("tbl_info_list");
              var ids = [];
              var ipts = table.getElementsByTagName("input");
              for (var i = 0; i < ipts.length; i++) {
                var ipt = ipts[i];
                if ((ipt.type == "checkbox" || ipt.type == "CHECKBOX") && ipt.checked) {
                  if (ipt.value != null && ipt.value.length > 0 && ipt.value != "on")
                    ids.push(ipt.value);
                }
              }
              if (ids.length != 0) {
                var panel = g("modifypricepanel");
                document.body.appendChild(panel);
                panel.style.left = "50%";
                panel.style.top = "50%";
                panel.style.display = "block";
                changePriceIds = ids;
              } else {
                alert("请选择信息进行可支付操作！");
                return false;
              }
              showPanel("block");
            }

            function k_changePrice(evt, sel) {
              evt = (evt) ? evt : ((window.event) ? event : null);
              if (evt) {
                if (evt.keyCode == 13) {
                  var ipt = g("modifypricepanel").getElementsByTagName("input")[2];
                  clickObj(ipt);
                  return false;
                } else {
                  return true;
                }
              }
            }
          </script>
        </div>
        <div class="hackbox"></div>
        <!-- /div02 -->


      </div>
    </div>
    <div class="tail">Copyright &copy;2008-2020 DXY all rights resserved.</div>
  </div>
  <input type="hidden" id="assets-cache-timestamp" value="1607590596101" />
  <script type="application/javascript">
    $('#leftMenus >ul >li >div').on('click', function () {
      $(this).parent().find('>ul').toggle()
    })
  </script>
</body>

</html>