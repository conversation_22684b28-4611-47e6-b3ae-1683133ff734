/**
 * @license Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.html or //ckeditor.com/license
 */

CKEDITOR.editorConfig = function( config ) {
    // Define changes to default configuration here. For example:
    config.language = 'zh-cn';
    config.filebrowserImageBrowseUrl = config.BasePath+ '/ckeditor/editor/filemanager/connectors/?type=Image';
    config.filebrowserImageUploadUrl = config.BasePath+ '/ckeditor/editor/filemanager/connectors/?Type=Image' ;
    config.filebrowserFlashBrowseUrl = config.BasePath+ '/ckeditor/editor/filemanager/connectors/?type=Flash';
    config.filebrowserFlashUploadUrl = config.BasePath+ '/ckeditor/editor/filemanager/connectors/?Type=Flash' ;
    config.filebrowserOfficeBrowseUrl = config.BasePath+ '/officeConvert/connectors/upload/?Type=Office' ;
    config.filebrowserFileBrowseUrl = config.BasePath+ '/ckeditor/editor/filemanager/connectors/?type=File' ;
    config.filebrowserFileUploadUrl = config.BasePath+ '/ckeditor/editor/filemanager/connectors/?Type=File' ;
//	config.extraPlugins="officeimport,fileimport";
    //设置初始化焦点
    config.startupFocus = false;
    //定制图片上传的Tab
    config.removeDialogTabs = 'image:advanced;link:advanced;';
    config.linkShowTargetTab = false;

    //不过滤
    config.allowedContent = true;

    //自定义字体
    config.font_names ='Arial/Arial, Helvetica, sans-serif;Comic Sans MS/Comic Sans MS, cursive;宋体; 微软雅黑; 黑体;Courier New/Courier New, Courier, monospace;Georgia/Georgia, serif;Lucida Sans Unicode/Lucida Sans Unicode, Lucida Grande, sans-serif;Tahoma/Tahoma, Geneva, sans-serif;Times New Roman/Times New Roman, Times, serif;Trebuchet MS/Trebuchet MS, Helvetica, sans-serif;Verdana/Verdana, Geneva, sans-serif;'
};
CKEDITOR.config.toolbar_Tong_ADMINNEW=[['Subscript','Superscript']];
CKEDITOR.config.toolbar_Tong_USER=[['Source','Styles','Format','Font','FontSize'],['TextColor','BGColor'],['Link','Unlink'],'/',['Bold','Italic','Underline','Strike','Subscript','Superscript'],['NumberedList','BulletedList'],['JustifyLeft','JustifyCenter','JustifyRight','JustifyBlock'],['Image',/*'Flash',*/'Table','-','PasteFromWord','Preview'],['Maximize']];
CKEDITOR.config.toolbar_Tong_ADMIN=[['Source','Font','FontSize','Subscript','Superscript'],['TextColor','BGColor'],['Link','Unlink','-','PasteFromWord','Preview','Flash','Table'],'/',['Bold','Italic','Underline','Strike'],['NumberedList','BulletedList'],['JustifyLeft','JustifyCenter','JustifyRight','JustifyBlock'],['-','Image']];
CKEDITOR.config.enterMode = CKEDITOR.ENTER_BR;//��ѡ��CKEDITOR.ENTER_BR��CKEDITOR.ENTER_DIV,CKEDITOR.ENTER_P
CKEDITOR.config.shiftEnterMode = CKEDITOR.ENTER_P;
CKEDITOR.on( 'dialogDefinition', function( ev )
{
// Take the dialog name and its definition from the event data.
    var dialogName = ev.data.name;
    var dialogDefinition = ev.data.definition;

// Check if the definition is from the dialog we're
// interested in (the 'image' dialog). This dialog name found using DevTools plugin
    if ( dialogName == 'image' )
    {
// Remove the 'Link' and 'Advanced' tabs from the 'Image' dialog.
// dialogDefinition.removeContents( 'link' );
// dialogDefinition.removeContents( 'advanced' );

// Get a reference to the 'Image Info' tab.
        var infoTab = dialogDefinition.getContents( 'info' );

// Remove unnecessary widgets/elements from the 'Image Info' tab.
// infoTab.remove( 'txtHSpace');
// infoTab.remove( 'txtVSpace');

        oldOnShow = dialogDefinition.onShow;
        dialogDefinition.onShow = function() {
            oldOnShow.apply(this, arguments);
            this.selectPage('Upload');
        };
    }
});
