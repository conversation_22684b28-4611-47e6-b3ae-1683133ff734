const setCookie = require('set-cookie-parser')

const target = 'https://www.biomart.net'
const proxyConf = {
  target,
  changeOrigin: true,
  secure: false,
  onProxyReq: (proxyReq, req) => {
    // 动态设置referer
    const referer = req.headers && req.headers.referer
   
    if (referer) {
      proxyReq.setHeader(
        'referer',
        referer.replace(/^http:\/\/(localhost|(\d+).(\d+).(\d+).(\d+)):(\d+)\//g, `${target}/`)
      )
    }

    if (req.body) {
      const bodyData = JSON.stringify(req.body)

      // incase if content-type is application/x-www-form-urlencoded -> we need to change to application/json
      proxyReq.setHeader('Content-Type', 'application/json')
      proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData))
      // stream the content
      proxyReq.write(bodyData)
      proxyReq.end()
    }
  },
  onProxyRes: proxyRes => {
    const setCookieArr = proxyRes.headers['set-cookie']

    if (setCookieArr) {
      const prlt = setCookie.parse(setCookieArr)
      const setCookies = prlt.map(({ name, value, expires, maxAge }) => {
        return `${name}=${value};Path=/;Expires=${new Date(
          expires
        ).toUTCString()};Max-Age=${maxAge}`
      })
      proxyRes.headers['set-cookie'] = setCookies
    }
  },
  headers: {
    origin: target,
    'user-agent':
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 Safari/537.36',
    accept: 'application/json',
    authority: 'www.biomart.net',
  },
  cookieDomainRewrite: {
    'www.biomart.net': '',
  },
}

module.exports = proxyConf
